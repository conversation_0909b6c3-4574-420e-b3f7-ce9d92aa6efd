"""Comprehensive System Integration Test for Advanced Agentic RAG."""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from main_integration import AdvancedAgenticRAGSystem

class SystemIntegrationTester:
    """Comprehensive tester for the Advanced Agentic RAG system"""
    
    def __init__(self):
        self.system = None
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
    
    async def run_all_tests(self):
        """Run all integration tests"""
        print("🧪 STARTING COMPREHENSIVE SYSTEM INTEGRATION TESTS")
        print("="*70)
        
        # Initialize system
        await self.test_system_initialization()
        
        # Test core functionality
        await self.test_basic_query_processing()
        await self.test_coordination_patterns()
        await self.test_multi_agent_collaboration()
        await self.test_knowledge_fusion()
        await self.test_memory_system()
        await self.test_tool_framework()
        await self.test_reasoning_systems()
        await self.test_critic_verification()
        
        # Test edge cases
        await self.test_error_handling()
        await self.test_performance()
        
        # Generate report
        await self.generate_test_report()
    
    async def test_system_initialization(self):
        """Test system initialization"""
        print("\n🔧 Testing System Initialization...")
        
        try:
            self.system = AdvancedAgenticRAGSystem()
            await self.system.initialize_system()
            
            # Verify initialization
            status = self.system.get_system_status()
            
            assert status["initialized"] == True, "System not properly initialized"
            assert status["agents_count"] > 0, "No agents registered"
            
            self.record_test("System Initialization", True, "System initialized successfully")
            print("✅ System initialization: PASSED")
            
        except Exception as e:
            self.record_test("System Initialization", False, str(e))
            print(f"❌ System initialization: FAILED - {str(e)}")
            raise
    
    async def test_basic_query_processing(self):
        """Test basic query processing"""
        print("\n📝 Testing Basic Query Processing...")
        
        test_cases = [
            {
                "name": "Simple HR Query",
                "query": "What is the leave policy for NuvoAi?",
                "organization": "NuvoAi",
                "department": "HR"
            },
            {
                "name": "Technical Query",
                "query": "How does Meril handle project management?",
                "organization": "Meril",
                "department": "Technical"
            },
            {
                "name": "General Query",
                "query": "Tell me about organizational policies",
                "organization": None,
                "department": None
            }
        ]
        
        for test_case in test_cases:
            try:
                result = await self.system.process_query(
                    test_case["query"],
                    test_case["organization"],
                    test_case["department"]
                )
                
                # Verify result structure
                assert "error" not in result, f"Query failed: {result.get('error')}"
                assert "system_metadata" in result, "Missing system metadata"
                assert result["system_metadata"]["execution_time"] > 0, "Invalid execution time"
                
                self.record_test(f"Basic Query: {test_case['name']}", True, "Query processed successfully")
                print(f"✅ {test_case['name']}: PASSED")
                
            except Exception as e:
                self.record_test(f"Basic Query: {test_case['name']}", False, str(e))
                print(f"❌ {test_case['name']}: FAILED - {str(e)}")
    
    async def test_coordination_patterns(self):
        """Test different coordination patterns"""
        print("\n🤝 Testing Coordination Patterns...")
        
        patterns = ["hierarchical", "collaborative", "specialist_consultation"]
        test_query = "What are the HR policies across organizations?"
        
        for pattern in patterns:
            try:
                result = await self.system.process_query(
                    test_query,
                    coordination_pattern=pattern
                )
                
                assert "error" not in result, f"Pattern {pattern} failed: {result.get('error')}"
                assert result["system_metadata"]["coordination_pattern"] == pattern
                
                self.record_test(f"Coordination Pattern: {pattern}", True, "Pattern executed successfully")
                print(f"✅ {pattern.title()}: PASSED")
                
            except Exception as e:
                self.record_test(f"Coordination Pattern: {pattern}", False, str(e))
                print(f"❌ {pattern.title()}: FAILED - {str(e)}")
    
    async def test_multi_agent_collaboration(self):
        """Test multi-agent collaboration"""
        print("\n👥 Testing Multi-Agent Collaboration...")
        
        try:
            result = await self.system.process_query(
                "Compare leave policies between NuvoAi and Meril organizations",
                coordination_pattern="collaborative"
            )
            
            assert "error" not in result, f"Collaboration failed: {result.get('error')}"
            
            # Check if multiple agents were involved
            agents_involved = result["system_metadata"].get("agents_involved", [])
            assert len(agents_involved) > 1, "Multi-agent collaboration not detected"
            
            self.record_test("Multi-Agent Collaboration", True, f"Involved {len(agents_involved)} agents")
            print(f"✅ Multi-Agent Collaboration: PASSED ({len(agents_involved)} agents)")
            
        except Exception as e:
            self.record_test("Multi-Agent Collaboration", False, str(e))
            print(f"❌ Multi-Agent Collaboration: FAILED - {str(e)}")
    
    async def test_knowledge_fusion(self):
        """Test knowledge fusion capabilities"""
        print("\n🧠 Testing Knowledge Fusion...")
        
        try:
            # Query that should trigger knowledge fusion
            result = await self.system.process_query(
                "What are all the different types of leave available across organizations?",
                coordination_pattern="collaborative"
            )
            
            assert "error" not in result, f"Knowledge fusion failed: {result.get('error')}"
            
            # Check for fusion indicators
            fusion_applied = result.get("knowledge_fusion_applied", False)
            
            self.record_test("Knowledge Fusion", True, f"Fusion applied: {fusion_applied}")
            print(f"✅ Knowledge Fusion: PASSED (Applied: {fusion_applied})")
            
        except Exception as e:
            self.record_test("Knowledge Fusion", False, str(e))
            print(f"❌ Knowledge Fusion: FAILED - {str(e)}")
    
    async def test_memory_system(self):
        """Test memory system functionality"""
        print("\n🧠 Testing Memory System...")
        
        try:
            # Get memory statistics
            status = self.system.get_system_status()
            memory_stats = status["memory_stats"]
            
            assert memory_stats["total_memories"] > 0, "No memories stored"
            assert "memory_by_type" in memory_stats, "Memory type breakdown missing"
            
            # Test memory retrieval through query
            result = await self.system.process_query(
                "What do you remember about NuvoAi policies?",
                organization="NuvoAi"
            )
            
            assert "error" not in result, "Memory-based query failed"
            
            self.record_test("Memory System", True, f"Total memories: {memory_stats['total_memories']}")
            print(f"✅ Memory System: PASSED ({memory_stats['total_memories']} memories)")
            
        except Exception as e:
            self.record_test("Memory System", False, str(e))
            print(f"❌ Memory System: FAILED - {str(e)}")
    
    async def test_tool_framework(self):
        """Test dynamic tool framework"""
        print("\n🔧 Testing Tool Framework...")
        
        try:
            status = self.system.get_system_status()
            tool_stats = status["tool_stats"]
            
            assert tool_stats["total_tools"] > 0, "No tools available"
            
            # Test tool recommendation
            tools = self.system.tool_framework.recommend_tools("calculate leave days", limit=3)
            
            self.record_test("Tool Framework", True, f"Available tools: {tool_stats['total_tools']}")
            print(f"✅ Tool Framework: PASSED ({tool_stats['total_tools']} tools, {len(tools)} recommended)")
            
        except Exception as e:
            self.record_test("Tool Framework", False, str(e))
            print(f"❌ Tool Framework: FAILED - {str(e)}")
    
    async def test_reasoning_systems(self):
        """Test reasoning systems (Tree of Thoughts, Self-Reflection)"""
        print("\n🤔 Testing Reasoning Systems...")
        
        try:
            # Query that should trigger complex reasoning
            result = await self.system.process_query(
                "If an employee at NuvoAi takes 15 days of leave, how many days do they have remaining?",
                organization="NuvoAi",
                department="HR"
            )
            
            assert "error" not in result, "Reasoning query failed"
            
            # Check for reasoning trace
            reasoning_trace = result.get("reasoning_trace", [])
            assert len(reasoning_trace) > 0, "No reasoning trace found"
            
            self.record_test("Reasoning Systems", True, f"Reasoning steps: {len(reasoning_trace)}")
            print(f"✅ Reasoning Systems: PASSED ({len(reasoning_trace)} reasoning steps)")
            
        except Exception as e:
            self.record_test("Reasoning Systems", False, str(e))
            print(f"❌ Reasoning Systems: FAILED - {str(e)}")
    
    async def test_critic_verification(self):
        """Test critic agent verification"""
        print("\n🔍 Testing Critic Verification...")
        
        try:
            # Process a query that should trigger verification
            result = await self.system.process_query(
                "What is the exact leave policy for NuvoAi employees?",
                organization="NuvoAi",
                department="HR"
            )
            
            assert "error" not in result, "Critic verification query failed"
            
            # Check for verification metadata
            verification_applied = "verification" in result
            
            self.record_test("Critic Verification", True, f"Verification applied: {verification_applied}")
            print(f"✅ Critic Verification: PASSED (Applied: {verification_applied})")
            
        except Exception as e:
            self.record_test("Critic Verification", False, str(e))
            print(f"❌ Critic Verification: FAILED - {str(e)}")
    
    async def test_error_handling(self):
        """Test error handling"""
        print("\n⚠️  Testing Error Handling...")
        
        error_cases = [
            {
                "name": "Empty Query",
                "query": "",
                "should_handle": True
            },
            {
                "name": "Invalid Organization",
                "query": "What is the policy?",
                "organization": "NonExistentOrg",
                "should_handle": True
            },
            {
                "name": "Invalid Coordination Pattern",
                "query": "Test query",
                "coordination_pattern": "invalid_pattern",
                "should_handle": True
            }
        ]
        
        for case in error_cases:
            try:
                result = await self.system.process_query(
                    case["query"],
                    organization=case.get("organization"),
                    coordination_pattern=case.get("coordination_pattern", "hierarchical")
                )
                
                # Should either handle gracefully or return error
                if case["should_handle"]:
                    # System should handle gracefully
                    handled_gracefully = "error" not in result or "system_metadata" in result
                    
                    self.record_test(f"Error Handling: {case['name']}", handled_gracefully, 
                                   "Handled gracefully" if handled_gracefully else "Not handled gracefully")
                    
                    status = "PASSED" if handled_gracefully else "FAILED"
                    print(f"{'✅' if handled_gracefully else '❌'} {case['name']}: {status}")
                
            except Exception as e:
                self.record_test(f"Error Handling: {case['name']}", False, str(e))
                print(f"❌ {case['name']}: FAILED - {str(e)}")
    
    async def test_performance(self):
        """Test system performance"""
        print("\n⚡ Testing Performance...")
        
        try:
            import time
            
            # Test response time
            start_time = time.time()
            result = await self.system.process_query(
                "Quick test query for performance",
                organization="NuvoAi"
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # Performance thresholds
            acceptable_time = 10.0  # 10 seconds
            performance_passed = response_time < acceptable_time
            
            self.record_test("Performance", performance_passed, 
                           f"Response time: {response_time:.2f}s (threshold: {acceptable_time}s)")
            
            status = "PASSED" if performance_passed else "FAILED"
            print(f"{'✅' if performance_passed else '❌'} Performance: {status} ({response_time:.2f}s)")
            
        except Exception as e:
            self.record_test("Performance", False, str(e))
            print(f"❌ Performance: FAILED - {str(e)}")
    
    def record_test(self, test_name: str, passed: bool, details: str):
        """Record test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
        
        self.test_results.append({
            "test_name": test_name,
            "passed": passed,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })
    
    async def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*70)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("="*70)
        
        success_rate = (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
        
        print(f"📈 Overall Success Rate: {success_rate:.1f}% ({self.passed_tests}/{self.total_tests})")
        print(f"✅ Passed Tests: {self.passed_tests}")
        print(f"❌ Failed Tests: {self.total_tests - self.passed_tests}")
        
        print("\n📋 Detailed Results:")
        print("-" * 70)
        
        for result in self.test_results:
            status = "✅ PASS" if result["passed"] else "❌ FAIL"
            print(f"{status} | {result['test_name']:<30} | {result['details']}")
        
        # System status summary
        if self.system:
            status = self.system.get_system_status()
            print(f"\n🔧 System Status:")
            print(f"   • Agents: {status['agents_count']}")
            print(f"   • Total Queries: {status['statistics']['total_queries']}")
            print(f"   • Success Rate: {status['statistics']['successful_responses']}/{status['statistics']['total_queries']}")
            print(f"   • Avg Response Time: {status['statistics']['average_response_time']:.2f}s")
            print(f"   • Memory Entries: {status['memory_stats']['total_memories']}")
            print(f"   • Available Tools: {status['tool_stats']['total_tools']}")
        
        # Save report to file
        report_data = {
            "summary": {
                "total_tests": self.total_tests,
                "passed_tests": self.passed_tests,
                "success_rate": success_rate,
                "timestamp": datetime.now().isoformat()
            },
            "detailed_results": self.test_results,
            "system_status": status if self.system else None
        }
        
        with open("test_report.json", "w") as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n💾 Detailed report saved to: test_report.json")
        print("="*70)
        
        # Final assessment
        if success_rate >= 80:
            print("🎉 SYSTEM INTEGRATION: EXCELLENT")
        elif success_rate >= 60:
            print("👍 SYSTEM INTEGRATION: GOOD")
        elif success_rate >= 40:
            print("⚠️  SYSTEM INTEGRATION: NEEDS IMPROVEMENT")
        else:
            print("🚨 SYSTEM INTEGRATION: CRITICAL ISSUES")

async def main():
    """Run the comprehensive integration test"""
    tester = SystemIntegrationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
