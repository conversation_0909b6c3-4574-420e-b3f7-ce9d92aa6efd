"""Main Integration Module for Advanced Agentic RAG System."""

import asyncio
import sys
import os
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(__file__))

# Import core components
from agents.communication.agent_coordinator import AgentCoordinator, CoordinationPattern
from ai.knowledge.fusion.knowledge_fusion_engine import KnowledgeFusionEngine
from ai.memory.integrated_memory_system import IntegratedMemorySystem, MemoryType
from ai.tools.dynamic_tool_framework import DynamicToolFramework

# Import agents
from agents.orchestrator.orchestrator_agent import OrchestratorAgent
from agents.specialized.reasoning_agent import ReasoningAgent
from agents.specialized.critic_agent import CriticAgent
from agents.organization.nuvoai_agent import NuvoAiAgent
from agents.organization.meril_agent import MerilAgent
from agents.department.hr_agent import HRAgent
from agents.department.technical_agent import TechnicalAgent

# Import AI components
from ai.reasoning.tree_of_thoughts.tree_of_thoughts import TreeOfThoughts
from ai.reasoning.self_reflection.self_reflection import SelfReflectionSystem

class AdvancedAgenticRAGSystem:
    """Main system integrating all Advanced Agentic RAG components"""
    
    def __init__(self):
        # Core systems
        self.agent_coordinator = AgentCoordinator()
        self.knowledge_fusion = KnowledgeFusionEngine()
        self.memory_system = IntegratedMemorySystem()
        self.tool_framework = DynamicToolFramework()
        
        # AI reasoning systems
        self.tree_of_thoughts = TreeOfThoughts()
        self.self_reflection = SelfReflectionSystem()
        
        # Agents
        self.agents = {}
        
        # System status
        self.is_initialized = False
        self.system_stats = {
            "total_queries": 0,
            "successful_responses": 0,
            "average_response_time": 0.0,
            "agent_utilization": {}
        }
    
    async def initialize_system(self):
        """Initialize the complete Advanced Agentic RAG system"""
        print("Initializing Advanced Agentic RAG System...")
        
        # Initialize core systems
        await self.setup_core_systems()
        
        # Initialize and register agents
        await self.setup_agents()
        
        # Setup integrations
        await self.setup_integrations()
        
        # Initialize knowledge base
        await self.initialize_knowledge_base()
        
        self.is_initialized = True
        print("✅ Advanced Agentic RAG System initialized successfully!")
        
        # Print system overview
        await self.print_system_overview()
    
    async def setup_core_systems(self):
        """Setup core system components"""
        # Link systems together
        self.agent_coordinator.set_knowledge_fusion(self.knowledge_fusion)
        self.agent_coordinator.set_memory_system(self.memory_system)
        
        print("✅ Core systems configured")
    
    async def setup_agents(self):
        """Initialize and register all agents"""
        # Create agents
        agents_to_create = [
            ("OrchestratorAgent", OrchestratorAgent),
            ("ReasoningAgent", ReasoningAgent),
            ("CriticAgent", CriticAgent),
            ("NuvoAiAgent", NuvoAiAgent),
            ("MerilAgent", MerilAgent),
            ("HRAgent", HRAgent),
            ("TechnicalAgent", TechnicalAgent)
        ]
        
        for agent_name, agent_class in agents_to_create:
            try:
                agent_instance = agent_class()
                self.agents[agent_name] = agent_instance
                self.agent_coordinator.register_agent(agent_name, agent_instance)
                print(f"✅ {agent_name} registered")
            except Exception as e:
                print(f"❌ Failed to create {agent_name}: {str(e)}")
        
        print(f"✅ {len(self.agents)} agents registered successfully")
    
    async def setup_integrations(self):
        """Setup integrations between components"""
        # Integrate reasoning systems with agents
        for agent_name, agent in self.agents.items():
            if hasattr(agent, 'set_reasoning_system'):
                agent.set_reasoning_system(self.tree_of_thoughts)
            
            if hasattr(agent, 'set_reflection_system'):
                agent.set_reflection_system(self.self_reflection)
            
            if hasattr(agent, 'set_tool_framework'):
                agent.set_tool_framework(self.tool_framework)
        
        print("✅ Agent integrations configured")
    
    async def initialize_knowledge_base(self):
        """Initialize knowledge base with sample data"""
        # Store sample organizational knowledge
        sample_knowledge = [
            {
                "organization": "NuvoAi",
                "department": "HR",
                "content": "NuvoAi provides 30 days of privilege leave annually to all employees.",
                "type": "policy"
            },
            {
                "organization": "NuvoAi", 
                "department": "HR",
                "content": "Maternity leave at NuvoAi is 26 weeks with full pay.",
                "type": "policy"
            },
            {
                "organization": "Meril",
                "department": "HR", 
                "content": "Meril employees get 25 days of annual leave plus 10 sick days.",
                "type": "policy"
            },
            {
                "organization": "Meril",
                "department": "Technical",
                "content": "All technical projects at Meril follow Agile methodology with 2-week sprints.",
                "type": "procedure"
            }
        ]
        
        # Store in memory system
        for knowledge in sample_knowledge:
            await self.memory_system.store_memory(
                MemoryType.SEMANTIC,
                knowledge,
                organization=knowledge["organization"],
                department=knowledge["department"],
                importance=0.8,
                tags=[knowledge["type"], "organizational_knowledge"]
            )
        
        print("✅ Knowledge base initialized with sample data")
    
    async def process_query(self, query: str, organization: str = None, 
                           department: str = None, coordination_pattern: str = "hierarchical") -> Dict[str, Any]:
        """Process a query using the Advanced Agentic RAG system"""
        if not self.is_initialized:
            return {"error": "System not initialized. Call initialize_system() first."}
        
        import time
        start_time = time.time()
        
        # Update stats
        self.system_stats["total_queries"] += 1
        
        # Prepare task
        task = {
            "query": query,
            "organization": organization,
            "department": department,
            "timestamp": time.time(),
            "coordination_pattern": coordination_pattern
        }
        
        try:
            # Get coordination pattern
            pattern = CoordinationPattern(coordination_pattern)
            
            # Process through agent coordination
            result = await self.agent_coordinator.coordinate_with_pattern(task, pattern)
            
            # Apply post-processing
            final_result = await self.post_process_result(result, task)
            
            # Update statistics
            execution_time = time.time() - start_time
            self.update_system_stats(True, execution_time)
            
            # Add system metadata
            final_result["system_metadata"] = {
                "execution_time": execution_time,
                "coordination_pattern": coordination_pattern,
                "agents_involved": self.extract_agents_involved(result),
                "knowledge_sources": self.extract_knowledge_sources(result),
                "system_version": "1.0.0"
            }
            
            return final_result
            
        except Exception as e:
            # Update statistics
            execution_time = time.time() - start_time
            self.update_system_stats(False, execution_time)
            
            return {
                "error": f"Query processing failed: {str(e)}",
                "execution_time": execution_time,
                "system_metadata": {
                    "error_type": type(e).__name__,
                    "coordination_pattern": coordination_pattern
                }
            }
    
    async def post_process_result(self, result: Dict[str, Any], task: Dict[str, Any]) -> Dict[str, Any]:
        """Post-process the result before returning"""
        # Apply final knowledge fusion if multiple sources
        if "fused_knowledge" in result:
            result["knowledge_fusion_applied"] = True
        
        # Format response for better readability
        if "results" in result and isinstance(result["results"], list):
            formatted_results = []
            for item in result["results"]:
                if isinstance(item, dict) and "text" in item:
                    formatted_results.append({
                        "response": item["text"],
                        "confidence": item.get("score", 0.5),
                        "source": item.get("source", "system")
                    })
            result["formatted_results"] = formatted_results
        
        # Add reasoning transparency
        if "reasoning_trace" not in result:
            result["reasoning_trace"] = ["Query processed through Advanced Agentic RAG system"]
        
        return result
    
    def extract_agents_involved(self, result: Dict[str, Any]) -> List[str]:
        """Extract list of agents involved in processing"""
        agents = []
        
        if "participating_agents" in result:
            agents.extend(result["participating_agents"])
        
        if "agent" in result:
            agents.append(result["agent"])
        
        # Look for agent references in nested results
        if "results" in result:
            for item in result["results"]:
                if isinstance(item, dict) and "agent" in item:
                    agents.append(item["agent"])
        
        return list(set(agents))
    
    def extract_knowledge_sources(self, result: Dict[str, Any]) -> List[str]:
        """Extract knowledge sources used"""
        sources = []
        
        if "sources" in result:
            sources.extend(result["sources"])
        
        if "fused_knowledge" in result:
            fused = result["fused_knowledge"]
            if isinstance(fused, dict) and "sources" in fused:
                sources.extend(fused["sources"])
        
        return list(set(sources))
    
    def update_system_stats(self, success: bool, execution_time: float):
        """Update system statistics"""
        if success:
            self.system_stats["successful_responses"] += 1
        
        # Update average response time
        total_queries = self.system_stats["total_queries"]
        current_avg = self.system_stats["average_response_time"]
        self.system_stats["average_response_time"] = (
            (current_avg * (total_queries - 1) + execution_time) / total_queries
        )
    
    async def print_system_overview(self):
        """Print system overview"""
        print("\n" + "="*60)
        print("🤖 ADVANCED AGENTIC RAG SYSTEM OVERVIEW")
        print("="*60)
        print(f"📊 Registered Agents: {len(self.agents)}")
        print(f"🧠 Memory System: Active")
        print(f"🔧 Tool Framework: {len(self.tool_framework.tools)} tools available")
        print(f"🔗 Knowledge Fusion: Enabled")
        print(f"💭 Tree of Thoughts: Enabled")
        print(f"🔍 Self-Reflection: Enabled")
        print("\n📋 Available Agents:")
        for agent_name in self.agents.keys():
            print(f"  • {agent_name}")
        print("\n🎯 Coordination Patterns:")
        print("  • Hierarchical (default)")
        print("  • Collaborative")
        print("  • Specialist Consultation")
        print("="*60)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            "initialized": self.is_initialized,
            "agents_count": len(self.agents),
            "statistics": self.system_stats,
            "memory_stats": self.memory_system.get_memory_statistics(),
            "tool_stats": self.tool_framework.get_tool_statistics(),
            "available_patterns": [pattern.value for pattern in CoordinationPattern]
        }

# Example usage and testing
async def main():
    """Main function for testing the system"""
    # Initialize system
    system = AdvancedAgenticRAGSystem()
    await system.initialize_system()
    
    # Test queries
    test_queries = [
        {
            "query": "What is the leave policy for NuvoAi employees?",
            "organization": "NuvoAi",
            "department": "HR"
        },
        {
            "query": "How does Meril handle technical project management?",
            "organization": "Meril", 
            "department": "Technical"
        },
        {
            "query": "Compare maternity leave policies between organizations",
            "coordination_pattern": "collaborative"
        }
    ]
    
    print("\n🧪 TESTING SYSTEM WITH SAMPLE QUERIES")
    print("="*60)
    
    for i, test_query in enumerate(test_queries, 1):
        print(f"\n📝 Test Query {i}: {test_query['query']}")
        print("-" * 40)
        
        result = await system.process_query(**test_query)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
        else:
            print(f"✅ Success!")
            if "formatted_results" in result:
                for res in result["formatted_results"]:
                    print(f"📄 Response: {res['response'][:200]}...")
                    print(f"🎯 Confidence: {res['confidence']:.2f}")
            
            if "system_metadata" in result:
                metadata = result["system_metadata"]
                print(f"⏱️  Execution Time: {metadata['execution_time']:.2f}s")
                print(f"🤝 Agents Involved: {', '.join(metadata.get('agents_involved', []))}")
    
    # Print final system status
    print("\n📊 FINAL SYSTEM STATUS")
    print("="*60)
    status = system.get_system_status()
    print(f"Total Queries Processed: {status['statistics']['total_queries']}")
    print(f"Successful Responses: {status['statistics']['successful_responses']}")
    print(f"Average Response Time: {status['statistics']['average_response_time']:.2f}s")
    print(f"Memory Entries: {status['memory_stats']['total_memories']}")

if __name__ == "__main__":
    asyncio.run(main())
