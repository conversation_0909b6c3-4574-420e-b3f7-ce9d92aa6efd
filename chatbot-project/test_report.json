{"summary": {"total_tests": 17, "passed_tests": 14, "success_rate": 82.35294117647058, "timestamp": "2025-07-16T17:33:10.625345"}, "detailed_results": [{"test_name": "System Initialization", "passed": true, "details": "System initialized successfully", "timestamp": "2025-07-16T17:33:10.623317"}, {"test_name": "Basic Query: Simple HR Query", "passed": true, "details": "Query processed successfully", "timestamp": "2025-07-16T17:33:10.623522"}, {"test_name": "Basic Query: Technical Query", "passed": true, "details": "Query processed successfully", "timestamp": "2025-07-16T17:33:10.623668"}, {"test_name": "Basic Query: General Query", "passed": true, "details": "Query processed successfully", "timestamp": "2025-07-16T17:33:10.623792"}, {"test_name": "Coordination Pattern: hierarchical", "passed": true, "details": "<PERSON><PERSON> executed successfully", "timestamp": "2025-07-16T17:33:10.623918"}, {"test_name": "Coordination Pattern: collaborative", "passed": false, "details": "Pattern collaborative failed: No solutions to reach consensus on", "timestamp": "2025-07-16T17:33:10.624018"}, {"test_name": "Coordination Pattern: specialist_consultation", "passed": true, "details": "<PERSON><PERSON> executed successfully", "timestamp": "2025-07-16T17:33:10.624125"}, {"test_name": "Multi-Agent Collaboration", "passed": false, "details": "Multi-agent collaboration not detected", "timestamp": "2025-07-16T17:33:10.624257"}, {"test_name": "Knowledge Fusion", "passed": false, "details": "Knowledge fusion failed: Could not reach consensus", "timestamp": "2025-07-16T17:33:10.624376"}, {"test_name": "Memory System", "passed": true, "details": "Total memories: 12", "timestamp": "2025-07-16T17:33:10.624520"}, {"test_name": "Tool Framework", "passed": true, "details": "Available tools: 1", "timestamp": "2025-07-16T17:33:10.624563"}, {"test_name": "Reasoning Systems", "passed": true, "details": "Reasoning steps: 1", "timestamp": "2025-07-16T17:33:10.624727"}, {"test_name": "Critic Verification", "passed": true, "details": "Verification applied: True", "timestamp": "2025-07-16T17:33:10.624895"}, {"test_name": "Error Handling: Empty Query", "passed": true, "details": "Handled gracefully", "timestamp": "2025-07-16T17:33:10.624974"}, {"test_name": "Error Handling: Invalid Organization", "passed": true, "details": "Handled gracefully", "timestamp": "2025-07-16T17:33:10.625071"}, {"test_name": "Error Handling: <PERSON><PERSON><PERSON> Coordination Pattern", "passed": true, "details": "Handled gracefully", "timestamp": "2025-07-16T17:33:10.625089"}, {"test_name": "Performance", "passed": true, "details": "Response time: 0.00s (threshold: 10.0s)", "timestamp": "2025-07-16T17:33:10.625242"}], "system_status": {"initialized": true, "agents_count": 10, "statistics": {"total_queries": 15, "successful_responses": 14, "average_response_time": 0.00011022885640462239, "agent_utilization": {}}, "memory_stats": {"total_memories": 18, "memory_by_type": {"conversation": 14, "episodic": 0, "semantic": 4, "working": 0, "agent_specific": 0}, "index_size": 8, "most_accessed": [{"id": "2ee9952c-1c23-4190-a02c-25f5237504c2", "access_count": 9, "type": "conversation"}, {"id": "568ff02d-db18-4af6-8074-7152a1bfed0e", "access_count": 8, "type": "semantic"}, {"id": "d56aa22b-05c3-4c1c-9bc9-8da110a76d84", "access_count": 6, "type": "semantic"}, {"id": "b35e77f5-1601-448c-8ab5-8eb0d51ba256", "access_count": 6, "type": "semantic"}, {"id": "efa2f02b-2957-4101-8de4-d7e57a523afd", "access_count": 5, "type": "conversation"}, {"id": "25eb0d6e-d6df-4869-a84b-d6f4deac0a4c", "access_count": 4, "type": "conversation"}, {"id": "cf059709-efc1-4635-93f1-317cfedbe9d3", "access_count": 4, "type": "semantic"}, {"id": "e1e1be92-c2e2-478e-bf47-1018aed6a604", "access_count": 2, "type": "conversation"}, {"id": "700c6b2c-d75a-489f-aa37-b24748d107b7", "access_count": 2, "type": "conversation"}, {"id": "962467ba-1fd0-4be0-955f-431a80b49186", "access_count": 2, "type": "conversation"}]}, "tool_stats": {"total_tools": 1, "available_tools": 1, "tools_by_type": {"computation": 1}, "total_executions": 0, "overall_success_rate": 0, "most_used_tools": []}, "available_patterns": ["hierarchical", "collaborative", "specialist_consultation"]}}