"""Agent allocation system for task assignment."""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class AgentCapability(Enum):
    KNOWLEDGE_RETRIEVAL = "knowledge_retrieval"
    REASONING = "reasoning"
    TOOL_EXECUTION = "tool_execution"
    VERIFICATION = "verification"
    SYNTHESIS = "synthesis"
    ORGANIZATION_SPECIFIC = "organization_specific"
    DEPARTMENT_SPECIFIC = "department_specific"

@dataclass
class AgentInfo:
    id: str
    name: str
    capabilities: List[AgentCapability]
    specializations: List[str]
    current_load: float
    max_load: float
    performance_score: float
    availability: bool

class AgentAllocator:
    def __init__(self):
        self.agents = {}
        self.allocation_history = []
        self.performance_tracker = {}
        
    def register_agent(self, agent_info: AgentInfo):
        """Register an agent in the allocation system."""
        self.agents[agent_info.id] = agent_info
        self.performance_tracker[agent_info.id] = {
            "tasks_completed": 0,
            "success_rate": 1.0,
            "avg_response_time": 0.0
        }
    
    def allocate_task(self, task: Dict[str, Any]) -> Optional[str]:
        """Allocate a task to the most suitable agent."""
        required_capabilities = task.get("required_capabilities", [])
        task_context = task.get("context", {})
        
        # Find candidate agents
        candidates = self._find_candidate_agents(required_capabilities, task_context)
        
        if not candidates:
            return None
        
        # Score and rank candidates
        scored_candidates = self._score_candidates(candidates, task)
        
        # Select best candidate
        best_agent = self._select_best_agent(scored_candidates)
        
        if best_agent:
            self._assign_task_to_agent(best_agent, task)
            return best_agent
        
        return None
    
    def _find_candidate_agents(self, required_capabilities: List[str], context: Dict[str, Any]) -> List[str]:
        """Find agents that can handle the required capabilities."""
        candidates = []
        
        for agent_id, agent_info in self.agents.items():
            if not agent_info.availability:
                continue
                
            if agent_info.current_load >= agent_info.max_load:
                continue
            
            # Check capability match
            agent_caps = [cap.value for cap in agent_info.capabilities]
            if not all(cap in agent_caps for cap in required_capabilities):
                continue
            
            # Check specialization match
            if self._matches_specialization(agent_info, context):
                candidates.append(agent_id)
        
        return candidates
    
    def _matches_specialization(self, agent_info: AgentInfo, context: Dict[str, Any]) -> bool:
        """Check if agent specialization matches task context."""
        # Organization match
        if "organization" in context:
            org_specialization = f"{context['organization']}_knowledge"
            if org_specialization in agent_info.specializations:
                return True
        
        # Department match
        if "department" in context:
            dept_specialization = f"{context['department']}_expertise"
            if dept_specialization in agent_info.specializations:
                return True
        
        # General match if no specific requirements
        if not context.get("organization") and not context.get("department"):
            return True
        
        return False
    
    def _score_candidates(self, candidates: List[str], task: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Score candidate agents for task suitability."""
        scored_candidates = []
        
        for agent_id in candidates:
            agent_info = self.agents[agent_id]
            performance = self.performance_tracker[agent_id]
            
            score = self._calculate_agent_score(agent_info, performance, task)
            
            scored_candidates.append({
                "agent_id": agent_id,
                "score": score,
                "agent_info": agent_info
            })
        
        # Sort by score (descending)
        scored_candidates.sort(key=lambda x: x["score"], reverse=True)
        
        return scored_candidates
    
    def _calculate_agent_score(self, agent_info: AgentInfo, performance: Dict[str, Any], task: Dict[str, Any]) -> float:
        """Calculate suitability score for an agent."""
        score = 0.0
        
        # Base performance score (0-40 points)
        score += agent_info.performance_score * 40
        
        # Success rate (0-30 points)
        score += performance["success_rate"] * 30
        
        # Load factor (0-20 points) - prefer less loaded agents
        load_factor = 1.0 - (agent_info.current_load / agent_info.max_load)
        score += load_factor * 20
        
        # Specialization bonus (0-10 points)
        specialization_bonus = self._calculate_specialization_bonus(agent_info, task)
        score += specialization_bonus
        
        return score
    
    def _calculate_specialization_bonus(self, agent_info: AgentInfo, task: Dict[str, Any]) -> float:
        """Calculate bonus points for agent specialization match."""
        bonus = 0.0
        context = task.get("context", {})
        
        # Organization specialization bonus
        if "organization" in context:
            org_specialization = f"{context['organization']}_knowledge"
            if org_specialization in agent_info.specializations:
                bonus += 5.0
        
        # Department specialization bonus
        if "department" in context:
            dept_specialization = f"{context['department']}_expertise"
            if dept_specialization in agent_info.specializations:
                bonus += 3.0
        
        # Task type specialization bonus
        task_type = task.get("type", "")
        if task_type in agent_info.specializations:
            bonus += 2.0
        
        return bonus
    
    def _select_best_agent(self, scored_candidates: List[Dict[str, Any]]) -> Optional[str]:
        """Select the best agent from scored candidates."""
        if not scored_candidates:
            return None
        
        # Simple selection: highest score
        best_candidate = scored_candidates[0]
        
        # Additional checks for load balancing
        if len(scored_candidates) > 1:
            # If top candidates have similar scores, prefer less loaded agent
            top_score = best_candidate["score"]
            similar_candidates = [c for c in scored_candidates if abs(c["score"] - top_score) < 5.0]
            
            if len(similar_candidates) > 1:
                # Select least loaded among similar candidates
                best_candidate = min(similar_candidates, 
                                   key=lambda x: x["agent_info"].current_load)
        
        return best_candidate["agent_id"]
    
    def _assign_task_to_agent(self, agent_id: str, task: Dict[str, Any]):
        """Assign task to agent and update load."""
        agent_info = self.agents[agent_id]
        
        # Estimate task load (simplified)
        estimated_load = self._estimate_task_load(task)
        agent_info.current_load += estimated_load
        
        # Record allocation
        allocation_record = {
            "agent_id": agent_id,
            "task_id": task.get("id"),
            "task_type": task.get("type"),
            "estimated_load": estimated_load,
            "timestamp": task.get("timestamp")
        }
        self.allocation_history.append(allocation_record)
    
    def _estimate_task_load(self, task: Dict[str, Any]) -> float:
        """Estimate computational load for a task."""
        base_load = 0.1
        
        # Adjust based on task type
        task_type = task.get("type", "")
        type_multipliers = {
            "information_retrieval": 1.0,
            "reasoning": 2.0,
            "tool_execution": 1.5,
            "verification": 1.2,
            "synthesis": 1.8
        }
        
        multiplier = type_multipliers.get(task_type, 1.0)
        
        # Adjust based on estimated duration
        duration = task.get("estimated_duration", 30)
        duration_factor = duration / 60.0  # Convert to hours
        
        return base_load * multiplier * duration_factor
    
    def release_agent(self, agent_id: str, task_id: str, success: bool, response_time: float):
        """Release agent after task completion and update performance."""
        if agent_id not in self.agents:
            return
        
        agent_info = self.agents[agent_id]
        performance = self.performance_tracker[agent_id]
        
        # Find and remove task load
        for record in self.allocation_history:
            if record["agent_id"] == agent_id and record["task_id"] == task_id:
                agent_info.current_load -= record["estimated_load"]
                agent_info.current_load = max(0.0, agent_info.current_load)
                break
        
        # Update performance metrics
        performance["tasks_completed"] += 1
        
        # Update success rate (exponential moving average)
        alpha = 0.1
        performance["success_rate"] = (1 - alpha) * performance["success_rate"] + alpha * (1.0 if success else 0.0)
        
        # Update average response time
        performance["avg_response_time"] = (1 - alpha) * performance["avg_response_time"] + alpha * response_time
        
        # Update agent performance score
        agent_info.performance_score = (performance["success_rate"] * 0.7 + 
                                      (1.0 - min(response_time / 120.0, 1.0)) * 0.3)
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current status of all agents."""
        status = {}
        
        for agent_id, agent_info in self.agents.items():
            performance = self.performance_tracker[agent_id]
            
            status[agent_id] = {
                "name": agent_info.name,
                "capabilities": [cap.value for cap in agent_info.capabilities],
                "specializations": agent_info.specializations,
                "current_load": agent_info.current_load,
                "max_load": agent_info.max_load,
                "availability": agent_info.availability,
                "performance_score": agent_info.performance_score,
                "tasks_completed": performance["tasks_completed"],
                "success_rate": performance["success_rate"],
                "avg_response_time": performance["avg_response_time"]
            }
        
        return status
    
    def rebalance_load(self):
        """Rebalance load across agents if needed."""
        # Simple rebalancing: mark overloaded agents as unavailable temporarily
        for agent_id, agent_info in self.agents.items():
            if agent_info.current_load > agent_info.max_load * 0.9:
                agent_info.availability = False
            elif agent_info.current_load < agent_info.max_load * 0.5:
                agent_info.availability = True