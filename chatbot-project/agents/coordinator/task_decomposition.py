"""Task decomposition logic for complex queries."""

from typing import Dict, List, Any, Tuple
import re

class TaskDecomposer:
    def __init__(self):
        self.decomposition_patterns = {
            "comparison": r"(compare|versus|vs|difference between)",
            "multi_entity": r"(and|or|\+|,)",
            "conditional": r"(if|when|unless|provided that)",
            "temporal": r"(before|after|during|since|until)",
            "causal": r"(because|due to|caused by|results in)",
            "hypothetical": r"(what if|suppose|assume|hypothetically)"
        }
    
    def decompose_query(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Decompose complex query into sub-queries."""
        query_type = self._identify_query_type(query)
        
        if query_type == "comparison":
            return self._decompose_comparison(query, context)
        elif query_type == "multi_entity":
            return self._decompose_multi_entity(query, context)
        elif query_type == "conditional":
            return self._decompose_conditional(query, context)
        elif query_type == "causal":
            return self._decompose_causal(query, context)
        else:
            return self._decompose_general(query, context)
    
    def _identify_query_type(self, query: str) -> str:
        """Identify the type of query for appropriate decomposition."""
        query_lower = query.lower()
        
        for pattern_type, pattern in self.decomposition_patterns.items():
            if re.search(pattern, query_lower):
                return pattern_type
        
        return "general"
    
    def _decompose_comparison(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Decompose comparison queries."""
        sub_queries = []
        
        # Extract entities being compared
        entities = self._extract_comparison_entities(query)
        
        for entity in entities:
            sub_queries.append({
                "query": f"What are the details about {entity}?",
                "type": "information_retrieval",
                "entity": entity,
                "context": context,
                "purpose": "comparison_data"
            })
        
        # Add comparison analysis sub-query
        sub_queries.append({
            "query": f"Compare {' and '.join(entities)}",
            "type": "analysis",
            "entities": entities,
            "context": context,
            "purpose": "comparison_analysis",
            "dependencies": [sq["query"] for sq in sub_queries[:-1]]
        })
        
        return sub_queries
    
    def _decompose_multi_entity(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Decompose queries involving multiple entities."""
        sub_queries = []
        
        # Extract multiple entities
        entities = self._extract_multiple_entities(query)
        
        for entity in entities:
            sub_queries.append({
                "query": f"Information about {entity} in context of: {query}",
                "type": "information_retrieval",
                "entity": entity,
                "context": context,
                "purpose": "entity_information"
            })
        
        # Add synthesis sub-query
        sub_queries.append({
            "query": f"Synthesize information about {', '.join(entities)}",
            "type": "synthesis",
            "entities": entities,
            "context": context,
            "purpose": "multi_entity_synthesis",
            "dependencies": [sq["query"] for sq in sub_queries[:-1]]
        })
        
        return sub_queries
    
    def _decompose_conditional(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Decompose conditional queries."""
        sub_queries = []
        
        # Extract condition and consequence
        condition, consequence = self._extract_condition_consequence(query)
        
        # Sub-query for condition analysis
        sub_queries.append({
            "query": f"Analyze condition: {condition}",
            "type": "condition_analysis",
            "condition": condition,
            "context": context,
            "purpose": "condition_evaluation"
        })
        
        # Sub-query for consequence analysis
        sub_queries.append({
            "query": f"Analyze consequence: {consequence}",
            "type": "consequence_analysis",
            "consequence": consequence,
            "context": context,
            "purpose": "consequence_evaluation"
        })
        
        # Sub-query for conditional reasoning
        sub_queries.append({
            "query": f"If {condition}, then {consequence}",
            "type": "conditional_reasoning",
            "condition": condition,
            "consequence": consequence,
            "context": context,
            "purpose": "conditional_analysis",
            "dependencies": [sub_queries[0]["query"], sub_queries[1]["query"]]
        })
        
        return sub_queries
    
    def _decompose_causal(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Decompose causal queries."""
        sub_queries = []
        
        # Extract cause and effect
        cause, effect = self._extract_cause_effect(query)
        
        # Sub-query for cause analysis
        sub_queries.append({
            "query": f"Analyze cause: {cause}",
            "type": "cause_analysis",
            "cause": cause,
            "context": context,
            "purpose": "cause_evaluation"
        })
        
        # Sub-query for effect analysis
        sub_queries.append({
            "query": f"Analyze effect: {effect}",
            "type": "effect_analysis",
            "effect": effect,
            "context": context,
            "purpose": "effect_evaluation"
        })
        
        # Sub-query for causal reasoning
        sub_queries.append({
            "query": f"Causal relationship between {cause} and {effect}",
            "type": "causal_reasoning",
            "cause": cause,
            "effect": effect,
            "context": context,
            "purpose": "causal_analysis",
            "dependencies": [sub_queries[0]["query"], sub_queries[1]["query"]]
        })
        
        return sub_queries
    
    def _decompose_general(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Decompose general complex queries."""
        sub_queries = []
        
        # Extract key concepts
        concepts = self._extract_key_concepts(query)
        
        # Create sub-queries for each concept
        for concept in concepts:
            sub_queries.append({
                "query": f"Information about {concept}",
                "type": "information_retrieval",
                "concept": concept,
                "context": context,
                "purpose": "concept_information"
            })
        
        # Add integration sub-query if multiple concepts
        if len(concepts) > 1:
            sub_queries.append({
                "query": f"Integrate information about {', '.join(concepts)}",
                "type": "integration",
                "concepts": concepts,
                "context": context,
                "purpose": "concept_integration",
                "dependencies": [sq["query"] for sq in sub_queries[:-1]]
            })
        
        return sub_queries
    
    def _extract_comparison_entities(self, query: str) -> List[str]:
        """Extract entities being compared."""
        # Simple extraction - in production, use NER
        patterns = [
            r"compare\s+([^and]+)\s+and\s+([^.?!]+)",
            r"([^vs]+)\s+vs\s+([^.?!]+)",
            r"difference between\s+([^and]+)\s+and\s+([^.?!]+)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, query.lower())
            if match:
                return [match.group(1).strip(), match.group(2).strip()]
        
        return ["entity1", "entity2"]  # Fallback
    
    def _extract_multiple_entities(self, query: str) -> List[str]:
        """Extract multiple entities from query."""
        # Simple extraction - split on common separators
        separators = [" and ", " or ", ", ", " + "]
        entities = [query]
        
        for sep in separators:
            new_entities = []
            for entity in entities:
                new_entities.extend(entity.split(sep))
            entities = new_entities
        
        return [e.strip() for e in entities if e.strip()]
    
    def _extract_condition_consequence(self, query: str) -> Tuple[str, str]:
        """Extract condition and consequence from conditional query."""
        patterns = [
            r"if\s+([^,]+),?\s+(.+)",
            r"when\s+([^,]+),?\s+(.+)",
            r"provided that\s+([^,]+),?\s+(.+)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, query.lower())
            if match:
                return match.group(1).strip(), match.group(2).strip()
        
        return "condition", "consequence"  # Fallback
    
    def _extract_cause_effect(self, query: str) -> Tuple[str, str]:
        """Extract cause and effect from causal query."""
        patterns = [
            r"(.+)\s+because\s+(.+)",
            r"(.+)\s+due to\s+(.+)",
            r"(.+)\s+caused by\s+(.+)",
            r"(.+)\s+results in\s+(.+)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, query.lower())
            if match:
                return match.group(2).strip(), match.group(1).strip()
        
        return "cause", "effect"  # Fallback
    
    def _extract_key_concepts(self, query: str) -> List[str]:
        """Extract key concepts from query."""
        # Simple keyword extraction - in production, use advanced NLP
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        words = query.lower().split()
        concepts = [word for word in words if word not in stop_words and len(word) > 3]
        
        return concepts[:3]  # Limit to top 3 concepts