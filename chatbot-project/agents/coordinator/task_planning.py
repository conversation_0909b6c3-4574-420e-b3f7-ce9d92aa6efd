"""Task planning system for agent coordination."""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import uuid

class TaskType(Enum):
    INFORMATION_RETRIEVAL = "information_retrieval"
    REASONING = "reasoning"
    TOOL_EXECUTION = "tool_execution"
    VERIFICATION = "verification"
    SYNTHESIS = "synthesis"

class TaskPriority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class Task:
    id: str
    type: TaskType
    description: str
    priority: TaskPriority
    dependencies: List[str]
    required_capabilities: List[str]
    context: Dict[str, Any]
    estimated_duration: int
    assigned_agent: Optional[str] = None
    status: str = "pending"

class TaskPlanner:
    def __init__(self):
        self.tasks = {}
        self.task_graph = {}
        
    def create_plan(self, query: str, context: Dict[str, Any]) -> List[Task]:
        """Create a task plan for a given query."""
        complexity = self._assess_complexity(query, context)
        
        if complexity == "simple":
            return self._create_simple_plan(query, context)
        elif complexity == "moderate":
            return self._create_moderate_plan(query, context)
        else:
            return self._create_complex_plan(query, context)
    
    def _assess_complexity(self, query: str, context: Dict[str, Any]) -> str:
        """Assess query complexity."""
        indicators = 0
        
        # Check for multiple organizations/departments
        if len(context.get("organizations", [])) > 1:
            indicators += 2
        if len(context.get("departments", [])) > 1:
            indicators += 1
            
        # Check for reasoning requirements
        reasoning_keywords = ["why", "how", "impact", "compare", "analyze"]
        if any(keyword in query.lower() for keyword in reasoning_keywords):
            indicators += 2
            
        # Check for tool requirements
        tool_keywords = ["calculate", "search", "find", "lookup"]
        if any(keyword in query.lower() for keyword in tool_keywords):
            indicators += 1
            
        if indicators <= 2:
            return "simple"
        elif indicators <= 4:
            return "moderate"
        else:
            return "complex"
    
    def _create_simple_plan(self, query: str, context: Dict[str, Any]) -> List[Task]:
        """Create plan for simple queries."""
        tasks = []
        
        # Single information retrieval task
        task = Task(
            id=str(uuid.uuid4()),
            type=TaskType.INFORMATION_RETRIEVAL,
            description=f"Retrieve information for: {query}",
            priority=TaskPriority.HIGH,
            dependencies=[],
            required_capabilities=["knowledge_retrieval"],
            context=context,
            estimated_duration=30
        )
        tasks.append(task)
        
        return tasks
    
    def _create_moderate_plan(self, query: str, context: Dict[str, Any]) -> List[Task]:
        """Create plan for moderate complexity queries."""
        tasks = []
        
        # Information retrieval
        retrieval_task = Task(
            id=str(uuid.uuid4()),
            type=TaskType.INFORMATION_RETRIEVAL,
            description=f"Retrieve information for: {query}",
            priority=TaskPriority.HIGH,
            dependencies=[],
            required_capabilities=["knowledge_retrieval"],
            context=context,
            estimated_duration=45
        )
        tasks.append(retrieval_task)
        
        # Reasoning task
        reasoning_task = Task(
            id=str(uuid.uuid4()),
            type=TaskType.REASONING,
            description=f"Analyze and reason about: {query}",
            priority=TaskPriority.HIGH,
            dependencies=[retrieval_task.id],
            required_capabilities=["reasoning", "analysis"],
            context=context,
            estimated_duration=60
        )
        tasks.append(reasoning_task)
        
        return tasks
    
    def _create_complex_plan(self, query: str, context: Dict[str, Any]) -> List[Task]:
        """Create plan for complex queries."""
        tasks = []
        
        # Multiple information retrieval tasks
        for org in context.get("organizations", []):
            retrieval_task = Task(
                id=str(uuid.uuid4()),
                type=TaskType.INFORMATION_RETRIEVAL,
                description=f"Retrieve {org} information for: {query}",
                priority=TaskPriority.HIGH,
                dependencies=[],
                required_capabilities=["knowledge_retrieval", f"{org}_knowledge"],
                context={**context, "organization": org},
                estimated_duration=45
            )
            tasks.append(retrieval_task)
        
        # Tool execution if needed
        if self._requires_tools(query):
            tool_task = Task(
                id=str(uuid.uuid4()),
                type=TaskType.TOOL_EXECUTION,
                description=f"Execute tools for: {query}",
                priority=TaskPriority.MEDIUM,
                dependencies=[],
                required_capabilities=["tool_execution"],
                context=context,
                estimated_duration=30
            )
            tasks.append(tool_task)
        
        # Reasoning task
        reasoning_deps = [t.id for t in tasks if t.type == TaskType.INFORMATION_RETRIEVAL]
        reasoning_task = Task(
            id=str(uuid.uuid4()),
            type=TaskType.REASONING,
            description=f"Complex reasoning for: {query}",
            priority=TaskPriority.HIGH,
            dependencies=reasoning_deps,
            required_capabilities=["advanced_reasoning", "multi_source_analysis"],
            context=context,
            estimated_duration=90
        )
        tasks.append(reasoning_task)
        
        # Verification task
        verification_task = Task(
            id=str(uuid.uuid4()),
            type=TaskType.VERIFICATION,
            description=f"Verify response for: {query}",
            priority=TaskPriority.MEDIUM,
            dependencies=[reasoning_task.id],
            required_capabilities=["verification", "fact_checking"],
            context=context,
            estimated_duration=30
        )
        tasks.append(verification_task)
        
        return tasks
    
    def _requires_tools(self, query: str) -> bool:
        """Check if query requires tool execution."""
        tool_keywords = ["calculate", "compute", "search database", "lookup"]
        return any(keyword in query.lower() for keyword in tool_keywords)
    
    def optimize_plan(self, tasks: List[Task]) -> List[Task]:
        """Optimize task execution order."""
        # Build dependency graph
        self._build_dependency_graph(tasks)
        
        # Identify parallel execution opportunities
        optimized_tasks = self._identify_parallel_tasks(tasks)
        
        # Adjust priorities based on dependencies
        self._adjust_priorities(optimized_tasks)
        
        return optimized_tasks
    
    def _build_dependency_graph(self, tasks: List[Task]):
        """Build task dependency graph."""
        self.task_graph = {}
        for task in tasks:
            self.task_graph[task.id] = {
                "task": task,
                "dependencies": task.dependencies,
                "dependents": []
            }
        
        # Build reverse dependencies
        for task_id, task_info in self.task_graph.items():
            for dep_id in task_info["dependencies"]:
                if dep_id in self.task_graph:
                    self.task_graph[dep_id]["dependents"].append(task_id)
    
    def _identify_parallel_tasks(self, tasks: List[Task]) -> List[Task]:
        """Identify tasks that can be executed in parallel."""
        for task in tasks:
            # Tasks with no dependencies can start immediately
            if not task.dependencies:
                task.context["parallel_group"] = 0
            else:
                # Calculate the maximum group of dependencies
                max_group = 0
                for dep_id in task.dependencies:
                    dep_task = next((t for t in tasks if t.id == dep_id), None)
                    if dep_task and "parallel_group" in dep_task.context:
                        max_group = max(max_group, dep_task.context["parallel_group"])
                task.context["parallel_group"] = max_group + 1
        
        return tasks
    
    def _adjust_priorities(self, tasks: List[Task]):
        """Adjust task priorities based on dependencies."""
        for task in tasks:
            # Critical path tasks get higher priority
            if self._is_on_critical_path(task, tasks):
                if task.priority.value < TaskPriority.HIGH.value:
                    task.priority = TaskPriority.HIGH
    
    def _is_on_critical_path(self, task: Task, tasks: List[Task]) -> bool:
        """Check if task is on the critical path."""
        # Simple heuristic: tasks with many dependents are on critical path
        dependents = self.task_graph.get(task.id, {}).get("dependents", [])
        return len(dependents) > 1