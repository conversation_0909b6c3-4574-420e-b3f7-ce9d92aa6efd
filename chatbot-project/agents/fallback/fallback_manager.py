"""Fallback mechanisms for agent system."""

from typing import Dict, Any, Optional
from enum import Enum
import asyncio

class FallbackTrigger(Enum):
    LOW_CONFIDENCE = "low_confidence"
    AGENT_ERROR = "agent_error"
    TIMEOUT = "timeout"
    VERIFICATION_FAILED = "verification_failed"

class FallbackManager:
    def __init__(self):
        self.confidence_threshold = 0.6
        self.human_handoff_threshold = 0.3
        self.fallback_responses = {
            "low_confidence": "I'm not entirely confident in this answer. Would you like me to connect you with a human agent?",
            "error": "I encountered an issue processing your request. Let me transfer you to a human agent.",
            "timeout": "This query is taking longer than expected. A human agent can help you better.",
            "verification_failed": "I couldn't verify this information. A human agent will provide accurate details."
        }
    
    async def handle_fallback(self, trigger: FallbackTrigger, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle fallback scenarios."""
        confidence = context.get("confidence", 0.0)
        
        if confidence < self.human_handoff_threshold:
            return await self.initiate_human_handoff(context)
        elif confidence < self.confidence_threshold:
            return self.generate_low_confidence_response(context)
        else:
            return self.generate_fallback_response(trigger, context)
    
    async def initiate_human_handoff(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Initiate human agent handoff."""
        ticket_id = f"HO-{context.get('user_id', '000')}-{hash(context.get('query', ''))}"
        
        handoff_data = {
            "user_id": context.get("user_id"),
            "query": context.get("query"),
            "context": context,
            "timestamp": context.get("timestamp"),
            "reason": "Low confidence or complex query",
            "ticket_id": ticket_id
        }
        
        # Queue for human agent (mock implementation)
        await self.queue_for_human_agent(handoff_data)
        
        return {
            "response": "I've connected you with a human agent who will assist you shortly.",
            "type": "human_handoff",
            "estimated_wait": "2-5 minutes",
            "ticket_id": ticket_id
        }
    
    def generate_low_confidence_response(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response for low confidence scenarios."""
        base_response = context.get("response", "")
        confidence = context.get("confidence", 0.0)
        
        return {
            "response": f"{base_response}\n\n⚠️ Confidence: {confidence:.1%}. {self.fallback_responses['low_confidence']}",
            "type": "low_confidence",
            "confidence": confidence,
            "human_handoff_available": True
        }
    
    def generate_fallback_response(self, trigger: FallbackTrigger, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate appropriate fallback response."""
        trigger_map = {
            FallbackTrigger.LOW_CONFIDENCE: "low_confidence",
            FallbackTrigger.AGENT_ERROR: "error",
            FallbackTrigger.TIMEOUT: "timeout",
            FallbackTrigger.VERIFICATION_FAILED: "verification_failed"
        }
        
        response_key = trigger_map.get(trigger, "error")
        
        return {
            "response": self.fallback_responses[response_key],
            "type": "fallback",
            "trigger": trigger.value,
            "human_handoff_available": True
        }
    
    async def queue_for_human_agent(self, handoff_data: Dict[str, Any]):
        """Queue request for human agent (mock implementation)."""
        # In production, this would integrate with ticketing system
        print(f"Queued for human agent: {handoff_data['ticket_id']}")
        return True