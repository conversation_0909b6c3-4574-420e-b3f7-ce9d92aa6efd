from typing import Dict, List, Any, Callable
import ast
import types

class DynamicToolCreator:
    def __init__(self, tool_registry):
        self.registry = tool_registry
        self.created_tools = {}
        
    def create_tool_from_description(self, description: str, name: str) -> bool:
        """Create a tool from natural language description"""
        tool_code = self.generate_tool_code(description, name)
        
        if tool_code:
            return self.create_tool_from_code(tool_code, name, description)
        
        return False
    
    def generate_tool_code(self, description: str, name: str) -> str:
        """Generate Python code for tool based on description"""
        # Simple template-based code generation
        templates = {
            "calculator": self.create_calculator_tool,
            "search": self.create_search_tool,
            "formatter": self.create_formatter_tool,
            "validator": self.create_validator_tool
        }
        
        desc_lower = description.lower()
        
        if any(word in desc_lower for word in ["calculate", "math", "compute"]):
            return templates["calculator"](name, description)
        elif any(word in desc_lower for word in ["search", "find", "lookup"]):
            return templates["search"](name, description)
        elif any(word in desc_lower for word in ["format", "convert", "transform"]):
            return templates["formatter"](name, description)
        elif any(word in desc_lower for word in ["validate", "check", "verify"]):
            return templates["validator"](name, description)
        
        return self.create_generic_tool(name, description)
    
    def create_calculator_tool(self, name: str, description: str) -> str:
        """Create calculator tool code"""
        return f'''
def {name}(expression: str) -> float:
    """
    {description}
    """
    try:
        # Safe evaluation of mathematical expressions
        allowed_names = {{
            k: v for k, v in __builtins__.items()
            if k in ["abs", "round", "min", "max", "sum"]
        }}
        allowed_names.update({{"__builtins__": {{}}}})
        
        result = eval(expression, allowed_names)
        return float(result)
    except Exception as e:
        return f"Error: {{str(e)}}"
'''
    
    def create_search_tool(self, name: str, description: str) -> str:
        """Create search tool code"""
        return f'''
def {name}(query: str, data: list = None) -> list:
    """
    {description}
    """
    if not data:
        data = ["sample", "data", "items"]
    
    query_lower = query.lower()
    results = []
    
    for item in data:
        if isinstance(item, str) and query_lower in item.lower():
            results.append(item)
        elif isinstance(item, dict) and any(
            query_lower in str(v).lower() for v in item.values()
        ):
            results.append(item)
    
    return results
'''
    
    def create_formatter_tool(self, name: str, description: str) -> str:
        """Create formatter tool code"""
        return f'''
def {name}(text: str, format_type: str = "upper") -> str:
    """
    {description}
    """
    if format_type == "upper":
        return text.upper()
    elif format_type == "lower":
        return text.lower()
    elif format_type == "title":
        return text.title()
    elif format_type == "reverse":
        return text[::-1]
    else:
        return text
'''
    
    def create_validator_tool(self, name: str, description: str) -> str:
        """Create validator tool code"""
        return f'''
def {name}(value: str, validation_type: str = "email") -> bool:
    """
    {description}
    """
    import re
    
    if validation_type == "email":
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{{2,}}$'
        return bool(re.match(pattern, value))
    elif validation_type == "phone":
        pattern = r'^[+]?[1-9]?[0-9]{{7,15}}$'
        return bool(re.match(pattern, value.replace(" ", "").replace("-", "")))
    elif validation_type == "url":
        pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return bool(re.match(pattern, value))
    else:
        return len(value) > 0
'''
    
    def create_generic_tool(self, name: str, description: str) -> str:
        """Create generic tool code"""
        return f'''
def {name}(input_data: str) -> str:
    """
    {description}
    """
    return f"Processed: {{input_data}}"
'''
    
    def create_tool_from_code(self, code: str, name: str, description: str) -> bool:
        """Create and register tool from code"""
        try:
            # Parse and validate code
            parsed = ast.parse(code)
            
            # Execute code to create function
            namespace = {}
            exec(code, namespace)
            
            # Get the function
            func = namespace.get(name)
            if not func:
                return False
            
            # Register the tool
            parameters = self.extract_parameters_from_function(func)
            success = self.registry.register_tool(name, func, description, parameters)
            
            if success:
                self.created_tools[name] = {
                    "code": code,
                    "description": description,
                    "created_at": "now"
                }
            
            return success
            
        except Exception as e:
            print(f"Error creating tool: {e}")
            return False
    
    def extract_parameters_from_function(self, func: Callable) -> Dict[str, Any]:
        """Extract parameter information from function"""
        import inspect
        
        sig = inspect.signature(func)
        parameters = {}
        
        for param_name, param in sig.parameters.items():
            param_info = {
                "name": param_name,
                "type": str(param.annotation) if param.annotation != inspect.Parameter.empty else "Any",
                "required": param.default == inspect.Parameter.empty
            }
            
            if param.default != inspect.Parameter.empty:
                param_info["default"] = param.default
            
            parameters[param_name] = param_info
        
        return parameters
    
    def create_composite_tool(self, name: str, tool_names: List[str], description: str) -> bool:
        """Create a composite tool that combines multiple existing tools"""
        if not all(self.registry.get_tool(tool_name) for tool_name in tool_names):
            return False
        
        def composite_func(**kwargs):
            results = {}
            for tool_name in tool_names:
                try:
                    result = self.registry.execute_tool(tool_name, **kwargs)
                    results[tool_name] = result
                except Exception as e:
                    results[tool_name] = f"Error: {str(e)}"
            return results
        
        return self.registry.register_tool(name, composite_func, description)
    
    def get_created_tools(self) -> List[Dict[str, Any]]:
        """Get list of dynamically created tools"""
        return [
            {
                "name": name,
                "description": info["description"],
                "created_at": info["created_at"]
            }
            for name, info in self.created_tools.items()
        ]