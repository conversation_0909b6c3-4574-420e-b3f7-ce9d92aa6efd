"""Document search tool for knowledge retrieval."""

from typing import Dict, List, Any, Optional
import re

class DocumentSearchTool:
    def __init__(self, vector_search_service, graph_search_service):
        self.vector_search = vector_search_service
        self.graph_search = graph_search_service
    
    async def search_documents(self, query: str, filters: Optional[Dict[str, Any]] = None, 
                             search_type: str = "hybrid") -> Dict[str, Any]:
        """Search documents using various methods."""
        try:
            if search_type == "vector":
                return await self._vector_search(query, filters)
            elif search_type == "graph":
                return await self._graph_search(query, filters)
            elif search_type == "hybrid":
                return await self._hybrid_search(query, filters)
            else:
                return {"success": False, "error": f"Unknown search type: {search_type}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _vector_search(self, query: str, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Perform vector-based semantic search."""
        results = await self.vector_search.search(query, k=10, filters=filters)
        
        return {
            "success": True,
            "search_type": "vector",
            "results": results,
            "count": len(results)
        }
    
    async def _graph_search(self, query: str, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Perform graph-based relationship search."""
        results = await self.graph_search.search(query, filters)
        
        return {
            "success": True,
            "search_type": "graph",
            "results": results,
            "count": len(results)
        }
    
    async def _hybrid_search(self, query: str, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Perform hybrid search combining vector and graph results."""
        # Get results from both methods
        vector_results = await self._vector_search(query, filters)
        graph_results = await self._graph_search(query, filters)
        
        # Combine and rank results
        combined_results = self._combine_results(
            vector_results.get("results", []),
            graph_results.get("results", [])
        )
        
        return {
            "success": True,
            "search_type": "hybrid",
            "results": combined_results,
            "count": len(combined_results),
            "vector_count": len(vector_results.get("results", [])),
            "graph_count": len(graph_results.get("results", []))
        }
    
    def _combine_results(self, vector_results: List[Dict], graph_results: List[Dict]) -> List[Dict]:
        """Combine and rank results from different search methods."""
        # Create a map to avoid duplicates
        result_map = {}
        
        # Add vector results with vector score
        for result in vector_results:
            doc_id = result.get("id", result.get("document_id"))
            if doc_id:
                result["vector_score"] = result.get("similarity_score", 0.0)
                result_map[doc_id] = result
        
        # Add graph results with graph score
        for result in graph_results:
            doc_id = result.get("id", result.get("document_id"))
            if doc_id:
                if doc_id in result_map:
                    # Combine scores
                    result_map[doc_id]["graph_score"] = result.get("relevance_score", 0.0)
                    result_map[doc_id]["combined_score"] = (
                        result_map[doc_id].get("vector_score", 0.0) * 0.6 +
                        result.get("relevance_score", 0.0) * 0.4
                    )
                else:
                    result["graph_score"] = result.get("relevance_score", 0.0)
                    result["vector_score"] = 0.0
                    result["combined_score"] = result.get("relevance_score", 0.0) * 0.4
                    result_map[doc_id] = result
        
        # Sort by combined score
        combined_results = list(result_map.values())
        combined_results.sort(key=lambda x: x.get("combined_score", 0.0), reverse=True)
        
        return combined_results[:10]  # Return top 10
    
    async def search_by_category(self, category: str, limit: int = 10) -> Dict[str, Any]:
        """Search documents by category."""
        filters = {"category": category}
        return await self.search_documents("", filters=filters, search_type="vector")
    
    async def search_by_organization(self, organization: str, query: str = "") -> Dict[str, Any]:
        """Search documents specific to an organization."""
        filters = {"organization": organization}
        return await self.search_documents(query, filters=filters, search_type="hybrid")
    
    async def search_policies(self, policy_type: str = "", query: str = "") -> Dict[str, Any]:
        """Search policy documents."""
        filters = {"document_type": "policy"}
        if policy_type:
            filters["policy_type"] = policy_type
        
        return await self.search_documents(query, filters=filters, search_type="hybrid")
    
    async def find_related_documents(self, document_id: str) -> Dict[str, Any]:
        """Find documents related to a given document."""
        try:
            # Use graph search to find related documents
            results = await self.graph_search.find_related(document_id)
            
            return {
                "success": True,
                "document_id": document_id,
                "related_documents": results,
                "count": len(results)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text for search enhancement."""
        # Simple keyword extraction
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        
        # Remove common stop words
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [word for word in words if word not in stop_words]
        
        # Return unique keywords
        return list(set(keywords))
    
    async def search_with_context(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Search documents with additional context."""
        # Enhance query with context
        enhanced_query = self._enhance_query_with_context(query, context)
        
        # Create filters from context
        filters = {}
        if "organization" in context:
            filters["organization"] = context["organization"]
        if "department" in context:
            filters["department"] = context["department"]
        if "user_role" in context:
            filters["user_role"] = context["user_role"]
        
        return await self.search_documents(enhanced_query, filters=filters, search_type="hybrid")
    
    def _enhance_query_with_context(self, query: str, context: Dict[str, Any]) -> str:
        """Enhance query with contextual information."""
        enhanced_parts = [query]
        
        if "organization" in context:
            enhanced_parts.append(f"organization:{context['organization']}")
        
        if "department" in context:
            enhanced_parts.append(f"department:{context['department']}")
        
        if "user_role" in context:
            enhanced_parts.append(f"role:{context['user_role']}")
        
        return " ".join(enhanced_parts)