from typing import Dict, List, Any, Callable
import inspect
from datetime import datetime

class ToolRegistry:
    def __init__(self):
        self.tools = {}
        self.tool_usage_stats = {}
        
    def register_tool(self, name: str, func: Callable, description: str, 
                     parameters: Dict[str, Any] = None) -> bool:
        """Register a tool in the registry"""
        tool_info = {
            "name": name,
            "function": func,
            "description": description,
            "parameters": parameters or {},
            "signature": str(inspect.signature(func)),
            "registered_at": datetime.now().isoformat(),
            "usage_count": 0
        }
        
        self.tools[name] = tool_info
        self.tool_usage_stats[name] = {"calls": 0, "successes": 0, "failures": 0}
        return True
    
    def get_tool(self, name: str) -> Dict[str, Any]:
        """Get tool information"""
        return self.tools.get(name)
    
    def list_tools(self) -> List[Dict[str, Any]]:
        """List all registered tools"""
        return [
            {
                "name": tool["name"],
                "description": tool["description"],
                "parameters": tool["parameters"],
                "usage_count": tool["usage_count"]
            }
            for tool in self.tools.values()
        ]
    
    def execute_tool(self, name: str, **kwargs) -> Any:
        """Execute a registered tool"""
        if name not in self.tools:
            raise ValueError(f"Tool '{name}' not found")
        
        tool = self.tools[name]
        
        try:
            result = tool["function"](**kwargs)
            tool["usage_count"] += 1
            self.tool_usage_stats[name]["calls"] += 1
            self.tool_usage_stats[name]["successes"] += 1
            return result
        except Exception as e:
            self.tool_usage_stats[name]["calls"] += 1
            self.tool_usage_stats[name]["failures"] += 1
            raise e
    
    def search_tools(self, query: str) -> List[Dict[str, Any]]:
        """Search tools by description or name"""
        query_lower = query.lower()
        matching_tools = []
        
        for tool in self.tools.values():
            if (query_lower in tool["name"].lower() or 
                query_lower in tool["description"].lower()):
                matching_tools.append({
                    "name": tool["name"],
                    "description": tool["description"],
                    "relevance_score": self.calculate_relevance(query_lower, tool)
                })
        
        return sorted(matching_tools, key=lambda x: x["relevance_score"], reverse=True)
    
    def calculate_relevance(self, query: str, tool: Dict[str, Any]) -> float:
        """Calculate relevance score for tool search"""
        score = 0.0
        
        if query in tool["name"].lower():
            score += 1.0
        if query in tool["description"].lower():
            score += 0.5
        
        # Boost popular tools
        usage_boost = min(tool["usage_count"] / 10.0, 0.2)
        score += usage_boost
        
        return score
    
    def get_tool_stats(self) -> Dict[str, Any]:
        """Get tool usage statistics"""
        return {
            "total_tools": len(self.tools),
            "total_calls": sum(stats["calls"] for stats in self.tool_usage_stats.values()),
            "success_rate": self.calculate_success_rate(),
            "most_used_tools": self.get_most_used_tools(5)
        }
    
    def calculate_success_rate(self) -> float:
        """Calculate overall success rate"""
        total_calls = sum(stats["calls"] for stats in self.tool_usage_stats.values())
        total_successes = sum(stats["successes"] for stats in self.tool_usage_stats.values())
        
        return total_successes / total_calls if total_calls > 0 else 0.0
    
    def get_most_used_tools(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get most frequently used tools"""
        tool_usage = [
            {"name": name, "usage_count": self.tools[name]["usage_count"]}
            for name in self.tools
        ]
        
        return sorted(tool_usage, key=lambda x: x["usage_count"], reverse=True)[:limit]