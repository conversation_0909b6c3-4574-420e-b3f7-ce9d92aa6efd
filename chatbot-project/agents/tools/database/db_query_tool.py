"""Database query tool for agent database operations."""

import asyncpg
import json
from typing import Dict, List, Any, Optional

class DatabaseQueryTool:
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.connection = None
    
    async def connect(self):
        """Connect to database."""
        if not self.connection:
            self.connection = await asyncpg.connect(self.connection_string)
    
    async def execute_query(self, query: str, params: Optional[List] = None) -> Dict[str, Any]:
        """Execute a database query safely."""
        try:
            await self.connect()
            
            # Basic SQL injection protection
            if not self._is_safe_query(query):
                return {"success": False, "error": "Unsafe query detected"}
            
            if query.strip().upper().startswith('SELECT'):
                rows = await self.connection.fetch(query, *(params or []))
                return {
                    "success": True,
                    "data": [dict(row) for row in rows],
                    "row_count": len(rows)
                }
            else:
                result = await self.connection.execute(query, *(params or []))
                return {
                    "success": True,
                    "result": result,
                    "message": "Query executed successfully"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _is_safe_query(self, query: str) -> bool:
        """Basic safety check for SQL queries."""
        query_upper = query.upper()
        
        # Allow only SELECT, INSERT, UPDATE for agents
        allowed_operations = ['SELECT', 'INSERT', 'UPDATE']
        dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE']
        
        # Check if query starts with allowed operation
        starts_with_allowed = any(query_upper.strip().startswith(op) for op in allowed_operations)
        
        # Check for dangerous keywords
        has_dangerous = any(keyword in query_upper for keyword in dangerous_keywords)
        
        return starts_with_allowed and not has_dangerous
    
    async def get_user_data(self, user_id: str) -> Dict[str, Any]:
        """Get user data by ID."""
        query = "SELECT * FROM users WHERE id = $1"
        return await self.execute_query(query, [user_id])
    
    async def get_organization_data(self, org_id: str) -> Dict[str, Any]:
        """Get organization data by ID."""
        query = "SELECT * FROM organizations WHERE id = $1"
        return await self.execute_query(query, [org_id])
    
    async def search_policies(self, search_term: str) -> Dict[str, Any]:
        """Search policies by term."""
        query = """
        SELECT * FROM knowledge_documents 
        WHERE document_type = 'policy' 
        AND (title ILIKE $1 OR content ILIKE $1)
        LIMIT 10
        """
        return await self.execute_query(query, [f"%{search_term}%"])
    
    async def close(self):
        """Close database connection."""
        if self.connection:
            await self.connection.close()
            self.connection = None