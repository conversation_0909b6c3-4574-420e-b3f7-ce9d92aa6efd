"""Basic calculator tool for mathematical operations."""

import math
import operator
from typing import Union, Dict, Any

class BasicCalculator:
    def __init__(self):
        self.operations = {
            '+': operator.add,
            '-': operator.sub,
            '*': operator.mul,
            '/': operator.truediv,
            '**': operator.pow,
            '%': operator.mod,
            '//': operator.floordiv
        }
        
        self.functions = {
            'sqrt': math.sqrt,
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
            'log': math.log,
            'log10': math.log10,
            'exp': math.exp,
            'abs': abs,
            'round': round,
            'ceil': math.ceil,
            'floor': math.floor
        }
    
    def calculate(self, expression: str) -> Dict[str, Any]:
        """Calculate mathematical expression safely."""
        try:
            # Basic safety check
            allowed_chars = set('0123456789+-*/.()% abcdefghijklmnopqrstuvwxyz')
            if not all(c.lower() in allowed_chars for c in expression):
                return {"success": False, "error": "Invalid characters in expression"}
            
            # Replace function names
            processed_expr = expression
            for func_name in self.functions:
                if func_name in processed_expr:
                    processed_expr = processed_expr.replace(func_name, f"self.functions['{func_name}']")
            
            # Evaluate safely
            result = eval(processed_expr, {"__builtins__": {}, "self": self, "math": math})
            
            return {
                "success": True,
                "result": result,
                "expression": expression,
                "type": type(result).__name__
            }
            
        except ZeroDivisionError:
            return {"success": False, "error": "Division by zero"}
        except ValueError as e:
            return {"success": False, "error": f"Math error: {str(e)}"}
        except Exception as e:
            return {"success": False, "error": f"Calculation error: {str(e)}"}
    
    def add(self, a: float, b: float) -> float:
        """Add two numbers."""
        return a + b
    
    def subtract(self, a: float, b: float) -> float:
        """Subtract two numbers."""
        return a - b
    
    def multiply(self, a: float, b: float) -> float:
        """Multiply two numbers."""
        return a * b
    
    def divide(self, a: float, b: float) -> float:
        """Divide two numbers."""
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b
    
    def power(self, base: float, exponent: float) -> float:
        """Raise base to the power of exponent."""
        return base ** exponent
    
    def percentage(self, value: float, percentage: float) -> float:
        """Calculate percentage of a value."""
        return (value * percentage) / 100