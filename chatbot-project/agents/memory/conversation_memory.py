from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

class ConversationMemory:
    def __init__(self, max_history: int = 50):
        self.max_history = max_history
        self.conversations = {}
        self.current_context = {}
    
    def store_interaction(self, user_id: str, query: str, response: str, metadata: Dict = None):
        """Store conversation interaction"""
        if user_id not in self.conversations:
            self.conversations[user_id] = []
        
        interaction = {
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "response": response,
            "metadata": metadata or {}
        }
        
        self.conversations[user_id].append(interaction)
        
        # Maintain max history
        if len(self.conversations[user_id]) > self.max_history:
            self.conversations[user_id] = self.conversations[user_id][-self.max_history:]
    
    def get_conversation_history(self, user_id: str, limit: int = 10) -> List[Dict]:
        """Get recent conversation history"""
        if user_id not in self.conversations:
            return []
        
        return self.conversations[user_id][-limit:]
    
    def get_context_for_query(self, user_id: str, current_query: str) -> Dict[str, Any]:
        """Get relevant context for current query"""
        history = self.get_conversation_history(user_id, 5)
        
        context = {
            "recent_topics": self.extract_topics(history),
            "user_preferences": self.extract_preferences(history),
            "conversation_flow": self.analyze_flow(history, current_query)
        }
        
        return context
    
    def extract_topics(self, history: List[Dict]) -> List[str]:
        """Extract topics from conversation history"""
        topics = set()
        
        for interaction in history:
            query = interaction["query"].lower()
            
            # Simple topic extraction
            if "leave" in query or "policy" in query:
                topics.add("hr_policies")
            if "travel" in query:
                topics.add("travel_policies")
            if "committee" in query or "harassment" in query:
                topics.add("compliance")
            if "who is" in query or "person" in query:
                topics.add("personnel")
        
        return list(topics)
    
    def extract_preferences(self, history: List[Dict]) -> Dict[str, Any]:
        """Extract user preferences from history"""
        preferences = {
            "preferred_detail_level": "medium",
            "organization_focus": None,
            "response_style": "informative"
        }
        
        # Analyze history for preferences
        for interaction in history:
            query = interaction["query"].lower()
            
            if "nuvo ai" in query:
                preferences["organization_focus"] = "NUVO AI"
            elif "meril" in query:
                preferences["organization_focus"] = "Meril"
            
            if "detailed" in query or "explain" in query:
                preferences["preferred_detail_level"] = "high"
            elif "brief" in query or "quick" in query:
                preferences["preferred_detail_level"] = "low"
        
        return preferences
    
    def analyze_flow(self, history: List[Dict], current_query: str) -> Dict[str, Any]:
        """Analyze conversation flow"""
        if not history:
            return {"flow_type": "new_conversation"}
        
        last_interaction = history[-1]
        
        # Check for follow-up patterns
        if any(word in current_query.lower() for word in ["more", "also", "what about", "and"]):
            return {
                "flow_type": "follow_up",
                "related_to": last_interaction["query"]
            }
        
        # Check for clarification
        if any(word in current_query.lower() for word in ["clarify", "explain", "what do you mean"]):
            return {
                "flow_type": "clarification",
                "clarifying": last_interaction["response"]
            }
        
        return {"flow_type": "new_topic"}
    
    def update_context(self, user_id: str, context_updates: Dict[str, Any]):
        """Update current context for user"""
        if user_id not in self.current_context:
            self.current_context[user_id] = {}
        
        self.current_context[user_id].update(context_updates)
    
    def get_current_context(self, user_id: str) -> Dict[str, Any]:
        """Get current context for user"""
        return self.current_context.get(user_id, {})