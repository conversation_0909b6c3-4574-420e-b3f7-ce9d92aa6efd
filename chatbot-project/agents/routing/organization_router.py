"""Enhanced organization routing system."""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class RoutingDecision:
    organization: str
    confidence: float
    reasoning: str
    fallback_orgs: List[str]

class OrganizationRouter:
    def __init__(self):
        self.org_keywords = {
            "NUVO AI": ["nuvo", "ai", "artificial intelligence", "machine learning"],
            "Meril Life Sciences": ["meril", "life sciences", "medical", "healthcare"],
            "Meril Healthcare": ["healthcare", "medical services"],
            "Meril Diagnostics": ["diagnostics", "testing"],
            "Meril Endo Surgery": ["surgery", "endo", "surgical"]
        }
        
    def route_query(self, query: str, user_context: Dict[str, Any]) -> RoutingDecision:
        """Enhanced routing with confidence scoring."""
        scores = {}
        
        # User's primary organization gets base score
        user_org = user_context.get("organization", "")
        if user_org:
            scores[user_org] = 0.5
        
        # Keyword matching
        query_lower = query.lower()
        for org, keywords in self.org_keywords.items():
            keyword_score = sum(0.2 for kw in keywords if kw in query_lower)
            scores[org] = scores.get(org, 0) + keyword_score
        
        # Cross-org queries
        if "compare" in query_lower or "difference" in query_lower:
            return RoutingDecision(
                organization="multi_org",
                confidence=0.8,
                reasoning="Cross-organizational comparison detected",
                fallback_orgs=list(scores.keys())
            )
        
        # Select best organization
        if scores:
            best_org = max(scores, key=scores.get)
            confidence = min(scores[best_org], 1.0)
            fallback = [org for org, score in sorted(scores.items(), key=lambda x: x[1], reverse=True)[1:3]]
            
            return RoutingDecision(
                organization=best_org,
                confidence=confidence,
                reasoning=f"Matched keywords and user context",
                fallback_orgs=fallback
            )
        
        return RoutingDecision(
            organization=user_org or "general",
            confidence=0.3,
            reasoning="Default routing based on user organization",
            fallback_orgs=[]
        )