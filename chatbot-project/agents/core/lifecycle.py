"""
Agent lifecycle management.
"""

import asyncio
from typing import Dict
from datetime import datetime

from .base_agent import BaseAgent, AgentStatus
from .agent_registry import AgentRegistry


class AgentLifecycle:
    """Manages the lifecycle of agents in the system."""

    def __init__(self, registry: AgentRegistry):
        self.registry = registry
        self.running = False
        self._monitor_task = None

    async def start_agent(self, agent: BaseAgent) -> bool:
        """Start an agent and register it."""
        try:
            # Initialize the agent
            if not await agent.initialize():
                return False

            # Register in the registry
            if not await self.registry.register_agent(agent):
                await agent.shutdown()
                return False

            print(f"Agent {agent.agent_id} started successfully")
            return True

        except Exception as e:
            print(f"Failed to start agent {agent.agent_id}: {e}")
            return False

    async def stop_agent(self, agent_id: str) -> bool:
        """Stop an agent and unregister it."""
        try:
            agent = await self.registry.get_agent(agent_id)
            if not agent:
                return False

            # Shutdown the agent
            await agent.shutdown()

            # Unregister from registry
            await self.registry.unregister_agent(agent_id)

            print(f"Agent {agent_id} stopped successfully")
            return True

        except Exception as e:
            print(f"Failed to stop agent {agent_id}: {e}")
            return False

    async def restart_agent(self, agent_id: str) -> bool:
        """Restart an agent."""
        agent = await self.registry.get_agent(agent_id)
        if not agent:
            return False

        # Stop and start the agent
        if await self.stop_agent(agent_id):
            return await self.start_agent(agent)

        return False

    async def start_monitoring(self):
        """Start monitoring agent health."""
        if self.running:
            return

        self.running = True
        self._monitor_task = asyncio.create_task(self._monitor_agents())

    async def stop_monitoring(self):
        """Stop monitoring agent health."""
        self.running = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass

    async def _monitor_agents(self):
        """Monitor agent health and restart failed agents."""
        while self.running:
            try:
                agents = await self.registry.get_all_agents()

                for agent in agents:
                    if agent.status == AgentStatus.ERROR:
                        print(
                            f"Detected failed agent {agent.agent_id}, attempting restart..."
                        )
                        await self.restart_agent(agent.agent_id)

                # Sleep for 30 seconds before next check
                await asyncio.sleep(30)

            except Exception as e:
                print(f"Error in agent monitoring: {e}")
                await asyncio.sleep(10)

    async def get_system_status(self) -> Dict:
        """Get overall system status."""
        health = await self.registry.health_check()

        return {
            "monitoring_active": self.running,
            "agent_health": health,
            "timestamp": datetime.utcnow().isoformat(),
        }
