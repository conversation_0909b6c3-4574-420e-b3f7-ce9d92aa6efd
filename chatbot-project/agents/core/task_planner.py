from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class TaskPriority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class Task:
    task_id: str
    description: str
    task_type: str
    priority: TaskPriority
    dependencies: List[str]
    required_capabilities: List[str]
    parameters: Dict[str, Any]
    estimated_duration: int = 60  # seconds

class TaskPlanner:
    def __init__(self):
        self.task_templates = {}
        self.dependency_graph = {}
        
    def decompose_task(self, main_task: Dict[str, Any]) -> List[Task]:
        """Decompose complex task into subtasks"""
        query = main_task.get("query", "").lower()
        task_type = main_task.get("type", "general")
        
        subtasks = []
        
        # Knowledge retrieval tasks
        if any(word in query for word in ["what", "how", "policy", "information"]):
            subtasks.append(Task(
                task_id=f"knowledge_{len(subtasks)}",
                description="Retrieve relevant knowledge",
                task_type="knowledge_retrieval",
                priority=TaskPriority.HIGH,
                dependencies=[],
                required_capabilities=["knowledge_search", "document_retrieval"],
                parameters={"query": main_task.get("query"), "k": 5}
            ))
        
        # Reasoning tasks
        if any(word in query for word in ["compare", "analyze", "why", "explain"]):
            subtasks.append(Task(
                task_id=f"reasoning_{len(subtasks)}",
                description="Perform reasoning analysis",
                task_type="reasoning",
                priority=TaskPriority.MEDIUM,
                dependencies=[subtasks[0].task_id] if subtasks else [],
                required_capabilities=["logical_reasoning", "comparison"],
                parameters={"context": main_task.get("context", {})}
            ))
        
        # Tool execution tasks
        if any(word in query for word in ["calculate", "compute", "process"]):
            subtasks.append(Task(
                task_id=f"tool_{len(subtasks)}",
                description="Execute required tools",
                task_type="tool_execution",
                priority=TaskPriority.LOW,
                dependencies=[],
                required_capabilities=["tool_execution"],
                parameters={"tools_needed": self.identify_needed_tools(query)}
            ))
        
        return subtasks
    
    def identify_needed_tools(self, query: str) -> List[str]:
        """Identify tools needed for query"""
        tools = []
        if "calculate" in query or "math" in query:
            tools.append("calculator")
        if "search" in query:
            tools.append("searcher")
        return tools
    
    def create_execution_plan(self, tasks: List[Task]) -> Dict[str, Any]:
        """Create execution plan with dependencies"""
        plan = {
            "tasks": tasks,
            "execution_order": self.topological_sort(tasks),
            "parallel_groups": self.identify_parallel_tasks(tasks),
            "estimated_total_time": sum(task.estimated_duration for task in tasks)
        }
        return plan
    
    def topological_sort(self, tasks: List[Task]) -> List[str]:
        """Sort tasks by dependencies"""
        # Simple topological sort
        sorted_tasks = []
        remaining_tasks = {task.task_id: task for task in tasks}
        
        while remaining_tasks:
            # Find tasks with no dependencies
            ready_tasks = [
                task_id for task_id, task in remaining_tasks.items()
                if not task.dependencies or all(dep not in remaining_tasks for dep in task.dependencies)
            ]
            
            if not ready_tasks:
                # Circular dependency - break it
                ready_tasks = [list(remaining_tasks.keys())[0]]
            
            for task_id in ready_tasks:
                sorted_tasks.append(task_id)
                del remaining_tasks[task_id]
        
        return sorted_tasks
    
    def identify_parallel_tasks(self, tasks: List[Task]) -> List[List[str]]:
        """Identify tasks that can run in parallel"""
        parallel_groups = []
        task_dict = {task.task_id: task for task in tasks}
        
        # Group tasks with no dependencies between them
        processed = set()
        
        for task in tasks:
            if task.task_id in processed:
                continue
                
            group = [task.task_id]
            processed.add(task.task_id)
            
            # Find other tasks that can run with this one
            for other_task in tasks:
                if (other_task.task_id not in processed and
                    task.task_id not in other_task.dependencies and
                    other_task.task_id not in task.dependencies):
                    group.append(other_task.task_id)
                    processed.add(other_task.task_id)
            
            if len(group) > 1:
                parallel_groups.append(group)
        
        return parallel_groups