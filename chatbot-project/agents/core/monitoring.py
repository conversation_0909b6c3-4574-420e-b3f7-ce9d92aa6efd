from typing import Dict, List, Any
from datetime import datetime, timedelta
import asyncio

class AgentMonitor:
    def __init__(self):
        self.metrics = {}
        self.alerts = []
        self.performance_history = {}
        self.health_checks = {}
        
    def record_agent_performance(self, agent_id: str, task_id: str, 
                                execution_time: float, success: bool, 
                                error_message: str = None):
        """Record agent performance metrics"""
        if agent_id not in self.metrics:
            self.metrics[agent_id] = {
                "total_tasks": 0,
                "successful_tasks": 0,
                "failed_tasks": 0,
                "total_execution_time": 0.0,
                "average_execution_time": 0.0,
                "error_rate": 0.0,
                "last_activity": None
            }
        
        metrics = self.metrics[agent_id]
        metrics["total_tasks"] += 1
        metrics["total_execution_time"] += execution_time
        metrics["average_execution_time"] = metrics["total_execution_time"] / metrics["total_tasks"]
        metrics["last_activity"] = datetime.now().isoformat()
        
        if success:
            metrics["successful_tasks"] += 1
        else:
            metrics["failed_tasks"] += 1
            if error_message:
                self.record_error(agent_id, task_id, error_message)
        
        metrics["error_rate"] = metrics["failed_tasks"] / metrics["total_tasks"]
        
        # Store in history
        if agent_id not in self.performance_history:
            self.performance_history[agent_id] = []
        
        self.performance_history[agent_id].append({
            "timestamp": datetime.now().isoformat(),
            "task_id": task_id,
            "execution_time": execution_time,
            "success": success,
            "error_message": error_message
        })
    
    def record_error(self, agent_id: str, task_id: str, error_message: str):
        """Record agent error"""
        error_record = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": agent_id,
            "task_id": task_id,
            "error_message": error_message,
            "severity": self.classify_error_severity(error_message)
        }
        
        # Add to alerts if severe
        if error_record["severity"] in ["high", "critical"]:
            self.alerts.append(error_record)
    
    def classify_error_severity(self, error_message: str) -> str:
        """Classify error severity"""
        error_lower = error_message.lower()
        
        if any(word in error_lower for word in ["critical", "fatal", "crash"]):
            return "critical"
        elif any(word in error_lower for word in ["error", "exception", "failed"]):
            return "high"
        elif any(word in error_lower for word in ["warning", "timeout"]):
            return "medium"
        else:
            return "low"
    
    async def perform_health_check(self, agent_id: str, agent_instance) -> Dict[str, Any]:
        """Perform health check on agent"""
        try:
            start_time = datetime.now()
            
            # Basic health check
            status = agent_instance.get_status()
            
            # Response time check
            response_time = (datetime.now() - start_time).total_seconds()
            
            health_status = {
                "agent_id": agent_id,
                "status": status.get("status", "unknown"),
                "response_time": response_time,
                "last_check": datetime.now().isoformat(),
                "healthy": response_time < 5.0 and status.get("status") != "error"
            }
            
            self.health_checks[agent_id] = health_status
            return health_status
            
        except Exception as e:
            health_status = {
                "agent_id": agent_id,
                "status": "error",
                "response_time": None,
                "last_check": datetime.now().isoformat(),
                "healthy": False,
                "error": str(e)
            }
            
            self.health_checks[agent_id] = health_status
            return health_status
    
    def get_agent_metrics(self, agent_id: str) -> Dict[str, Any]:
        """Get metrics for specific agent"""
        return self.metrics.get(agent_id, {})
    
    def get_system_overview(self) -> Dict[str, Any]:
        """Get system-wide monitoring overview"""
        total_agents = len(self.metrics)
        total_tasks = sum(metrics["total_tasks"] for metrics in self.metrics.values())
        total_errors = sum(metrics["failed_tasks"] for metrics in self.metrics.values())
        
        # Calculate system-wide error rate
        system_error_rate = total_errors / total_tasks if total_tasks > 0 else 0
        
        # Get recent alerts
        recent_alerts = [
            alert for alert in self.alerts
            if datetime.fromisoformat(alert["timestamp"]) > datetime.now() - timedelta(hours=1)
        ]
        
        return {
            "total_agents": total_agents,
            "total_tasks_processed": total_tasks,
            "system_error_rate": system_error_rate,
            "recent_alerts": len(recent_alerts),
            "healthy_agents": sum(1 for hc in self.health_checks.values() if hc.get("healthy", False)),
            "timestamp": datetime.now().isoformat()
        }
    
    def get_performance_trends(self, agent_id: str, hours: int = 24) -> Dict[str, Any]:
        """Get performance trends for agent"""
        if agent_id not in self.performance_history:
            return {"error": "No performance history found"}
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_history = [
            record for record in self.performance_history[agent_id]
            if datetime.fromisoformat(record["timestamp"]) > cutoff_time
        ]
        
        if not recent_history:
            return {"error": "No recent performance data"}
        
        # Calculate trends
        execution_times = [record["execution_time"] for record in recent_history]
        success_rate = sum(1 for record in recent_history if record["success"]) / len(recent_history)
        
        return {
            "agent_id": agent_id,
            "time_period_hours": hours,
            "total_tasks": len(recent_history),
            "success_rate": success_rate,
            "avg_execution_time": sum(execution_times) / len(execution_times),
            "min_execution_time": min(execution_times),
            "max_execution_time": max(execution_times)
        }
    
    def clear_old_data(self, days: int = 7):
        """Clear old monitoring data"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        # Clear old performance history
        for agent_id in self.performance_history:
            self.performance_history[agent_id] = [
                record for record in self.performance_history[agent_id]
                if datetime.fromisoformat(record["timestamp"]) > cutoff_time
            ]
        
        # Clear old alerts
        self.alerts = [
            alert for alert in self.alerts
            if datetime.fromisoformat(alert["timestamp"]) > cutoff_time
        ]