"""
Agent registry for managing and discovering agents.
"""

import asyncio
from typing import Dict, List, Optional, Set
from datetime import datetime

from .base_agent import BaseAgent, AgentStatus


class AgentRegistry:
    """Registry for managing agent instances and capabilities."""

    def __init__(self):
        self._agents: Dict[str, BaseAgent] = {}
        self._capabilities_index: Dict[str, Set[str]] = {}
        self._type_index: Dict[str, Set[str]] = {}
        self._lock = asyncio.Lock()

    async def register_agent(self, agent: BaseAgent) -> bool:
        """Register an agent in the registry."""
        async with self._lock:
            if agent.agent_id in self._agents:
                return False

            self._agents[agent.agent_id] = agent

            # Index by capabilities
            for capability in agent.capabilities:
                if capability not in self._capabilities_index:
                    self._capabilities_index[capability] = set()
                self._capabilities_index[capability].add(agent.agent_id)

            # Index by type (extracted from agent_id or class name)
            agent_type = self._extract_agent_type(agent)
            if agent_type not in self._type_index:
                self._type_index[agent_type] = set()
            self._type_index[agent_type].add(agent.agent_id)

            return True

    async def unregister_agent(self, agent_id: str) -> bool:
        """Unregister an agent from the registry."""
        async with self._lock:
            if agent_id not in self._agents:
                return False

            agent = self._agents[agent_id]

            # Remove from capabilities index
            for capability in agent.capabilities:
                if capability in self._capabilities_index:
                    self._capabilities_index[capability].discard(agent_id)
                    if not self._capabilities_index[capability]:
                        del self._capabilities_index[capability]

            # Remove from type index
            agent_type = self._extract_agent_type(agent)
            if agent_type in self._type_index:
                self._type_index[agent_type].discard(agent_id)
                if not self._type_index[agent_type]:
                    del self._type_index[agent_type]

            del self._agents[agent_id]
            return True

    async def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """Get an agent by ID."""
        return self._agents.get(agent_id)

    async def find_agents_by_capability(self, capability: str) -> List[BaseAgent]:
        """Find agents that have a specific capability."""
        agent_ids = self._capabilities_index.get(capability, set())
        return [
            self._agents[agent_id]
            for agent_id in agent_ids
            if agent_id in self._agents
            and self._agents[agent_id].status == AgentStatus.ACTIVE
        ]

    async def find_agents_by_type(self, agent_type: str) -> List[BaseAgent]:
        """Find agents of a specific type."""
        agent_ids = self._type_index.get(agent_type, set())
        return [
            self._agents[agent_id]
            for agent_id in agent_ids
            if agent_id in self._agents
            and self._agents[agent_id].status == AgentStatus.ACTIVE
        ]

    async def get_all_agents(self) -> List[BaseAgent]:
        """Get all registered agents."""
        return list(self._agents.values())

    async def get_active_agents(self) -> List[BaseAgent]:
        """Get all active agents."""
        return [
            agent
            for agent in self._agents.values()
            if agent.status == AgentStatus.ACTIVE
        ]

    async def health_check(self) -> Dict[str, int]:
        """Get health status of all agents."""
        status_counts = {}
        for agent in self._agents.values():
            status = agent.status.value
            status_counts[status] = status_counts.get(status, 0) + 1

        return {
            "total_agents": len(self._agents),
            "status_breakdown": status_counts,
            "last_check": datetime.utcnow().isoformat(),
        }

    def _extract_agent_type(self, agent: BaseAgent) -> str:
        """Extract agent type from agent instance."""
        # Try to extract from agent_id first (e.g., "coordinator_001" -> "coordinator")
        if "_" in agent.agent_id:
            return agent.agent_id.split("_")[0]

        # Fall back to class name
        class_name = agent.__class__.__name__.lower()
        if class_name.endswith("agent"):
            return class_name[:-5]  # Remove "agent" suffix

        return class_name
