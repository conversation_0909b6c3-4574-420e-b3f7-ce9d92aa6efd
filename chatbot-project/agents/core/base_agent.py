from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import uuid
from datetime import datetime
from enum import Enum

class AgentStatus(Enum):
    INITIALIZING = "initializing"
    IDLE = "idle"
    BUSY = "busy"
    ACTIVE = "active"
    ERROR = "error"
    OFFLINE = "offline"

class BaseAgent(ABC):
    def __init__(self, agent_id: str = None, name: str = None):
        self.agent_id = agent_id or str(uuid.uuid4())
        self.name = name or self.__class__.__name__
        self.status = AgentStatus.INITIALIZING
        self.capabilities = []
        self.memory = {}
        self.running = False
        
    @abstractmethod
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process a task and return results"""
        pass
    
    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """Return list of agent capabilities"""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status.value,
            "capabilities": self.capabilities,
            "timestamp": datetime.now().isoformat()
        }
    
    def update_status(self, status: AgentStatus):
        """Update agent status"""
        self.status = status
    
    def store_memory(self, key: str, value: Any):
        """Store information in agent memory"""
        self.memory[key] = value
    
    def retrieve_memory(self, key: str) -> Any:
        """Retrieve information from agent memory"""
        return self.memory.get(key)
    
    async def initialize(self) -> bool:
        """Initialize the agent"""
        try:
            await self._setup()
            self.status = AgentStatus.ACTIVE
            self.running = True
            return True
        except Exception as e:
            self.status = AgentStatus.ERROR
            return False
    
    async def _setup(self):
        """Setup method to be overridden by subclasses"""
        pass
    
    async def shutdown(self):
        """Shutdown the agent"""
        self.status = AgentStatus.OFFLINE
        self.running = False