"""
Agent communication protocols and message definitions.
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from enum import Enum


class MessageType(str, Enum):
    REQUEST = "request"
    RESPONSE = "response"
    BROADCAST = "broadcast"
    NOTIFICATION = "notification"
    TASK_ASSIGNMENT = "task_assignment"
    TASK_RESULT = "task_result"


class Priority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class AgentMessage(BaseModel):
    """Standard message format for agent communication."""

    message_id: str
    sender_id: str
    recipient_id: str
    message_type: MessageType
    priority: Priority = Priority.MEDIUM
    content: Dict[str, Any]
    metadata: Dict[str, Any] = {}
    timestamp: datetime
    ttl: Optional[int] = None  # Time to live in seconds
    requires_response: bool = False
    conversation_id: Optional[str] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class TaskMessage(BaseModel):
    """Specialized message for task assignments."""

    task_id: str
    task_type: str
    query: str
    context: Dict[str, Any] = {}
    requirements: Dict[str, Any] = {}
    deadline: Optional[datetime] = None


class ResultMessage(BaseModel):
    """Specialized message for task results."""

    task_id: str
    success: bool
    result: Dict[str, Any] = {}
    error: Optional[str] = None
    confidence: Optional[float] = None
    reasoning_trace: Optional[List[Dict[str, Any]]] = None
