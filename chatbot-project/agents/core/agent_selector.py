from typing import Dict, List, Any, Optional
from .base_agent import BaseAgent
from .task_planner import Task

class AgentSelector:
    def __init__(self, agent_registry):
        self.registry = agent_registry
        self.selection_history = []
        
    async def select_agents_for_tasks(self, tasks: List[Task]) -> Dict[str, str]:
        """Select best agents for each task"""
        assignments = {}
        
        for task in tasks:
            best_agent = await self.select_best_agent(task)
            if best_agent:
                assignments[task.task_id] = best_agent.agent_id
        
        return assignments
    
    async def select_best_agent(self, task: Task) -> Optional[BaseAgent]:
        """Select the best agent for a specific task"""
        # Get agents with required capabilities
        candidate_agents = []
        
        for capability in task.required_capabilities:
            agents = await self.registry.find_agents_by_capability(capability)
            candidate_agents.extend(agents)
        
        if not candidate_agents:
            return None
        
        # Remove duplicates
        candidate_agents = list(set(candidate_agents))
        
        # Score agents based on suitability
        scored_agents = []
        for agent in candidate_agents:
            score = self.calculate_agent_score(agent, task)
            scored_agents.append((agent, score))
        
        # Sort by score and return best
        scored_agents.sort(key=lambda x: x[1], reverse=True)
        
        return scored_agents[0][0] if scored_agents else None
    
    def calculate_agent_score(self, agent: BaseAgent, task: Task) -> float:
        """Calculate suitability score for agent-task pair"""
        score = 0.0
        
        # Capability match score
        matching_capabilities = set(agent.capabilities) & set(task.required_capabilities)
        capability_score = len(matching_capabilities) / len(task.required_capabilities)
        score += capability_score * 0.4
        
        # Agent availability score
        if agent.status == "idle":
            score += 0.3
        elif agent.status == "busy":
            score += 0.1
        
        # Agent specialization score
        if task.task_type in agent.capabilities:
            score += 0.2
        
        # Historical performance score
        historical_score = self.get_historical_performance(agent.agent_id, task.task_type)
        score += historical_score * 0.1
        
        return score
    
    def get_historical_performance(self, agent_id: str, task_type: str) -> float:
        """Get historical performance score for agent on task type"""
        # Simple mock implementation
        performance_data = {
            "KnowledgeAgent": {"knowledge_retrieval": 0.9, "reasoning": 0.6},
            "ReasoningAgent": {"reasoning": 0.9, "knowledge_retrieval": 0.5},
            "ToolAgent": {"tool_execution": 0.8, "reasoning": 0.4}
        }
        
        return performance_data.get(agent_id, {}).get(task_type, 0.5)
    
    def record_assignment(self, task_id: str, agent_id: str, result: Dict[str, Any]):
        """Record assignment result for learning"""
        self.selection_history.append({
            "task_id": task_id,
            "agent_id": agent_id,
            "result": result,
            "timestamp": "now"
        })
    
    def get_selection_stats(self) -> Dict[str, Any]:
        """Get agent selection statistics"""
        if not self.selection_history:
            return {"total_assignments": 0}
        
        agent_counts = {}
        success_counts = {}
        
        for record in self.selection_history:
            agent_id = record["agent_id"]
            agent_counts[agent_id] = agent_counts.get(agent_id, 0) + 1
            
            if record["result"].get("success", False):
                success_counts[agent_id] = success_counts.get(agent_id, 0) + 1
        
        return {
            "total_assignments": len(self.selection_history),
            "agent_usage": agent_counts,
            "success_rates": {
                agent_id: success_counts.get(agent_id, 0) / count
                for agent_id, count in agent_counts.items()
            }
        }