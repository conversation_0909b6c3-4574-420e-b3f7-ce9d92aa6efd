import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.core.base_agent import BaseAgent
from typing import Dict, List, Any

class OrganizationAgent(BaseAgent):
    def __init__(self, organization_name: str):
        super().__init__(name=f"OrganizationAgent_{organization_name}")
        self.organization = organization_name
        self.capabilities = ["organization_knowledge", "policy_interpretation", "org_specific_search"]
        self.org_knowledge = self.load_organization_knowledge()
        
    def load_organization_knowledge(self) -> Dict[str, Any]:
        """Load organization-specific knowledge"""
        knowledge_base = {
            "NUVO AI": {
                "policies": ["privilege_leave", "sexual_harassment", "travel_policy"],
                "key_personnel": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dr<PERSON>"],
                "departments": ["HR", "Engineering", "AI/ML"],
                "specialties": ["AI development", "Machine Learning", "Software Engineering"]
            },
            "Meril Life Sciences": {
                "policies": ["privilege_leave", "maternity_leave", "travel_policy", "sexual_harassment"],
                "key_personnel": ["Anita Nagar", "<PERSON><PERSON><PERSON> Nair"],
                "departments": ["HR", "R&D", "Manufacturing", "Quality"],
                "specialties": ["Medical devices", "Healthcare", "Life sciences"]
            },
            "Meril Healthcare": {
                "policies": ["group_policies", "sexual_harassment"],
                "key_personnel": ["Pallabi Sarkar"],
                "departments": ["HR", "Operations"],
                "specialties": ["Healthcare services", "Medical support"]
            }
        }
        
        return knowledge_base.get(self.organization, {})
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process organization-specific task"""
        self.update_status("processing")
        
        query = task.get("query", "")
        context = task.get("context", {})
        
        try:
            # Check if query is relevant to this organization
            relevance = self.calculate_relevance(query, context)
            
            if relevance < 0.3:
                return {
                    "results": [],
                    "relevance": relevance,
                    "message": f"Query not relevant to {self.organization}",
                    "agent": self.name
                }
            
            # Process organization-specific query
            results = await self.process_org_query(query, context)
            
            self.update_status("idle")
            return {
                "results": results,
                "organization": self.organization,
                "relevance": relevance,
                "confidence": self.calculate_confidence(results),
                "agent": self.name
            }
            
        except Exception as e:
            self.update_status("error")
            return {"error": str(e), "agent": self.name}
    
    def calculate_relevance(self, query: str, context: Dict[str, Any]) -> float:
        """Calculate query relevance to organization"""
        query_lower = query.lower()
        org_lower = self.organization.lower()
        
        relevance = 0.0
        
        # Direct organization mention
        if org_lower in query_lower:
            relevance += 0.5
        
        # Context organization match
        if context.get("organization", "").lower() == org_lower:
            relevance += 0.4
        
        # Policy/personnel mentions
        for policy in self.org_knowledge.get("policies", []):
            if policy.replace("_", " ") in query_lower:
                relevance += 0.2
                break
        
        for person in self.org_knowledge.get("key_personnel", []):
            if person.lower() in query_lower:
                relevance += 0.3
                break
        
        return min(relevance, 1.0)
    
    async def process_org_query(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Process organization-specific query"""
        results = []
        query_lower = query.lower()
        
        # Policy queries
        if "policy" in query_lower:
            policy_results = self.search_policies(query_lower)
            results.extend(policy_results)
        
        # Personnel queries
        if any(word in query_lower for word in ["who", "person", "contact"]):
            personnel_results = self.search_personnel(query_lower)
            results.extend(personnel_results)
        
        # Department queries
        if "department" in query_lower:
            dept_results = self.search_departments(query_lower)
            results.extend(dept_results)
        
        # General organization info
        if not results:
            results.append({
                "text": f"General information about {self.organization}: {self.get_org_summary()}",
                "score": 0.6,
                "type": "organization_info"
            })
        
        return results
    
    def search_policies(self, query: str) -> List[Dict[str, Any]]:
        """Search organization policies"""
        results = []
        policies = self.org_knowledge.get("policies", [])
        
        for policy in policies:
            if policy.replace("_", " ") in query:
                policy_info = self.get_policy_details(policy)
                results.append({
                    "text": f"{self.organization} {policy} policy: {policy_info}",
                    "score": 0.8,
                    "type": "policy",
                    "policy_name": policy
                })
        
        return results
    
    def search_personnel(self, query: str) -> List[Dict[str, Any]]:
        """Search organization personnel"""
        results = []
        personnel = self.org_knowledge.get("key_personnel", [])
        
        for person in personnel:
            if person.lower() in query:
                person_info = self.get_person_details(person)
                results.append({
                    "text": f"{person} at {self.organization}: {person_info}",
                    "score": 0.9,
                    "type": "personnel",
                    "person_name": person
                })
        
        return results
    
    def search_departments(self, query: str) -> List[Dict[str, Any]]:
        """Search organization departments"""
        results = []
        departments = self.org_knowledge.get("departments", [])
        
        for dept in departments:
            if dept.lower() in query:
                results.append({
                    "text": f"{dept} department at {self.organization}",
                    "score": 0.7,
                    "type": "department",
                    "department_name": dept
                })
        
        return results
    
    def get_policy_details(self, policy: str) -> str:
        """Get policy details"""
        policy_details = {
            "privilege_leave": "30 days annual leave with specific eligibility criteria",
            "sexual_harassment": "Prevention and redressal committee established",
            "travel_policy": "Guidelines for domestic and international travel",
            "maternity_leave": "26 weeks paid leave for maximum 2 children"
        }
        
        return policy_details.get(policy, "Policy details available in documentation")
    
    def get_person_details(self, person: str) -> str:
        """Get person details"""
        person_details = {
            "Ankita Desai": "Policy Drafter at NUVO AI",
            "Dr. Harshadkumar Panjkar": "Policy Approver at NUVO AI",
            "Anita Nagar": "Presiding Officer, Sexual Harassment Committee at Meril",
            "Pallabi Sarkar": "DGM at Meril Healthcare"
        }
        
        return person_details.get(person, "Key personnel member")
    
    def get_org_summary(self) -> str:
        """Get organization summary"""
        specialties = ", ".join(self.org_knowledge.get("specialties", []))
        return f"Specializes in {specialties}" if specialties else "Multi-domain organization"
    
    def calculate_confidence(self, results: List[Dict[str, Any]]) -> float:
        """Calculate confidence in results"""
        if not results:
            return 0.0
        
        scores = [r.get("score", 0) for r in results]
        return sum(scores) / len(scores)
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities