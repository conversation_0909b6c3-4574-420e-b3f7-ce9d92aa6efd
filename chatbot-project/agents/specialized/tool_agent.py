import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.core.base_agent import BaseAgent
from agents.tools.registry import ToolRegistry
from typing import Dict, List, Any

class ToolAgent(BaseAgent):
    def __init__(self):
        super().__init__(name="ToolAgent")
        self.tool_registry = ToolRegistry()
        self.capabilities = ["tool_execution", "tool_creation", "tool_discovery"]
        self.initialize_default_tools()
    
    def initialize_default_tools(self):
        """Initialize default tools"""
        # Calculator tool
        def calculator(expression: str) -> str:
            try:
                result = eval(expression, {"__builtins__": {}})
                return str(result)
            except:
                return "Invalid expression"
        
        # Text processor tool
        def text_processor(text: str, operation: str = "upper") -> str:
            if operation == "upper":
                return text.upper()
            elif operation == "lower":
                return text.lower()
            elif operation == "reverse":
                return text[::-1]
            return text
        
        # List searcher tool
        def list_searcher(items: List[str], query: str) -> List[str]:
            return [item for item in items if query.lower() in item.lower()]
        
        # Register default tools using ToolDefinition
        from agents.tools.registry import ToolDefinition, ToolType
        
        calc_tool = ToolDefinition(
            name="calculator",
            type=ToolType.CALCULATOR,
            description="Perform mathematical calculations",
            parameters={"expression": {"type": "str", "required": True}},
            returns={"type": "str", "description": "Calculation result"},
            function=calculator,
            examples=[{"input": "2+2", "output": "4"}],
            metadata={}
        )
        
        text_tool = ToolDefinition(
            name="text_processor",
            type=ToolType.DATA_PROCESSOR,
            description="Process text with various operations",
            parameters={
                "text": {"type": "str", "required": True},
                "operation": {"type": "str", "required": False, "default": "upper"}
            },
            returns={"type": "str", "description": "Processed text"},
            function=text_processor,
            examples=[{"input": {"text": "hello", "operation": "upper"}, "output": "HELLO"}],
            metadata={}
        )
        
        search_tool = ToolDefinition(
            name="list_searcher",
            type=ToolType.DATA_PROCESSOR,
            description="Search items in a list",
            parameters={
                "items": {"type": "List[str]", "required": True},
                "query": {"type": "str", "required": True}
            },
            returns={"type": "List[str]", "description": "Matching items"},
            function=list_searcher,
            examples=[{"input": {"items": ["apple", "banana"], "query": "app"}, "output": ["apple"]}],
            metadata={}
        )
        
        self.tool_registry.register_tool(calc_tool)
        self.tool_registry.register_tool(text_tool)
        self.tool_registry.register_tool(search_tool)
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process tool-related task"""
        self.update_status("processing")
        
        task_type = task.get("type", "tool_execution")
        
        try:
            if task_type == "tool_execution":
                result = await self.execute_tool_task(task)
            elif task_type == "tool_creation":
                result = await self.create_tool_task(task)
            elif task_type == "tool_discovery":
                result = await self.discover_tools_task(task)
            else:
                result = {"error": f"Unknown task type: {task_type}"}
            
            self.update_status("idle")
            return result
            
        except Exception as e:
            self.update_status("error")
            return {"error": str(e), "agent": self.name}
    
    async def execute_tool_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a specific tool"""
        tool_name = task.get("tool_name")
        tool_params = task.get("parameters", {})
        
        if not tool_name:
            return {"error": "No tool name specified"}
        
        try:
            result = self.tool_registry.execute_tool(tool_name, **tool_params)
            
            return {
                "results": [{"text": f"Tool '{tool_name}' result: {result}", "score": 1.0}],
                "tool_used": tool_name,
                "tool_result": result,
                "confidence": 0.9,
                "agent": self.name
            }
            
        except Exception as e:
            return {
                "error": f"Tool execution failed: {str(e)}",
                "tool_name": tool_name,
                "agent": self.name
            }
    
    async def create_tool_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new tool dynamically"""
        tool_description = task.get("description", "")
        tool_name = task.get("name", f"dynamic_tool_{len(self.tool_creator.created_tools)}")
        
        if not tool_description:
            return {"error": "No tool description provided"}
        
        success = self.tool_creator.create_tool_from_description(tool_description, tool_name)
        
        if success:
            return {
                "results": [{"text": f"Successfully created tool '{tool_name}'", "score": 1.0}],
                "tool_created": tool_name,
                "description": tool_description,
                "confidence": 0.8,
                "agent": self.name
            }
        else:
            return {
                "error": f"Failed to create tool '{tool_name}'",
                "description": tool_description,
                "agent": self.name
            }
    
    async def discover_tools_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Discover available tools"""
        query = task.get("query", "")
        
        if query:
            tools = self.tool_registry.search_tools(query)
        else:
            tools = self.tool_registry.list_tools()
        
        return {
            "results": [
                {"text": f"Tool: {tool['name']} - {tool['description']}", "score": 0.8}
                for tool in tools[:5]
            ],
            "available_tools": tools,
            "total_tools": len(tools),
            "confidence": 0.9,
            "agent": self.name
        }
    
    def get_tool_suggestions(self, query: str) -> List[Dict[str, Any]]:
        """Get tool suggestions based on query"""
        suggestions = []
        
        query_lower = query.lower()
        
        # Suggest tools based on query content
        if any(word in query_lower for word in ["calculate", "math", "compute"]):
            suggestions.append({
                "tool": "calculator",
                "reason": "Query involves mathematical operations",
                "confidence": 0.9
            })
        
        if any(word in query_lower for word in ["search", "find", "lookup"]):
            suggestions.append({
                "tool": "list_searcher",
                "reason": "Query involves searching",
                "confidence": 0.8
            })
        
        if any(word in query_lower for word in ["format", "convert", "transform"]):
            suggestions.append({
                "tool": "text_processor",
                "reason": "Query involves text processing",
                "confidence": 0.7
            })
        
        return suggestions
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities
    
    def get_tool_stats(self) -> Dict[str, Any]:
        """Get tool usage statistics"""
        return self.tool_registry.get_tool_stats()