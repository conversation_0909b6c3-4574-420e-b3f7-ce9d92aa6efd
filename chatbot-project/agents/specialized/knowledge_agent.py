import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.core.base_agent import BaseAgent
from ai.knowledge.fusion.knowledge_fusion import KnowledgeFusion
from typing import Dict, List, Any

class KnowledgeAgent(BaseAgent):
    def __init__(self):
        super().__init__(name="KnowledgeAgent")
        self.knowledge_fusion = KnowledgeFusion()
        self.capabilities = ["knowledge_search", "document_retrieval", "cross_validation"]
        self.initialized = False
    
    async def initialize(self, documents: List[Dict]):
        """Initialize knowledge systems"""
        if not self.initialized:
            self.knowledge_fusion.initialize(documents)
            self.initialized = True
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process knowledge retrieval task"""
        if not self.initialized:
            return {"error": "Knowledge agent not initialized"}
        
        self.update_status("searching")
        
        query = task.get("query", "")
        user_context = task.get("context", {})
        k = task.get("k", 5)
        
        try:
            # Perform fused knowledge search
            results = self.knowledge_fusion.fused_search(query, k, user_context)
            
            # Get knowledge summary
            summary = self.knowledge_fusion.get_knowledge_summary(query, user_context)
            
            self.update_status("idle")
            
            return {
                "results": results,
                "summary": summary,
                "confidence": self.calculate_confidence(results),
                "agent": self.name
            }
            
        except Exception as e:
            self.update_status("error")
            return {"error": str(e), "agent": self.name}
    
    def calculate_confidence(self, results: List[Dict]) -> float:
        """Calculate confidence based on search results"""
        if not results:
            return 0.0
        
        scores = [r.get("fusion_score", r.get("score", 0)) for r in results]
        return sum(scores) / len(scores) if scores else 0.0
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities