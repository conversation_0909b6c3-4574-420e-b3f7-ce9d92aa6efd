import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.core.base_agent import BaseAgent
from typing import Dict, List, Any

class CriticAgent(BaseAgent):
    def __init__(self):
        super().__init__(name="CriticAgent")
        self.capabilities = ["response_evaluation", "fact_checking", "consistency_verification", "quality_assessment"]
        self.evaluation_criteria = {
            "accuracy": 0.3,
            "completeness": 0.25,
            "relevance": 0.2,
            "consistency": 0.15,
            "clarity": 0.1
        }
        
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process response evaluation task"""
        self.update_status("evaluating")
        
        task_type = task.get("type", "response_evaluation")
        
        try:
            if task_type == "response_evaluation":
                result = await self.evaluate_response(task)
            elif task_type == "fact_checking":
                result = await self.fact_check(task)
            elif task_type == "consistency_check":
                result = await self.check_consistency(task)
            else:
                result = await self.general_evaluation(task)
            
            self.update_status("idle")
            return result
            
        except Exception as e:
            self.update_status("error")
            return {"error": str(e), "agent": self.name}
    
    async def evaluate_response(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate response quality"""
        response_text = task.get("response", "")
        original_query = task.get("query", "")
        context = task.get("context", {})
        
        evaluation = {
            "accuracy": self.evaluate_accuracy(response_text, context),
            "completeness": self.evaluate_completeness(response_text, original_query),
            "relevance": self.evaluate_relevance(response_text, original_query),
            "consistency": self.evaluate_consistency(response_text),
            "clarity": self.evaluate_clarity(response_text)
        }
        
        # Calculate overall score
        overall_score = sum(
            evaluation[criterion] * weight 
            for criterion, weight in self.evaluation_criteria.items()
        )
        
        # Generate feedback
        feedback = self.generate_feedback(evaluation, overall_score)
        
        return {
            "results": [{
                "text": f"Response evaluation: {feedback['summary']}",
                "score": overall_score
            }],
            "evaluation_scores": evaluation,
            "overall_score": overall_score,
            "feedback": feedback,
            "confidence": 0.8,
            "agent": self.name
        }
    
    def evaluate_accuracy(self, response: str, context: Dict[str, Any]) -> float:
        """Evaluate response accuracy"""
        # Simple accuracy checks
        score = 0.7  # Base score
        
        # Check for factual consistency with known information
        if context.get("organization"):
            org = context["organization"].lower()
            if org in response.lower():
                score += 0.2
        
        # Check for specific policy information
        policy_terms = ["leave", "policy", "days", "weeks"]
        if any(term in response.lower() for term in policy_terms):
            score += 0.1
        
        return min(score, 1.0)
    
    def evaluate_completeness(self, response: str, query: str) -> float:
        """Evaluate response completeness"""
        query_words = set(query.lower().split())
        response_words = set(response.lower().split())
        
        # Check coverage of query terms
        coverage = len(query_words & response_words) / len(query_words) if query_words else 0
        
        # Check response length adequacy
        length_score = min(len(response) / 200, 1.0)  # Expect at least 200 chars
        
        return (coverage * 0.7 + length_score * 0.3)
    
    def evaluate_relevance(self, response: str, query: str) -> float:
        """Evaluate response relevance"""
        query_lower = query.lower()
        response_lower = response.lower()
        
        # Direct relevance indicators
        relevance_score = 0.5  # Base score
        
        # Check for query keywords in response
        query_keywords = [word for word in query_lower.split() if len(word) > 3]
        keyword_matches = sum(1 for keyword in query_keywords if keyword in response_lower)
        
        if query_keywords:
            relevance_score += (keyword_matches / len(query_keywords)) * 0.4
        
        # Check for appropriate response structure
        if any(indicator in response_lower for indicator in ["based on", "according to", "policy states"]):
            relevance_score += 0.1
        
        return min(relevance_score, 1.0)
    
    def evaluate_consistency(self, response: str) -> float:
        """Evaluate internal consistency"""
        # Check for contradictory statements
        contradiction_indicators = [
            ("yes", "no"), ("allow", "prohibit"), ("can", "cannot"),
            ("required", "optional"), ("must", "may not")
        ]
        
        response_lower = response.lower()
        consistency_score = 1.0
        
        for pos, neg in contradiction_indicators:
            if pos in response_lower and neg in response_lower:
                # Check if they're in different contexts (simple heuristic)
                pos_index = response_lower.find(pos)
                neg_index = response_lower.find(neg)
                
                if abs(pos_index - neg_index) < 100:  # Close proximity suggests contradiction
                    consistency_score -= 0.2
        
        return max(consistency_score, 0.0)
    
    def evaluate_clarity(self, response: str) -> float:
        """Evaluate response clarity"""
        # Simple clarity metrics
        sentences = response.split('.')
        avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences) if sentences else 0
        
        # Prefer moderate sentence length (10-25 words)
        length_score = 1.0 - abs(avg_sentence_length - 17.5) / 17.5 if avg_sentence_length > 0 else 0.5
        length_score = max(0, min(1, length_score))
        
        # Check for clear structure indicators
        structure_indicators = ["first", "second", "additionally", "however", "therefore", "in conclusion"]
        structure_score = min(sum(1 for indicator in structure_indicators if indicator in response.lower()) / 3, 1.0)
        
        return (length_score * 0.6 + structure_score * 0.4)
    
    async def fact_check(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Perform fact checking on response"""
        response = task.get("response", "")
        known_facts = task.get("known_facts", {})
        
        fact_check_results = []
        
        # Check specific claims
        claims = self.extract_claims(response)
        
        for claim in claims:
            verification = self.verify_claim(claim, known_facts)
            fact_check_results.append({
                "claim": claim,
                "verification": verification,
                "confidence": verification.get("confidence", 0.5)
            })
        
        overall_factual_accuracy = sum(r["confidence"] for r in fact_check_results) / len(fact_check_results) if fact_check_results else 0.5
        
        return {
            "results": [{
                "text": f"Fact check completed: {len(fact_check_results)} claims verified",
                "score": overall_factual_accuracy
            }],
            "fact_check_results": fact_check_results,
            "overall_accuracy": overall_factual_accuracy,
            "agent": self.name
        }
    
    def extract_claims(self, response: str) -> List[str]:
        """Extract factual claims from response"""
        # Simple claim extraction
        sentences = [s.strip() for s in response.split('.') if s.strip()]
        
        # Filter for sentences that make specific claims
        claims = []
        for sentence in sentences:
            if any(indicator in sentence.lower() for indicator in ["days", "weeks", "policy", "allows", "requires"]):
                claims.append(sentence)
        
        return claims[:5]  # Limit to top 5 claims
    
    def verify_claim(self, claim: str, known_facts: Dict[str, Any]) -> Dict[str, Any]:
        """Verify a specific claim"""
        # Simple verification logic
        claim_lower = claim.lower()
        
        verification = {
            "status": "unverified",
            "confidence": 0.5,
            "reasoning": "No specific verification data available"
        }
        
        # Check against known facts
        if "30 days" in claim_lower and "privilege leave" in claim_lower:
            verification = {
                "status": "verified",
                "confidence": 0.9,
                "reasoning": "Matches known privilege leave policy"
            }
        elif "26 weeks" in claim_lower and "maternity" in claim_lower:
            verification = {
                "status": "verified", 
                "confidence": 0.9,
                "reasoning": "Matches known maternity leave policy"
            }
        
        return verification
    
    async def check_consistency(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Check consistency across multiple responses"""
        responses = task.get("responses", [])
        
        if len(responses) < 2:
            return {
                "results": [{"text": "Need at least 2 responses for consistency check", "score": 0.0}],
                "agent": self.name
            }
        
        consistency_issues = []
        
        # Compare responses pairwise
        for i in range(len(responses)):
            for j in range(i + 1, len(responses)):
                issues = self.compare_responses(responses[i], responses[j])
                consistency_issues.extend(issues)
        
        consistency_score = max(0, 1.0 - len(consistency_issues) * 0.2)
        
        return {
            "results": [{
                "text": f"Consistency check: {len(consistency_issues)} issues found",
                "score": consistency_score
            }],
            "consistency_issues": consistency_issues,
            "consistency_score": consistency_score,
            "agent": self.name
        }
    
    def compare_responses(self, response1: str, response2: str) -> List[Dict[str, Any]]:
        """Compare two responses for consistency"""
        issues = []
        
        # Simple consistency checks
        r1_lower = response1.lower()
        r2_lower = response2.lower()
        
        # Check for contradictory numbers
        import re
        numbers1 = re.findall(r'\d+', r1_lower)
        numbers2 = re.findall(r'\d+', r2_lower)
        
        if numbers1 and numbers2 and numbers1 != numbers2:
            issues.append({
                "type": "numerical_inconsistency",
                "description": f"Different numbers mentioned: {numbers1} vs {numbers2}"
            })
        
        return issues
    
    def generate_feedback(self, evaluation: Dict[str, float], overall_score: float) -> Dict[str, Any]:
        """Generate feedback based on evaluation"""
        feedback = {
            "summary": "",
            "strengths": [],
            "improvements": [],
            "overall_quality": ""
        }
        
        # Determine overall quality
        if overall_score >= 0.8:
            feedback["overall_quality"] = "Excellent"
            feedback["summary"] = "High-quality response with strong performance across all criteria"
        elif overall_score >= 0.6:
            feedback["overall_quality"] = "Good"
            feedback["summary"] = "Good response with room for minor improvements"
        elif overall_score >= 0.4:
            feedback["overall_quality"] = "Fair"
            feedback["summary"] = "Adequate response but needs improvement in several areas"
        else:
            feedback["overall_quality"] = "Poor"
            feedback["summary"] = "Response needs significant improvement"
        
        # Identify strengths and improvements
        for criterion, score in evaluation.items():
            if score >= 0.8:
                feedback["strengths"].append(f"Strong {criterion}")
            elif score < 0.5:
                feedback["improvements"].append(f"Improve {criterion}")
        
        return feedback
    
    async def general_evaluation(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """General evaluation task"""
        return {
            "results": [{"text": "General evaluation completed", "score": 0.7}],
            "agent": self.name
        }
    
    async def verify_response(self, response: Dict[str, Any], original_task: Dict[str, Any]) -> Dict[str, Any]:
        """Advanced response verification for multi-agent coordination"""
        verification_result = {
            "confidence": 0.0,
            "verification_passed": False,
            "issues": [],
            "recommendations": [],
            "detailed_analysis": {}
        }

        try:
            # Extract response content
            response_text = response.get("response", "")
            if isinstance(response.get("results"), list) and response["results"]:
                response_text = response["results"][0].get("text", response_text)

            # Perform comprehensive verification
            accuracy_check = await self.verify_accuracy(response_text, original_task)
            completeness_check = await self.verify_completeness(response_text, original_task)
            consistency_check = await self.verify_internal_consistency(response_text)
            reasoning_check = await self.verify_reasoning_chain(response)

            # Calculate overall confidence
            verification_scores = [
                accuracy_check["score"],
                completeness_check["score"],
                consistency_check["score"],
                reasoning_check["score"]
            ]

            verification_result["confidence"] = sum(verification_scores) / len(verification_scores)
            verification_result["verification_passed"] = verification_result["confidence"] >= 0.7

            # Collect issues and recommendations
            for check in [accuracy_check, completeness_check, consistency_check, reasoning_check]:
                verification_result["issues"].extend(check.get("issues", []))
                verification_result["recommendations"].extend(check.get("recommendations", []))

            verification_result["detailed_analysis"] = {
                "accuracy": accuracy_check,
                "completeness": completeness_check,
                "consistency": consistency_check,
                "reasoning": reasoning_check
            }

        except Exception as e:
            verification_result["issues"].append(f"Verification error: {str(e)}")
            verification_result["confidence"] = 0.0

        return verification_result

    async def verify_accuracy(self, response_text: str, task: Dict[str, Any]) -> Dict[str, Any]:
        """Verify response accuracy"""
        accuracy_result = {
            "score": 0.0,
            "issues": [],
            "recommendations": []
        }

        # Check factual consistency
        organization = task.get("organization", "")
        department = task.get("department", "")

        base_score = 0.6

        # Organization context verification
        if organization and organization.lower() in response_text.lower():
            base_score += 0.2
        elif organization:
            accuracy_result["issues"].append(f"Response doesn't mention specified organization: {organization}")
            accuracy_result["recommendations"].append("Include organization-specific context")

        # Department context verification
        if department and department.lower() in response_text.lower():
            base_score += 0.1

        # Check for specific policy references
        policy_indicators = ["policy", "procedure", "guideline", "regulation"]
        if any(indicator in response_text.lower() for indicator in policy_indicators):
            base_score += 0.1
        else:
            accuracy_result["recommendations"].append("Reference specific policies or procedures")

        accuracy_result["score"] = min(base_score, 1.0)
        return accuracy_result

    async def verify_completeness(self, response_text: str, task: Dict[str, Any]) -> Dict[str, Any]:
        """Verify response completeness"""
        completeness_result = {
            "score": 0.0,
            "issues": [],
            "recommendations": []
        }

        query = task.get("query", "")
        query_words = set(query.lower().split())
        response_words = set(response_text.lower().split())

        # Query coverage
        coverage = len(query_words & response_words) / len(query_words) if query_words else 0

        # Response length adequacy
        min_length = 100
        length_score = min(len(response_text) / min_length, 1.0)

        if length_score < 0.5:
            completeness_result["issues"].append("Response is too brief")
            completeness_result["recommendations"].append("Provide more detailed explanation")

        # Check for essential components
        essential_components = ["explanation", "example", "reference"]
        component_score = 0

        if any(comp in response_text.lower() for comp in ["because", "due to", "reason"]):
            component_score += 0.33
        else:
            completeness_result["recommendations"].append("Include reasoning or explanation")

        if any(comp in response_text.lower() for comp in ["example", "instance", "case"]):
            component_score += 0.33

        if any(comp in response_text.lower() for comp in ["policy", "document", "section"]):
            component_score += 0.34

        completeness_result["score"] = (coverage * 0.4 + length_score * 0.3 + component_score * 0.3)
        return completeness_result

    async def verify_internal_consistency(self, response_text: str) -> Dict[str, Any]:
        """Verify internal consistency of response"""
        consistency_result = {
            "score": 1.0,
            "issues": [],
            "recommendations": []
        }

        # Check for contradictions
        contradictions = [
            ("yes", "no"), ("allow", "prohibit"), ("can", "cannot"),
            ("required", "optional"), ("must", "may not"), ("always", "never")
        ]

        response_lower = response_text.lower()

        for pos, neg in contradictions:
            if pos in response_lower and neg in response_lower:
                pos_index = response_lower.find(pos)
                neg_index = response_lower.find(neg)

                # Check proximity (potential contradiction)
                if abs(pos_index - neg_index) < 150:
                    consistency_result["score"] -= 0.3
                    consistency_result["issues"].append(f"Potential contradiction: '{pos}' and '{neg}' in close proximity")
                    consistency_result["recommendations"].append("Clarify seemingly contradictory statements")

        # Check for logical flow
        sentences = [s.strip() for s in response_text.split('.') if s.strip()]
        if len(sentences) > 1:
            # Simple coherence check
            transition_words = ["however", "therefore", "additionally", "furthermore", "consequently"]
            has_transitions = any(word in response_text.lower() for word in transition_words)

            if not has_transitions and len(sentences) > 3:
                consistency_result["recommendations"].append("Use transition words to improve logical flow")

        consistency_result["score"] = max(consistency_result["score"], 0.0)
        return consistency_result

    async def verify_reasoning_chain(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Verify reasoning chain in response"""
        reasoning_result = {
            "score": 0.5,
            "issues": [],
            "recommendations": []
        }

        # Check if reasoning trace is available
        reasoning_trace = response.get("reasoning_trace", [])

        if reasoning_trace:
            reasoning_result["score"] += 0.3

            # Verify reasoning steps
            if len(reasoning_trace) >= 3:
                reasoning_result["score"] += 0.1
            else:
                reasoning_result["recommendations"].append("Provide more detailed reasoning steps")

            # Check for logical progression
            step_keywords = ["first", "then", "next", "finally", "therefore", "because"]
            logical_steps = sum(1 for step in reasoning_trace if any(keyword in str(step).lower() for keyword in step_keywords))

            if logical_steps > 0:
                reasoning_result["score"] += 0.1
            else:
                reasoning_result["recommendations"].append("Use logical connectors in reasoning steps")
        else:
            reasoning_result["issues"].append("No reasoning trace provided")
            reasoning_result["recommendations"].append("Include step-by-step reasoning")

        # Check for evidence or sources
        if response.get("sources") or "based on" in str(response).lower():
            reasoning_result["score"] += 0.1
        else:
            reasoning_result["recommendations"].append("Cite sources or evidence")

        reasoning_result["score"] = min(reasoning_result["score"], 1.0)
        return reasoning_result

    def get_capabilities(self) -> List[str]:
        return self.capabilities