import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.core.base_agent import BaseAgent
from typing import Dict, List, Any

class DepartmentAgent(BaseAgent):
    def __init__(self, department_name: str):
        super().__init__(name=f"DepartmentAgent_{department_name}")
        self.department = department_name
        self.capabilities = ["department_expertise", "domain_knowledge", "specialized_reasoning"]
        self.dept_knowledge = self.load_department_knowledge()
        
    def load_department_knowledge(self) -> Dict[str, Any]:
        """Load department-specific knowledge"""
        knowledge_base = {
            "HR": {
                "expertise": ["leave_policies", "employee_benefits", "compliance", "recruitment"],
                "terminology": ["PL", "CL", "SL", "LTA", "maternity_leave", "sexual_harassment"],
                "processes": ["leave_application", "policy_enforcement", "grievance_handling"],
                "key_documents": ["HR_manual", "policy_documents", "compliance_guidelines"]
            },
            "Finance": {
                "expertise": ["budgeting", "expense_management", "reimbursements", "payroll"],
                "terminology": ["reimbursement", "expense_claim", "budget_allocation", "cost_center"],
                "processes": ["expense_approval", "budget_planning", "financial_reporting"],
                "key_documents": ["finance_policy", "expense_guidelines", "budget_documents"]
            },
            "IT": {
                "expertise": ["system_administration", "security", "infrastructure", "support"],
                "terminology": ["server", "network", "security_policy", "backup", "maintenance"],
                "processes": ["incident_management", "change_management", "security_protocols"],
                "key_documents": ["IT_policy", "security_guidelines", "system_documentation"]
            },
            "Engineering": {
                "expertise": ["software_development", "system_design", "technical_architecture"],
                "terminology": ["API", "database", "deployment", "testing", "code_review"],
                "processes": ["development_lifecycle", "code_review", "deployment_process"],
                "key_documents": ["technical_specifications", "architecture_documents"]
            }
        }
        
        return knowledge_base.get(self.department, {})
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process department-specific task"""
        self.update_status("processing")
        
        query = task.get("query", "")
        context = task.get("context", {})
        
        try:
            # Check domain relevance
            relevance = self.calculate_domain_relevance(query)
            
            if relevance < 0.2:
                return {
                    "results": [],
                    "relevance": relevance,
                    "message": f"Query not relevant to {self.department} department",
                    "agent": self.name
                }
            
            # Process domain-specific query
            results = await self.process_domain_query(query, context)
            
            self.update_status("idle")
            return {
                "results": results,
                "department": self.department,
                "relevance": relevance,
                "confidence": self.calculate_confidence(results),
                "agent": self.name
            }
            
        except Exception as e:
            self.update_status("error")
            return {"error": str(e), "agent": self.name}
    
    def calculate_domain_relevance(self, query: str) -> float:
        """Calculate query relevance to department domain"""
        query_lower = query.lower()
        relevance = 0.0
        
        # Direct department mention
        if self.department.lower() in query_lower:
            relevance += 0.4
        
        # Expertise area mentions
        expertise = self.dept_knowledge.get("expertise", [])
        for area in expertise:
            if area.replace("_", " ") in query_lower:
                relevance += 0.3
                break
        
        # Terminology mentions
        terminology = self.dept_knowledge.get("terminology", [])
        for term in terminology:
            if term.lower() in query_lower:
                relevance += 0.2
                break
        
        # Process mentions
        processes = self.dept_knowledge.get("processes", [])
        for process in processes:
            if process.replace("_", " ") in query_lower:
                relevance += 0.2
                break
        
        return min(relevance, 1.0)
    
    async def process_domain_query(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Process domain-specific query"""
        results = []
        query_lower = query.lower()
        
        # Expertise-based responses
        expertise_results = self.search_expertise(query_lower)
        results.extend(expertise_results)
        
        # Process-based responses
        process_results = self.search_processes(query_lower)
        results.extend(process_results)
        
        # Terminology explanations
        terminology_results = self.explain_terminology(query_lower)
        results.extend(terminology_results)
        
        # General department guidance
        if not results:
            results.append({
                "text": f"{self.department} department guidance: {self.get_general_guidance(query_lower)}",
                "score": 0.5,
                "type": "general_guidance"
            })
        
        return results
    
    def search_expertise(self, query: str) -> List[Dict[str, Any]]:
        """Search department expertise areas"""
        results = []
        expertise = self.dept_knowledge.get("expertise", [])
        
        for area in expertise:
            if area.replace("_", " ") in query:
                guidance = self.get_expertise_guidance(area)
                results.append({
                    "text": f"{self.department} expertise in {area}: {guidance}",
                    "score": 0.8,
                    "type": "expertise",
                    "expertise_area": area
                })
        
        return results
    
    def search_processes(self, query: str) -> List[Dict[str, Any]]:
        """Search department processes"""
        results = []
        processes = self.dept_knowledge.get("processes", [])
        
        for process in processes:
            if process.replace("_", " ") in query:
                process_info = self.get_process_info(process)
                results.append({
                    "text": f"{self.department} {process}: {process_info}",
                    "score": 0.7,
                    "type": "process",
                    "process_name": process
                })
        
        return results
    
    def explain_terminology(self, query: str) -> List[Dict[str, Any]]:
        """Explain department-specific terminology"""
        results = []
        terminology = self.dept_knowledge.get("terminology", [])
        
        for term in terminology:
            if term.lower() in query:
                explanation = self.get_term_explanation(term)
                results.append({
                    "text": f"{term} ({self.department}): {explanation}",
                    "score": 0.6,
                    "type": "terminology",
                    "term": term
                })
        
        return results
    
    def get_expertise_guidance(self, area: str) -> str:
        """Get guidance for expertise area"""
        guidance_map = {
            "leave_policies": "Comprehensive leave management including PL, CL, SL, and special leaves",
            "employee_benefits": "Medical insurance, LTA, and other employee welfare benefits",
            "compliance": "Regulatory compliance including sexual harassment prevention",
            "expense_management": "Expense claim processing and reimbursement procedures",
            "system_administration": "Server management, monitoring, and maintenance",
            "software_development": "Full software development lifecycle management"
        }
        
        return guidance_map.get(area, f"Specialized knowledge in {area}")
    
    def get_process_info(self, process: str) -> str:
        """Get process information"""
        process_info = {
            "leave_application": "Submit through HR portal with manager approval",
            "expense_approval": "Submit with receipts, requires department head approval",
            "incident_management": "Report through IT helpdesk, tracked until resolution",
            "development_lifecycle": "Requirements → Design → Development → Testing → Deployment"
        }
        
        return process_info.get(process, f"Standard {process} procedure")
    
    def get_term_explanation(self, term: str) -> str:
        """Get terminology explanation"""
        explanations = {
            "PL": "Privilege Leave - Annual leave entitlement",
            "CL": "Casual Leave - Short-term leave for personal reasons",
            "SL": "Sick Leave - Medical leave for health issues",
            "LTA": "Leave Travel Allowance - Reimbursement for travel expenses",
            "API": "Application Programming Interface",
            "reimbursement": "Repayment of expenses incurred on behalf of organization"
        }
        
        return explanations.get(term, f"Department-specific term: {term}")
    
    def get_general_guidance(self, query: str) -> str:
        """Get general department guidance"""
        if self.department == "HR":
            return "For employee-related queries, policies, and benefits information"
        elif self.department == "Finance":
            return "For expense claims, budget queries, and financial procedures"
        elif self.department == "IT":
            return "For technical support, system access, and IT infrastructure"
        elif self.department == "Engineering":
            return "For technical development, architecture, and engineering processes"
        else:
            return f"Specialized support for {self.department} domain"
    
    def calculate_confidence(self, results: List[Dict[str, Any]]) -> float:
        """Calculate confidence in results"""
        if not results:
            return 0.0
        
        scores = [r.get("score", 0) for r in results]
        return sum(scores) / len(scores)
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities