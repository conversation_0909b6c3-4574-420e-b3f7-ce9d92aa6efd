import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.core.base_agent import BaseAgent
from typing import Dict, List, Any, Tuple

class ReasoningAgent(BaseAgent):
    def __init__(self):
        super().__init__(name="ReasoningAgent")
        self.capabilities = ["logical_reasoning", "comparison", "inference", "tree_of_thoughts"]
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process reasoning task"""
        self.update_status("reasoning")
        
        task_type = task.get("type", "general_reasoning")
        
        try:
            if task_type == "comparison":
                result = await self.perform_comparison(task)
            elif task_type == "inference":
                result = await self.perform_inference(task)
            elif task_type == "tree_of_thoughts":
                result = await self.tree_of_thoughts_reasoning(task)
            else:
                result = await self.general_reasoning(task)
            
            self.update_status("idle")
            return result
            
        except Exception as e:
            self.update_status("error")
            return {"error": str(e), "agent": self.name}
    
    async def perform_comparison(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comparison reasoning"""
        query = task.get("query", "")
        context = task.get("context", {})
        
        # Extract comparison entities
        entities = self.extract_comparison_entities(query)
        
        # Mock comparison logic
        comparison_result = {
            "comparison_type": "policy_comparison",
            "entities": entities,
            "differences": [
                "Entity 1 has feature X",
                "Entity 2 has feature Y", 
                "Common features: Z"
            ],
            "conclusion": f"Comparison between {entities[0] if entities else 'items'} shows key differences in implementation."
        }
        
        return {
            "results": [{"text": str(comparison_result), "score": 0.8}],
            "reasoning_type": "comparison",
            "confidence": 0.8,
            "agent": self.name
        }
    
    async def tree_of_thoughts_reasoning(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Implement Tree of Thoughts reasoning"""
        query = task.get("query", "")
        
        # Generate multiple reasoning paths
        thoughts = [
            {"path": "Path 1: Direct policy lookup", "confidence": 0.8},
            {"path": "Path 2: Cross-reference with related policies", "confidence": 0.7},
            {"path": "Path 3: Consider organizational context", "confidence": 0.9}
        ]
        
        # Evaluate paths
        best_path = max(thoughts, key=lambda x: x["confidence"])
        
        return {
            "results": [{"text": f"Best reasoning path: {best_path['path']}", "score": best_path["confidence"]}],
            "reasoning_paths": thoughts,
            "selected_path": best_path,
            "reasoning_type": "tree_of_thoughts",
            "confidence": best_path["confidence"],
            "agent": self.name
        }
    
    async def perform_inference(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Perform logical inference"""
        premises = task.get("premises", [])
        
        # Simple inference logic
        inference = "Based on the given information, we can conclude..."
        
        return {
            "results": [{"text": inference, "score": 0.7}],
            "reasoning_type": "inference",
            "confidence": 0.7,
            "agent": self.name
        }
    
    async def general_reasoning(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """General reasoning capability"""
        query = task.get("query", "")
        
        reasoning_steps = [
            "1. Analyze the query components",
            "2. Identify relevant knowledge domains", 
            "3. Apply logical reasoning",
            "4. Synthesize conclusion"
        ]
        
        return {
            "results": [{"text": "General reasoning applied to: " + query, "score": 0.6}],
            "reasoning_steps": reasoning_steps,
            "reasoning_type": "general",
            "confidence": 0.6,
            "agent": self.name
        }
    
    def extract_comparison_entities(self, query: str) -> List[str]:
        """Extract entities for comparison"""
        entities = []
        
        # Simple entity extraction for comparison
        if "nuvo ai" in query.lower():
            entities.append("NUVO AI")
        if "meril" in query.lower():
            entities.append("Meril")
        
        return entities
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities