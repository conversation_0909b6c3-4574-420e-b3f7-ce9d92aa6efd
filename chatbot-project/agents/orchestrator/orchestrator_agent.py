import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.core.base_agent import BaseAgent
from agents.routing.organization_router import OrganizationRouter
from agents.fallback.fallback_manager import Fallback<PERSON><PERSON><PERSON>, FallbackTrigger
from typing import Dict, List, Any
import asyncio

class OrchestratorAgent(BaseAgent):
    def __init__(self):
        super().__init__(name="OrchestratorAgent")
        self.specialized_agents = {}
        self.task_queue = []
        self.capabilities = ["task_planning", "agent_coordination", "response_synthesis"]
        self.router = OrganizationRouter()
        self.fallback_manager = FallbackManager()
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Main task processing pipeline"""
        self.update_status("processing")
        
        try:
            # 1. Enhanced organization routing
            routing_decision = self.router.route_query(task.get("query", ""), task.get("user_context", {}))
            task["routing"] = routing_decision
            
            # 2. Plan task decomposition
            subtasks = await self.plan_task_decomposition(task)
            
            # 3. Allocate agents
            agent_assignments = await self.allocate_agents(subtasks)
            
            # 4. Execute subtasks
            results = await self.execute_subtasks(agent_assignments)
            
            # 5. Synthesize response
            final_response = await self.synthesize_response(results, task)
            
            # 6. Check if fallback needed
            if final_response.get("confidence", 1.0) < 0.6:
                fallback_response = await self.fallback_manager.handle_fallback(
                    FallbackTrigger.LOW_CONFIDENCE, 
                    {**task, **final_response}
                )
                final_response.update(fallback_response)
            
            self.update_status("idle")
            return final_response
            
        except Exception as e:
            self.update_status("error")
            # Handle error with fallback
            fallback_response = await self.fallback_manager.handle_fallback(
                FallbackTrigger.AGENT_ERROR,
                {**task, "error": str(e)}
            )
            return fallback_response
    
    async def plan_task_decomposition(self, task: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Decompose complex task into subtasks"""
        query = task.get("query", "")
        user_context = task.get("user_context", {})
        
        subtasks = []
        
        # Intent-based decomposition
        if "policy" in query.lower():
            subtasks.append({
                "type": "knowledge_retrieval",
                "target": "policy_search",
                "query": query,
                "context": user_context
            })
        
        if "who is" in query.lower() or "person" in query.lower():
            subtasks.append({
                "type": "entity_search",
                "target": "person_lookup",
                "query": query,
                "context": user_context
            })
        
        if "compare" in query.lower() or "difference" in query.lower():
            subtasks.append({
                "type": "comparison",
                "target": "cross_org_analysis",
                "query": query,
                "context": user_context
            })
        
        # Default knowledge retrieval
        if not subtasks:
            subtasks.append({
                "type": "knowledge_retrieval",
                "target": "general_search",
                "query": query,
                "context": user_context
            })
        
        return subtasks
    
    async def allocate_agents(self, subtasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Allocate appropriate agents to subtasks"""
        assignments = {}
        
        for i, subtask in enumerate(subtasks):
            task_type = subtask.get("type")
            
            if task_type == "knowledge_retrieval":
                assignments[f"task_{i}"] = {
                    "agent_type": "knowledge_agent",
                    "subtask": subtask
                }
            elif task_type == "entity_search":
                assignments[f"task_{i}"] = {
                    "agent_type": "entity_agent", 
                    "subtask": subtask
                }
            elif task_type == "comparison":
                assignments[f"task_{i}"] = {
                    "agent_type": "reasoning_agent",
                    "subtask": subtask
                }
        
        return assignments
    
    async def execute_subtasks(self, assignments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute subtasks using assigned agents"""
        results = {}
        
        # Simulate agent execution
        for task_id, assignment in assignments.items():
            agent_type = assignment["agent_type"]
            subtask = assignment["subtask"]
            
            # Mock agent execution
            if agent_type == "knowledge_agent":
                results[task_id] = await self.mock_knowledge_search(subtask)
            elif agent_type == "entity_agent":
                results[task_id] = await self.mock_entity_search(subtask)
            elif agent_type == "reasoning_agent":
                results[task_id] = await self.mock_reasoning(subtask)
        
        return results
    
    async def synthesize_response(self, results: Dict[str, Any], original_task: Dict[str, Any]) -> Dict[str, Any]:
        """Synthesize final response from subtask results"""
        # Combine all results
        combined_info = []
        confidence_scores = []
        
        for task_id, result in results.items():
            if "results" in result:
                combined_info.extend(result["results"])
            if "confidence" in result:
                confidence_scores.append(result["confidence"])
        
        # Calculate overall confidence
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.5
        
        return {
            "response": self.format_response(combined_info, original_task),
            "confidence": avg_confidence,
            "sources": len(combined_info),
            "agent_coordination": "orchestrator",
            "subtasks_completed": len(results)
        }
    
    def format_response(self, info_items: List[Dict], task: Dict[str, Any]) -> str:
        """Format final response"""
        if not info_items:
            return "I couldn't find specific information for your query."
        
        # Simple response formatting
        response_parts = []
        for item in info_items[:3]:  # Top 3 results
            if "text" in item:
                response_parts.append(item["text"][:200] + "...")
        
        return " ".join(response_parts)
    
    async def mock_knowledge_search(self, subtask: Dict[str, Any]) -> Dict[str, Any]:
        """Mock knowledge search agent"""
        return {
            "results": [
                {"text": "Mock knowledge result for: " + subtask["query"], "score": 0.8}
            ],
            "confidence": 0.8
        }
    
    async def mock_entity_search(self, subtask: Dict[str, Any]) -> Dict[str, Any]:
        """Mock entity search agent"""
        return {
            "results": [
                {"text": "Mock entity result for: " + subtask["query"], "score": 0.9}
            ],
            "confidence": 0.9
        }
    
    async def mock_reasoning(self, subtask: Dict[str, Any]) -> Dict[str, Any]:
        """Mock reasoning agent"""
        return {
            "results": [
                {"text": "Mock reasoning result for: " + subtask["query"], "score": 0.7}
            ],
            "confidence": 0.7
        }
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities
    
    def register_agent(self, agent_type: str, agent_instance):
        """Register specialized agent"""
        self.specialized_agents[agent_type] = agent_instance