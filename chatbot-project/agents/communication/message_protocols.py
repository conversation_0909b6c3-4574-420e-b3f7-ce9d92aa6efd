from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import json
import uuid
from datetime import datetime

class MessageType(Enum):
    REQUEST = "request"
    RESPONSE = "response"
    BROADCAST = "broadcast"
    TASK_DELEGATION = "task_delegation"
    STATUS_UPDATE = "status_update"
    ERROR = "error"

class Priority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class AgentMessage:
    message_id: str
    sender_id: str
    recipient_id: str
    message_type: MessageType
    priority: Priority
    payload: Dict[str, Any]
    timestamp: str
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    ttl: Optional[int] = None  # Time to live in seconds

class MessageProtocols:
    def __init__(self):
        self.message_handlers = {}
        self.message_history = []
        self.pending_requests = {}
        
    def create_message(self, sender_id: str, recipient_id: str, 
                      message_type: MessageType, payload: Dict[str, Any],
                      priority: Priority = Priority.MEDIUM,
                      correlation_id: str = None, reply_to: str = None) -> AgentMessage:
        """Create a structured message"""
        return AgentMessage(
            message_id=str(uuid.uuid4()),
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=message_type,
            priority=priority,
            payload=payload,
            timestamp=datetime.now().isoformat(),
            correlation_id=correlation_id,
            reply_to=reply_to
        )
    
    def create_request_message(self, sender_id: str, recipient_id: str,
                             task: Dict[str, Any], priority: Priority = Priority.MEDIUM) -> AgentMessage:
        """Create a request message"""
        return self.create_message(
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=MessageType.REQUEST,
            payload={"task": task, "request_type": "task_execution"},
            priority=priority
        )
    
    def create_response_message(self, sender_id: str, recipient_id: str,
                              result: Dict[str, Any], original_message_id: str) -> AgentMessage:
        """Create a response message"""
        return self.create_message(
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=MessageType.RESPONSE,
            payload={"result": result, "status": "completed"},
            reply_to=original_message_id
        )
    
    def create_broadcast_message(self, sender_id: str, announcement: Dict[str, Any],
                               priority: Priority = Priority.LOW) -> AgentMessage:
        """Create a broadcast message"""
        return self.create_message(
            sender_id=sender_id,
            recipient_id="*",  # Broadcast to all
            message_type=MessageType.BROADCAST,
            payload={"announcement": announcement},
            priority=priority
        )
    
    def create_task_delegation_message(self, sender_id: str, recipient_id: str,
                                     delegated_task: Dict[str, Any]) -> AgentMessage:
        """Create a task delegation message"""
        return self.create_message(
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=MessageType.TASK_DELEGATION,
            payload={"delegated_task": delegated_task, "delegation_level": 1},
            priority=Priority.HIGH
        )
    
    def create_status_update_message(self, sender_id: str, recipient_id: str,
                                   status_info: Dict[str, Any]) -> AgentMessage:
        """Create a status update message"""
        return self.create_message(
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=MessageType.STATUS_UPDATE,
            payload={"status": status_info},
            priority=Priority.LOW
        )
    
    def serialize_message(self, message: AgentMessage) -> str:
        """Serialize message to JSON"""
        message_dict = asdict(message)
        message_dict["message_type"] = message.message_type.value
        message_dict["priority"] = message.priority.value
        return json.dumps(message_dict)
    
    def deserialize_message(self, message_json: str) -> AgentMessage:
        """Deserialize message from JSON"""
        message_dict = json.loads(message_json)
        message_dict["message_type"] = MessageType(message_dict["message_type"])
        message_dict["priority"] = Priority(message_dict["priority"])
        return AgentMessage(**message_dict)
    
    def validate_message(self, message: AgentMessage) -> bool:
        """Validate message structure"""
        required_fields = ["message_id", "sender_id", "recipient_id", "message_type", "payload", "timestamp"]
        
        for field in required_fields:
            if not hasattr(message, field) or getattr(message, field) is None:
                return False
        
        # Validate message type
        if not isinstance(message.message_type, MessageType):
            return False
        
        # Validate priority
        if not isinstance(message.priority, Priority):
            return False
        
        return True
    
    def route_message(self, message: AgentMessage, agent_registry) -> bool:
        """Route message to appropriate recipient"""
        if not self.validate_message(message):
            return False
        
        # Store in history
        self.message_history.append(message)
        
        # Handle broadcast messages
        if message.recipient_id == "*":
            return self.handle_broadcast(message, agent_registry)
        
        # Route to specific recipient
        return self.deliver_message(message, agent_registry)
    
    def handle_broadcast(self, message: AgentMessage, agent_registry) -> bool:
        """Handle broadcast message delivery"""
        # Get all active agents except sender
        try:
            all_agents = agent_registry.get_all_agents()
            recipients = [agent for agent in all_agents if agent.agent_id != message.sender_id]
            
            success_count = 0
            for agent in recipients:
                individual_message = self.create_message(
                    sender_id=message.sender_id,
                    recipient_id=agent.agent_id,
                    message_type=message.message_type,
                    payload=message.payload,
                    priority=message.priority
                )
                
                if self.deliver_to_agent(individual_message, agent):
                    success_count += 1
            
            return success_count > 0
            
        except Exception:
            return False
    
    def deliver_message(self, message: AgentMessage, agent_registry) -> bool:
        """Deliver message to specific recipient"""
        try:
            recipient_agent = agent_registry.get_agent(message.recipient_id)
            if recipient_agent:
                return self.deliver_to_agent(message, recipient_agent)
            return False
        except Exception:
            return False
    
    def deliver_to_agent(self, message: AgentMessage, agent) -> bool:
        """Deliver message to specific agent"""
        try:
            # Store message in agent's inbox (if agent has one)
            if hasattr(agent, 'receive_message'):
                agent.receive_message(message)
                return True
            
            # Fallback: store in agent memory
            if hasattr(agent, 'store_memory'):
                agent.store_memory(f"message_{message.message_id}", asdict(message))
                return True
            
            return False
        except Exception:
            return False
    
    def create_conflict_resolution_message(self, sender_id: str, conflicting_agents: List[str],
                                         conflict_data: Dict[str, Any]) -> AgentMessage:
        """Create message for conflict resolution"""
        return self.create_message(
            sender_id=sender_id,
            recipient_id="conflict_resolver",
            message_type=MessageType.REQUEST,
            payload={
                "conflict_type": "agent_disagreement",
                "conflicting_agents": conflicting_agents,
                "conflict_data": conflict_data
            },
            priority=Priority.HIGH
        )
    
    def get_message_statistics(self) -> Dict[str, Any]:
        """Get message statistics"""
        if not self.message_history:
            return {"total_messages": 0}
        
        type_counts = {}
        priority_counts = {}
        
        for message in self.message_history:
            msg_type = message.message_type.value
            priority = message.priority.value
            
            type_counts[msg_type] = type_counts.get(msg_type, 0) + 1
            priority_counts[priority] = priority_counts.get(priority, 0) + 1
        
        return {
            "total_messages": len(self.message_history),
            "message_types": type_counts,
            "priority_distribution": priority_counts,
            "recent_messages": len([
                msg for msg in self.message_history[-10:]
            ])
        }
    
    def cleanup_old_messages(self, hours: int = 24):
        """Clean up old messages"""
        from datetime import timedelta
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        self.message_history = [
            msg for msg in self.message_history
            if datetime.fromisoformat(msg.timestamp) > cutoff_time
        ]