import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

class AgentCoordinator:
    def __init__(self):
        self.agents = {}
        self.message_queue = asyncio.Queue()
        self.active_tasks = {}
        self.coordination_history = []
    
    def register_agent(self, agent_id: str, agent_instance):
        """Register an agent with the coordinator"""
        self.agents[agent_id] = {
            "instance": agent_instance,
            "status": "idle",
            "capabilities": agent_instance.get_capabilities(),
            "last_active": datetime.now()
        }
    
    async def coordinate_multi_agent_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate a task across multiple agents"""
        task_id = f"task_{datetime.now().timestamp()}"
        
        self.active_tasks[task_id] = {
            "task": task,
            "status": "planning",
            "agents_involved": [],
            "results": {},
            "start_time": datetime.now()
        }
        
        try:
            # 1. Determine required agents
            required_agents = self.determine_required_agents(task)
            
            # 2. Check agent availability
            available_agents = self.check_agent_availability(required_agents)
            
            # 3. Coordinate execution
            results = await self.execute_coordinated_task(task_id, available_agents, task)
            
            # 4. Aggregate results
            final_result = self.aggregate_results(results, task)
            
            self.active_tasks[task_id]["status"] = "completed"
            return final_result
            
        except Exception as e:
            self.active_tasks[task_id]["status"] = "failed"
            return {"error": str(e), "task_id": task_id}
    
    def determine_required_agents(self, task: Dict[str, Any]) -> List[str]:
        """Determine which agents are needed for the task"""
        query = task.get("query", "").lower()
        required = []
        
        # Always need orchestrator for coordination
        if "OrchestratorAgent" in self.agents:
            required.append("OrchestratorAgent")
        
        # Knowledge retrieval tasks
        if any(keyword in query for keyword in ["policy", "information", "what is", "how to"]):
            if "KnowledgeAgent" in self.agents:
                required.append("KnowledgeAgent")
        
        # Reasoning tasks
        if any(keyword in query for keyword in ["compare", "analyze", "why", "explain"]):
            if "ReasoningAgent" in self.agents:
                required.append("ReasoningAgent")
        
        return required
    
    def check_agent_availability(self, required_agents: List[str]) -> List[str]:
        """Check which required agents are available"""
        available = []
        
        for agent_id in required_agents:
            if agent_id in self.agents:
                agent_info = self.agents[agent_id]
                if agent_info["status"] in ["idle", "ready"]:
                    available.append(agent_id)
        
        return available
    
    async def execute_coordinated_task(self, task_id: str, agent_ids: List[str], task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task with coordinated agents"""
        results = {}
        
        # Execute tasks in parallel where possible
        agent_tasks = []
        
        for agent_id in agent_ids:
            agent_instance = self.agents[agent_id]["instance"]
            
            # Update agent status
            self.agents[agent_id]["status"] = "busy"
            self.agents[agent_id]["last_active"] = datetime.now()
            
            # Create agent-specific task
            agent_task = self.create_agent_task(agent_id, task)
            agent_tasks.append(self.execute_agent_task(agent_id, agent_instance, agent_task))
        
        # Wait for all agents to complete
        agent_results = await asyncio.gather(*agent_tasks, return_exceptions=True)
        
        # Process results
        for i, agent_id in enumerate(agent_ids):
            result = agent_results[i]
            
            if isinstance(result, Exception):
                results[agent_id] = {"error": str(result)}
            else:
                results[agent_id] = result
            
            # Reset agent status
            self.agents[agent_id]["status"] = "idle"
        
        return results
    
    def create_agent_task(self, agent_id: str, original_task: Dict[str, Any]) -> Dict[str, Any]:
        """Create agent-specific task from original task"""
        # Customize task based on agent type
        agent_task = original_task.copy()
        
        if agent_id == "KnowledgeAgent":
            agent_task["type"] = "knowledge_retrieval"
        elif agent_id == "ReasoningAgent":
            agent_task["type"] = "reasoning"
        elif agent_id == "OrchestratorAgent":
            agent_task["type"] = "orchestration"
        
        return agent_task
    
    async def execute_agent_task(self, agent_id: str, agent_instance, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task on specific agent"""
        try:
            result = await agent_instance.process_task(task)
            
            # Log coordination
            self.coordination_history.append({
                "timestamp": datetime.now().isoformat(),
                "agent_id": agent_id,
                "task_type": task.get("type", "unknown"),
                "status": "completed"
            })
            
            return result
            
        except Exception as e:
            self.coordination_history.append({
                "timestamp": datetime.now().isoformat(),
                "agent_id": agent_id,
                "task_type": task.get("type", "unknown"),
                "status": "failed",
                "error": str(e)
            })
            
            raise e
    
    def aggregate_results(self, agent_results: Dict[str, Any], original_task: Dict[str, Any]) -> Dict[str, Any]:
        """Aggregate results from multiple agents"""
        aggregated = {
            "query": original_task.get("query", ""),
            "agents_used": list(agent_results.keys()),
            "combined_results": [],
            "confidence_scores": [],
            "coordination_success": True
        }
        
        # Combine results from all agents
        for agent_id, result in agent_results.items():
            if "error" not in result:
                if "results" in result:
                    aggregated["combined_results"].extend(result["results"])
                if "confidence" in result:
                    aggregated["confidence_scores"].append(result["confidence"])
            else:
                aggregated["coordination_success"] = False
        
        # Calculate overall confidence
        if aggregated["confidence_scores"]:
            aggregated["overall_confidence"] = sum(aggregated["confidence_scores"]) / len(aggregated["confidence_scores"])
        else:
            aggregated["overall_confidence"] = 0.0
        
        return aggregated
    
    def get_coordination_status(self) -> Dict[str, Any]:
        """Get current coordination status"""
        return {
            "registered_agents": len(self.agents),
            "active_tasks": len([t for t in self.active_tasks.values() if t["status"] == "running"]),
            "agent_statuses": {aid: info["status"] for aid, info in self.agents.items()},
            "recent_coordination": self.coordination_history[-10:]
        }