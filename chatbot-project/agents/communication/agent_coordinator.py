import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
from dataclasses import dataclass
from enum import Enum
import uuid

class CoordinationPattern(Enum):
    HIERARCHICAL = "hierarchical"
    COLLABORATIVE = "collaborative"
    SPECIALIST_CONSULTATION = "specialist_consultation"

@dataclass
class AgentMessage:
    id: str
    sender_id: str
    receiver_id: str
    message_type: str
    content: Dict[str, Any]
    priority: int = 1
    timestamp: float = None
    requires_response: bool = False

class AgentCoordinator:
    def __init__(self):
        self.agents = {}
        self.message_queue = asyncio.Queue()
        self.active_tasks = {}
        self.coordination_history = []
        self.knowledge_fusion = None
        self.memory_system = None
        self.coordination_patterns = {
            CoordinationPattern.HIERARCHICAL: self.hierarchical_coordination,
            CoordinationPattern.COLLABORATIVE: self.collaborative_coordination,
            CoordinationPattern.SPECIALIST_CONSULTATION: self.specialist_consultation
        }
    
    def register_agent(self, agent_id: str, agent_instance):
        """Register an agent with the coordinator"""
        self.agents[agent_id] = {
            "instance": agent_instance,
            "status": "idle",
            "capabilities": agent_instance.get_capabilities(),
            "last_active": datetime.now()
        }
        print(f"Agent {agent_id} registered with coordinator")

    def set_knowledge_fusion(self, knowledge_fusion):
        """Set knowledge fusion system"""
        self.knowledge_fusion = knowledge_fusion

    def set_memory_system(self, memory_system):
        """Set memory system"""
        self.memory_system = memory_system
    
    async def coordinate_multi_agent_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate a task across multiple agents"""
        task_id = f"task_{datetime.now().timestamp()}"
        
        self.active_tasks[task_id] = {
            "task": task,
            "status": "planning",
            "agents_involved": [],
            "results": {},
            "start_time": datetime.now()
        }
        
        try:
            # 1. Determine required agents
            required_agents = self.determine_required_agents(task)
            
            # 2. Check agent availability
            available_agents = self.check_agent_availability(required_agents)
            
            # 3. Coordinate execution
            results = await self.execute_coordinated_task(task_id, available_agents, task)
            
            # 4. Aggregate results
            final_result = self.aggregate_results(results, task)
            
            self.active_tasks[task_id]["status"] = "completed"
            return final_result
            
        except Exception as e:
            self.active_tasks[task_id]["status"] = "failed"
            return {"error": str(e), "task_id": task_id}
    
    def determine_required_agents(self, task: Dict[str, Any]) -> List[str]:
        """Determine which agents are needed for the task"""
        query = task.get("query", "").lower()
        required = []
        
        # Always need orchestrator for coordination
        if "OrchestratorAgent" in self.agents:
            required.append("OrchestratorAgent")
        
        # Knowledge retrieval tasks
        if any(keyword in query for keyword in ["policy", "information", "what is", "how to"]):
            if "KnowledgeAgent" in self.agents:
                required.append("KnowledgeAgent")
        
        # Reasoning tasks
        if any(keyword in query for keyword in ["compare", "analyze", "why", "explain"]):
            if "ReasoningAgent" in self.agents:
                required.append("ReasoningAgent")
        
        return required
    
    def check_agent_availability(self, required_agents: List[str]) -> List[str]:
        """Check which required agents are available"""
        available = []
        
        for agent_id in required_agents:
            if agent_id in self.agents:
                agent_info = self.agents[agent_id]
                if agent_info["status"] in ["idle", "ready"]:
                    available.append(agent_id)
        
        return available
    
    async def execute_coordinated_task(self, task_id: str, agent_ids: List[str], task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task with coordinated agents"""
        results = {}
        
        # Execute tasks in parallel where possible
        agent_tasks = []
        
        for agent_id in agent_ids:
            agent_instance = self.agents[agent_id]["instance"]
            
            # Update agent status
            self.agents[agent_id]["status"] = "busy"
            self.agents[agent_id]["last_active"] = datetime.now()
            
            # Create agent-specific task
            agent_task = self.create_agent_task(agent_id, task)
            agent_tasks.append(self.execute_agent_task(agent_id, agent_instance, agent_task))
        
        # Wait for all agents to complete
        agent_results = await asyncio.gather(*agent_tasks, return_exceptions=True)
        
        # Process results
        for i, agent_id in enumerate(agent_ids):
            result = agent_results[i]
            
            if isinstance(result, Exception):
                results[agent_id] = {"error": str(result)}
            else:
                results[agent_id] = result
            
            # Reset agent status
            self.agents[agent_id]["status"] = "idle"
        
        return results
    
    def create_agent_task(self, agent_id: str, original_task: Dict[str, Any]) -> Dict[str, Any]:
        """Create agent-specific task from original task"""
        # Customize task based on agent type
        agent_task = original_task.copy()
        
        if agent_id == "KnowledgeAgent":
            agent_task["type"] = "knowledge_retrieval"
        elif agent_id == "ReasoningAgent":
            agent_task["type"] = "reasoning"
        elif agent_id == "OrchestratorAgent":
            agent_task["type"] = "orchestration"
        
        return agent_task
    
    async def execute_agent_task(self, agent_id: str, agent_instance, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task on specific agent"""
        try:
            result = await agent_instance.process_task(task)
            
            # Log coordination
            self.coordination_history.append({
                "timestamp": datetime.now().isoformat(),
                "agent_id": agent_id,
                "task_type": task.get("type", "unknown"),
                "status": "completed"
            })
            
            return result
            
        except Exception as e:
            self.coordination_history.append({
                "timestamp": datetime.now().isoformat(),
                "agent_id": agent_id,
                "task_type": task.get("type", "unknown"),
                "status": "failed",
                "error": str(e)
            })
            
            raise e
    
    def aggregate_results(self, agent_results: Dict[str, Any], original_task: Dict[str, Any]) -> Dict[str, Any]:
        """Aggregate results from multiple agents"""
        aggregated = {
            "query": original_task.get("query", ""),
            "agents_used": list(agent_results.keys()),
            "combined_results": [],
            "confidence_scores": [],
            "coordination_success": True
        }
        
        # Combine results from all agents
        for agent_id, result in agent_results.items():
            if "error" not in result:
                if "results" in result:
                    aggregated["combined_results"].extend(result["results"])
                if "confidence" in result:
                    aggregated["confidence_scores"].append(result["confidence"])
            else:
                aggregated["coordination_success"] = False
        
        # Calculate overall confidence
        if aggregated["confidence_scores"]:
            aggregated["overall_confidence"] = sum(aggregated["confidence_scores"]) / len(aggregated["confidence_scores"])
        else:
            aggregated["overall_confidence"] = 0.0
        
        return aggregated
    
    def get_coordination_status(self) -> Dict[str, Any]:
        """Get current coordination status"""
        return {
            "registered_agents": len(self.agents),
            "active_tasks": len([t for t in self.active_tasks.values() if t["status"] == "running"]),
            "agent_statuses": {aid: info["status"] for aid, info in self.agents.items()},
            "recent_coordination": self.coordination_history[-10:]
        }

    async def coordinate_with_pattern(self, task: Dict[str, Any], pattern: CoordinationPattern = CoordinationPattern.HIERARCHICAL) -> Dict[str, Any]:
        """Coordinate agents using specific pattern"""
        if pattern not in self.coordination_patterns:
            raise ValueError(f"Unknown coordination pattern: {pattern}")

        # Add memory context to task
        if self.memory_system:
            memory_context = await self.memory_system.get_relevant_context(task)
            task["memory_context"] = memory_context

        result = await self.coordination_patterns[pattern](task)

        # Store interaction in memory
        if self.memory_system:
            await self.memory_system.store_interaction(task, result)

        return result

    async def hierarchical_coordination(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced hierarchical coordination with knowledge fusion"""
        orchestrator = self.agents.get("OrchestratorAgent", {}).get("instance")
        if not orchestrator:
            return {"error": "No orchestrator agent available"}

        # Task decomposition
        subtasks = await orchestrator.plan_task_decomposition(task)

        # Agent allocation
        agent_assignments = await orchestrator.allocate_agents(subtasks)

        # Execute with knowledge sharing
        results = await self.execute_parallel_tasks_with_knowledge_sharing(agent_assignments)

        # Apply knowledge fusion
        if self.knowledge_fusion:
            fused_knowledge = await self.knowledge_fusion.fuse_multi_agent_results(results)
            results["fused_knowledge"] = fused_knowledge

        # Synthesize with verification
        final_result = await orchestrator.synthesize_response(results, task)

        # Critic agent verification
        if "CriticAgent" in self.agents:
            critic = self.agents["CriticAgent"]["instance"]
            verification = await critic.verify_response(final_result, task)
            final_result["verification"] = verification

            if verification.get("confidence", 1.0) < 0.7:
                final_result = await self.request_revision(final_result, task, verification)

        return final_result

    async def collaborative_coordination(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Collaborative coordination pattern"""
        relevant_agents = self.select_relevant_agents(task)

        # Create shared workspace
        workspace = {
            "task": task,
            "shared_knowledge": {},
            "partial_solutions": {},
            "consensus": None
        }

        # Agents work collaboratively
        for agent_id in relevant_agents:
            if agent_id in self.agents:
                agent = self.agents[agent_id]["instance"]
                contribution = await self.contribute_to_workspace(agent, workspace)
                workspace["partial_solutions"][agent_id] = contribution

        # Reach consensus
        consensus = await self.reach_consensus(workspace)
        return consensus

    async def specialist_consultation(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Specialist consultation pattern"""
        primary_agent_id = self.select_primary_agent(task)
        if primary_agent_id not in self.agents:
            return {"error": f"Primary agent {primary_agent_id} not available"}

        primary_agent = self.agents[primary_agent_id]["instance"]

        # Identify specialist needs
        specialist_needs = await self.identify_specialist_needs(primary_agent, task)

        # Consult specialists
        specialist_inputs = {}
        for specialist_type in specialist_needs:
            specialist_id = self.find_specialist(specialist_type)
            if specialist_id and specialist_id in self.agents:
                specialist = self.agents[specialist_id]["instance"]
                specialist_input = await specialist.process_task(task)
                specialist_inputs[specialist_type] = specialist_input

        # Integrate specialist inputs
        final_result = await self.integrate_specialist_inputs(primary_agent, task, specialist_inputs)
        return final_result

    async def execute_parallel_tasks_with_knowledge_sharing(self, agent_assignments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute tasks in parallel with knowledge sharing"""
        results = {}
        shared_knowledge = {}

        # Execute tasks
        for agent_id, subtask in agent_assignments.items():
            if agent_id in self.agents:
                agent = self.agents[agent_id]["instance"]

                # Add shared knowledge to subtask
                subtask["shared_knowledge"] = shared_knowledge

                # Execute subtask
                result = await agent.process_task(subtask)
                results[agent_id] = result

                # Update shared knowledge
                if "knowledge_contribution" in result:
                    shared_knowledge.update(result["knowledge_contribution"])

        results["shared_knowledge"] = shared_knowledge
        return results

    async def request_revision(self, result: Dict[str, Any], task: Dict[str, Any],
                              verification: Dict[str, Any]) -> Dict[str, Any]:
        """Request revision based on critic feedback"""
        revision_task = {
            "type": "revision",
            "original_task": task,
            "original_result": result,
            "feedback": verification,
            "issues": verification.get("issues", []),
            "recommendations": verification.get("recommendations", [])
        }

        # Try to get the original agent to revise
        orchestrator = self.agents.get("OrchestratorAgent", {}).get("instance")
        if orchestrator:
            revised_result = await orchestrator.revise_response(revision_task)
            return revised_result

        return result  # Return original if no revision possible

    def select_relevant_agents(self, task: Dict[str, Any]) -> List[str]:
        """Select relevant agents for collaborative coordination"""
        relevant_agents = []

        # Extract task characteristics
        organization = task.get("organization", "").lower()
        department = task.get("department", "").lower()
        query = task.get("query", "").lower()

        # Select based on organization
        if "nuvoai" in organization:
            relevant_agents.extend(["NuvoAiAgent", "NuvoAiHRAgent", "NuvoAiTechnicalAgent"])
        elif "meril" in organization:
            relevant_agents.extend(["MerilAgent", "MerilHRAgent", "MerilTechnicalAgent"])

        # Select based on department
        if "hr" in department or "human" in department:
            relevant_agents.extend(["HRAgent", "NuvoAiHRAgent", "MerilHRAgent"])
        elif "technical" in department or "tech" in department:
            relevant_agents.extend(["TechnicalAgent", "NuvoAiTechnicalAgent", "MerilTechnicalAgent"])
        elif "finance" in department:
            relevant_agents.extend(["FinanceAgent"])

        # Select based on query content
        if any(term in query for term in ["leave", "policy", "vacation"]):
            relevant_agents.extend(["HRAgent", "PolicyAgent"])
        elif any(term in query for term in ["technical", "system", "software"]):
            relevant_agents.extend(["TechnicalAgent"])

        # Remove duplicates and filter existing agents
        relevant_agents = list(set(relevant_agents))
        return [agent_id for agent_id in relevant_agents if agent_id in self.agents]

    async def reach_consensus(self, workspace: Dict[str, Any]) -> Dict[str, Any]:
        """Reach consensus from collaborative workspace"""
        partial_solutions = workspace["partial_solutions"]

        if not partial_solutions:
            return {"error": "No solutions to reach consensus on"}

        # Simple consensus mechanism - weighted voting
        consensus_content = []
        total_confidence = 0

        for agent_id, solution in partial_solutions.items():
            if isinstance(solution, dict) and "results" in solution:
                confidence = solution.get("confidence", 0.5)
                total_confidence += confidence

                for result in solution["results"]:
                    if "text" in result:
                        consensus_content.append({
                            "content": result["text"],
                            "weight": confidence,
                            "agent": agent_id
                        })

        # Combine weighted content
        if consensus_content:
            combined_text = " | ".join([
                f"[{item['agent']}:{item['weight']:.2f}] {item['content']}"
                for item in consensus_content
            ])

            avg_confidence = total_confidence / len(partial_solutions)

            return {
                "results": [{"text": combined_text, "score": avg_confidence}],
                "consensus_method": "weighted_voting",
                "confidence": avg_confidence,
                "participating_agents": list(partial_solutions.keys())
            }

        return {"error": "Could not reach consensus"}

    def select_primary_agent(self, task: Dict[str, Any]) -> str:
        """Select primary agent for specialist consultation"""
        organization = task.get("organization", "").lower()
        department = task.get("department", "").lower()

        # Priority order for primary agent selection
        if "nuvoai" in organization:
            if "hr" in department:
                return "NuvoAiHRAgent"
            elif "technical" in department:
                return "NuvoAiTechnicalAgent"
            else:
                return "NuvoAiAgent"
        elif "meril" in organization:
            if "hr" in department:
                return "MerilHRAgent"
            elif "technical" in department:
                return "MerilTechnicalAgent"
            else:
                return "MerilAgent"

        # Default to orchestrator
        return "OrchestratorAgent"

    async def identify_specialist_needs(self, primary_agent, task: Dict[str, Any]) -> List[str]:
        """Identify what specialists are needed"""
        # This would typically be done by the primary agent
        # For now, use simple heuristics
        query = task.get("query", "").lower()
        specialists_needed = []

        if any(term in query for term in ["leave", "policy", "hr"]):
            specialists_needed.append("hr_specialist")

        if any(term in query for term in ["technical", "system", "software"]):
            specialists_needed.append("technical_specialist")

        if any(term in query for term in ["finance", "budget", "cost"]):
            specialists_needed.append("finance_specialist")

        if any(term in query for term in ["legal", "compliance", "regulation"]):
            specialists_needed.append("legal_specialist")

        return specialists_needed

    def find_specialist(self, specialist_type: str) -> Optional[str]:
        """Find appropriate specialist agent"""
        specialist_mapping = {
            "hr_specialist": ["HRAgent", "NuvoAiHRAgent", "MerilHRAgent"],
            "technical_specialist": ["TechnicalAgent", "NuvoAiTechnicalAgent", "MerilTechnicalAgent"],
            "finance_specialist": ["FinanceAgent"],
            "legal_specialist": ["LegalAgent"],
            "policy_specialist": ["PolicyAgent"]
        }

        candidates = specialist_mapping.get(specialist_type, [])

        # Return first available specialist
        for candidate in candidates:
            if candidate in self.agents:
                return candidate

        return None

    async def contribute_to_workspace(self, agent, workspace: Dict[str, Any]) -> Dict[str, Any]:
        """Agent contributes to collaborative workspace"""
        task = workspace["task"]
        shared_knowledge = workspace["shared_knowledge"]

        # Add shared knowledge to task
        enhanced_task = task.copy()
        enhanced_task["shared_knowledge"] = shared_knowledge

        # Get agent contribution
        contribution = await agent.process_task(enhanced_task)

        # Update shared knowledge with agent's contribution
        if "knowledge_contribution" in contribution:
            workspace["shared_knowledge"].update(contribution["knowledge_contribution"])

        return contribution

    async def integrate_specialist_inputs(self, primary_agent, task: Dict[str, Any],
                                        specialist_inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Integrate specialist inputs into final result"""
        integration_task = {
            "type": "integration",
            "original_task": task,
            "specialist_inputs": specialist_inputs
        }

        # Let primary agent integrate the inputs
        if hasattr(primary_agent, 'integrate_specialist_inputs'):
            return await primary_agent.integrate_specialist_inputs(integration_task)
        else:
            # Default integration
            combined_results = []
            total_confidence = 0

            for specialist_type, input_data in specialist_inputs.items():
                if isinstance(input_data, dict) and "results" in input_data:
                    combined_results.extend(input_data["results"])
                    total_confidence += input_data.get("confidence", 0.5)

            avg_confidence = total_confidence / len(specialist_inputs) if specialist_inputs else 0.5

            return {
                "results": combined_results,
                "confidence": avg_confidence,
                "integration_method": "default",
                "specialist_types": list(specialist_inputs.keys())
            }