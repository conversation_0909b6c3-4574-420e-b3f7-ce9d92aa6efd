"""Phase 2: AI Foundation Testing - According to Implementation Roadmap."""

import asyncio
import sys
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(__file__))

class Phase2AIFoundationTester:
    """Test Phase 2: AI Foundation components"""
    
    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.phase_score = 0.0
    
    async def run_phase_2_tests(self):
        """Run all Phase 2 AI Foundation tests"""
        print("🧠 PHASE 2: AI FOUNDATION TESTING")
        print("="*70)
        print("Testing according to Implementation Roadmap Phase 2")
        print("="*70)
        
        # Phase 2.1: NLP Pipeline - Intent Recognition
        await self.test_nlp_intent_recognition()
        
        # Phase 2.2: NLP Pipeline - Entity Extraction
        await self.test_nlp_entity_extraction()
        
        # Phase 2.3: Knowledge Graph Foundation
        await self.test_knowledge_graph_foundation()
        
        # Phase 2.4: Vector Search Implementation
        await self.test_vector_search_implementation()
        
        # Generate Phase 2 report
        await self.generate_phase_2_report()
    
    async def test_nlp_intent_recognition(self):
        """Test Phase 2.1: NLP Pipeline - Intent Recognition"""
        print("\n🎯 Phase 2.1: NLP Pipeline - Intent Recognition")
        print("-" * 40)
        
        # Test intent recognition components
        await self.test_intent_recognition_components()
        
        # Test query decomposition
        await self.test_query_decomposition()
    
    async def test_intent_recognition_components(self):
        """Test intent recognition components"""
        try:
            intent_components = [
                "ai/nlp/intent",
                "ai/nlp/decomposition",
                "backend/nlp"
            ]
            
            present_components = []
            for component in intent_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            # Check for specific intent recognition files
            intent_files = []
            for root, dirs, files in os.walk("ai/nlp"):
                for file in files:
                    if "intent" in file.lower() or "classification" in file.lower():
                        intent_files.append(os.path.join(root, file))
            
            if present_components or intent_files:
                self.record_test("Intent Recognition Components", True, 
                               f"Components: {present_components}, Files: {len(intent_files)}")
                print(f"✅ Intent recognition components: PASSED - {len(present_components)} components, {len(intent_files)} files")
            else:
                self.record_test("Intent Recognition Components", False, "No intent recognition components found")
                print("❌ Intent recognition components: FAILED - No components found")
                
        except Exception as e:
            self.record_test("Intent Recognition Components", False, str(e))
            print(f"❌ Intent recognition components: ERROR - {str(e)}")
    
    async def test_query_decomposition(self):
        """Test query decomposition system"""
        try:
            decomposition_components = [
                "ai/nlp/decomposition",
                "agents/coordinator/task_decomposition.py",
                "agents/orchestrator/orchestrator_agent.py"
            ]
            
            present_components = []
            for component in decomposition_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            if present_components:
                # Test if orchestrator has decomposition methods
                orchestrator_path = "agents/orchestrator/orchestrator_agent.py"
                if os.path.exists(orchestrator_path):
                    with open(orchestrator_path, 'r') as f:
                        content = f.read()
                        if "plan_task_decomposition" in content or "decomposition" in content.lower():
                            self.record_test("Query Decomposition", True, 
                                           f"Decomposition implemented in {len(present_components)} components")
                            print(f"✅ Query decomposition: PASSED - {len(present_components)} components")
                        else:
                            self.record_test("Query Decomposition", False, "Decomposition methods not found")
                            print("❌ Query decomposition: FAILED - Methods not implemented")
                else:
                    self.record_test("Query Decomposition", True, f"Components present: {present_components}")
                    print(f"✅ Query decomposition: PASSED - {len(present_components)} components")
            else:
                self.record_test("Query Decomposition", False, "No decomposition components found")
                print("❌ Query decomposition: FAILED - No components")
                
        except Exception as e:
            self.record_test("Query Decomposition", False, str(e))
            print(f"❌ Query decomposition: ERROR - {str(e)}")
    
    async def test_nlp_entity_extraction(self):
        """Test Phase 2.2: NLP Pipeline - Entity Extraction"""
        print("\n🏷️  Phase 2.2: NLP Pipeline - Entity Extraction")
        print("-" * 40)
        
        # Test entity extraction components
        await self.test_entity_extraction_components()
        
        # Test entity linking
        await self.test_entity_linking()
    
    async def test_entity_extraction_components(self):
        """Test entity extraction components"""
        try:
            entity_components = [
                "ai/nlp/entity",
                "backend/nlp/entity",
                "ai/nlp"
            ]
            
            present_components = []
            entity_files = []
            
            for component in entity_components:
                if os.path.exists(component):
                    present_components.append(component)
                    
                    # Look for entity-related files
                    if os.path.isdir(component):
                        for root, dirs, files in os.walk(component):
                            for file in files:
                                if "entity" in file.lower() or "ner" in file.lower():
                                    entity_files.append(os.path.join(root, file))
            
            if present_components or entity_files:
                self.record_test("Entity Extraction Components", True, 
                               f"Components: {len(present_components)}, Files: {len(entity_files)}")
                print(f"✅ Entity extraction components: PASSED - {len(present_components)} components")
            else:
                self.record_test("Entity Extraction Components", False, "No entity extraction components found")
                print("❌ Entity extraction components: FAILED - No components found")
                
        except Exception as e:
            self.record_test("Entity Extraction Components", False, str(e))
            print(f"❌ Entity extraction components: ERROR - {str(e)}")
    
    async def test_entity_linking(self):
        """Test entity linking capabilities"""
        try:
            # Check for entity linking in knowledge graph or NLP components
            linking_indicators = [
                "ai/knowledge/graph_search",
                "ai/nlp/entity",
                "backend/knowledge"
            ]
            
            linking_present = any(os.path.exists(path) for path in linking_indicators)
            
            if linking_present:
                self.record_test("Entity Linking", True, "Entity linking infrastructure present")
                print("✅ Entity linking: PASSED")
            else:
                self.record_test("Entity Linking", False, "No entity linking infrastructure found")
                print("❌ Entity linking: FAILED - No infrastructure")
                
        except Exception as e:
            self.record_test("Entity Linking", False, str(e))
            print(f"❌ Entity linking: ERROR - {str(e)}")
    
    async def test_knowledge_graph_foundation(self):
        """Test Phase 2.3: Knowledge Graph Foundation"""
        print("\n🕸️  Phase 2.3: Knowledge Graph Foundation")
        print("-" * 40)
        
        # Test knowledge graph construction
        await self.test_knowledge_graph_construction()
        
        # Test graph embeddings
        await self.test_graph_embeddings()
        
        # Test graph reasoning
        await self.test_graph_reasoning()
    
    async def test_knowledge_graph_construction(self):
        """Test knowledge graph construction pipeline"""
        try:
            kg_components = [
                "ai/knowledge/graph_search",
                "ai/knowledge",
                "backend/knowledge"
            ]
            
            present_components = []
            for component in kg_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            # Check for graph-related files
            graph_files = []
            if os.path.exists("ai/knowledge"):
                for root, dirs, files in os.walk("ai/knowledge"):
                    for file in files:
                        if "graph" in file.lower() or "neo4j" in file.lower():
                            graph_files.append(file)
            
            if present_components:
                self.record_test("Knowledge Graph Construction", True, 
                               f"Components: {present_components}, Graph files: {len(graph_files)}")
                print(f"✅ Knowledge graph construction: PASSED - {len(present_components)} components")
            else:
                self.record_test("Knowledge Graph Construction", False, "No knowledge graph components found")
                print("❌ Knowledge graph construction: FAILED - No components")
                
        except Exception as e:
            self.record_test("Knowledge Graph Construction", False, str(e))
            print(f"❌ Knowledge graph construction: ERROR - {str(e)}")
    
    async def test_graph_embeddings(self):
        """Test graph embedding generation"""
        try:
            # Check for embedding-related components
            embedding_indicators = [
                "ai/knowledge/vector_search",
                "ai/knowledge/fusion",
                "backend/embeddings"
            ]
            
            embedding_present = any(os.path.exists(path) for path in embedding_indicators)
            
            if embedding_present:
                self.record_test("Graph Embeddings", True, "Graph embedding infrastructure present")
                print("✅ Graph embeddings: PASSED")
            else:
                self.record_test("Graph Embeddings", False, "No graph embedding infrastructure found")
                print("❌ Graph embeddings: FAILED - No infrastructure")
                
        except Exception as e:
            self.record_test("Graph Embeddings", False, str(e))
            print(f"❌ Graph embeddings: ERROR - {str(e)}")
    
    async def test_graph_reasoning(self):
        """Test knowledge graph reasoning capabilities"""
        try:
            reasoning_components = [
                "ai/reasoning",
                "ai/knowledge/graph_search",
                "agents/specialized/reasoning_agent.py"
            ]
            
            present_components = []
            for component in reasoning_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            if present_components:
                self.record_test("Graph Reasoning", True, f"Reasoning components: {present_components}")
                print(f"✅ Graph reasoning: PASSED - {len(present_components)} components")
            else:
                self.record_test("Graph Reasoning", False, "No graph reasoning components found")
                print("❌ Graph reasoning: FAILED - No components")
                
        except Exception as e:
            self.record_test("Graph Reasoning", False, str(e))
            print(f"❌ Graph reasoning: ERROR - {str(e)}")
    
    async def test_vector_search_implementation(self):
        """Test Phase 2.4: Vector Search Implementation"""
        print("\n🔍 Phase 2.4: Vector Search Implementation")
        print("-" * 40)
        
        # Test document processing
        await self.test_document_processing()
        
        # Test embedding generation
        await self.test_embedding_generation()
        
        # Test vector search service
        await self.test_vector_search_service()
        
        # Test hybrid search
        await self.test_hybrid_search()
    
    async def test_document_processing(self):
        """Test document processing pipeline"""
        try:
            doc_components = [
                "ai/knowledge/document_processor.py",
                "backend/document_processing",
                "ai/knowledge"
            ]
            
            present_components = []
            for component in doc_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            if present_components:
                self.record_test("Document Processing", True, f"Processing components: {present_components}")
                print(f"✅ Document processing: PASSED - {len(present_components)} components")
            else:
                self.record_test("Document Processing", False, "No document processing components found")
                print("❌ Document processing: FAILED - No components")
                
        except Exception as e:
            self.record_test("Document Processing", False, str(e))
            print(f"❌ Document processing: ERROR - {str(e)}")
    
    async def test_embedding_generation(self):
        """Test embedding generation capabilities"""
        try:
            embedding_components = [
                "ai/knowledge/vector_search",
                "backend/embeddings",
                "ai/llm"
            ]
            
            present_components = []
            for component in embedding_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            if present_components:
                self.record_test("Embedding Generation", True, f"Embedding components: {present_components}")
                print(f"✅ Embedding generation: PASSED - {len(present_components)} components")
            else:
                self.record_test("Embedding Generation", False, "No embedding generation components found")
                print("❌ Embedding generation: FAILED - No components")
                
        except Exception as e:
            self.record_test("Embedding Generation", False, str(e))
            print(f"❌ Embedding generation: ERROR - {str(e)}")
    
    async def test_vector_search_service(self):
        """Test vector search service"""
        try:
            vector_components = [
                "ai/knowledge/vector_search",
                "milvus-standalone",
                "backend/vector_search"
            ]
            
            present_components = []
            for component in vector_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            # Check for vector search files
            vector_files = []
            if os.path.exists("ai/knowledge/vector_search"):
                vector_files = [f for f in os.listdir("ai/knowledge/vector_search") 
                              if f.endswith('.py') and f != '__init__.py']
            
            if present_components:
                self.record_test("Vector Search Service", True, 
                               f"Components: {present_components}, Files: {len(vector_files)}")
                print(f"✅ Vector search service: PASSED - {len(present_components)} components")
            else:
                self.record_test("Vector Search Service", False, "No vector search service found")
                print("❌ Vector search service: FAILED - No service")
                
        except Exception as e:
            self.record_test("Vector Search Service", False, str(e))
            print(f"❌ Vector search service: ERROR - {str(e)}")
    
    async def test_hybrid_search(self):
        """Test hybrid search capabilities"""
        try:
            # Check for hybrid search implementation
            hybrid_indicators = [
                "ai/knowledge/fusion",
                "ai/knowledge/vector_search",
                "ai/knowledge/graph_search"
            ]
            
            present_indicators = []
            for indicator in hybrid_indicators:
                if os.path.exists(indicator):
                    present_indicators.append(indicator)
            
            # Hybrid search requires both vector and graph components
            if len(present_indicators) >= 2:
                self.record_test("Hybrid Search", True, f"Hybrid components: {present_indicators}")
                print(f"✅ Hybrid search: PASSED - {len(present_indicators)} components")
            else:
                self.record_test("Hybrid Search", False, f"Insufficient components for hybrid search: {present_indicators}")
                print(f"❌ Hybrid search: FAILED - Need more components")
                
        except Exception as e:
            self.record_test("Hybrid Search", False, str(e))
            print(f"❌ Hybrid search: ERROR - {str(e)}")
    
    def record_test(self, test_name: str, passed: bool, details: str):
        """Record test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
        
        self.test_results.append({
            "test_name": test_name,
            "passed": passed,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })
    
    async def generate_phase_2_report(self):
        """Generate Phase 2 test report"""
        self.phase_score = (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
        
        print("\n" + "="*70)
        print("📊 PHASE 2: AI FOUNDATION - TEST REPORT")
        print("="*70)
        
        print(f"📈 Phase 2 Success Rate: {self.phase_score:.1f}% ({self.passed_tests}/{self.total_tests})")
        print(f"✅ Passed Tests: {self.passed_tests}")
        print(f"❌ Failed Tests: {self.total_tests - self.passed_tests}")
        
        print("\n📋 Detailed Results:")
        print("-" * 70)
        
        for result in self.test_results:
            status = "✅ PASS" if result["passed"] else "❌ FAIL"
            print(f"{status} | {result['test_name']:<30} | {result['details']}")
        
        # Phase 2 Assessment
        print(f"\n🎯 Phase 2 Assessment:")
        if self.phase_score >= 80:
            print("🎉 PHASE 2: EXCELLENT - AI Foundation is solid")
        elif self.phase_score >= 60:
            print("👍 PHASE 2: GOOD - AI Foundation mostly ready, minor improvements needed")
        elif self.phase_score >= 40:
            print("⚠️  PHASE 2: NEEDS IMPROVEMENT - Several AI components missing")
        else:
            print("🚨 PHASE 2: CRITICAL - Major AI foundation components missing")
        
        # Save detailed report
        report_data = {
            "phase": "Phase 2: AI Foundation",
            "success_rate": self.phase_score,
            "total_tests": self.total_tests,
            "passed_tests": self.passed_tests,
            "detailed_results": self.test_results,
            "timestamp": datetime.now().isoformat()
        }
        
        with open("phase_2_ai_foundation_report.json", "w") as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n💾 Detailed Phase 2 report saved to: phase_2_ai_foundation_report.json")
        print("="*70)

async def main():
    """Run Phase 2 AI Foundation tests"""
    tester = Phase2AIFoundationTester()
    await tester.run_phase_2_tests()

if __name__ == "__main__":
    asyncio.run(main())
