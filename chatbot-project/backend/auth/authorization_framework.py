"""
Advanced Authorization Framework for CHaBot.
Implements RBAC, ABAC, resource-based permissions, and dynamic authorization.
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional, Set, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import uuid
import re
from abc import ABC, abstractmethod
import aioredis
from fastapi import HTTPException, Request, Depends
from functools import wraps


class PermissionType(Enum):
    ALLOW = "allow"
    DENY = "deny"


class ResourceType(Enum):
    CONVERSATION = "conversation"
    DOCUMENT = "document"
    USER = "user"
    ORGANIZATION = "organization"
    DEPARTMENT = "department"
    AGENT = "agent"
    SYSTEM = "system"
    API = "api"


class ActionType(Enum):
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    EXECUTE = "execute"
    MANAGE = "manage"
    ADMIN = "admin"


@dataclass
class Permission:
    id: str
    name: str
    description: str
    resource_type: ResourceType
    actions: List[ActionType]
    conditions: Dict[str, Any]
    metadata: Dict[str, Any]


@dataclass
class Role:
    id: str
    name: str
    description: str
    permissions: List[str]  # Permission IDs
    parent_roles: List[str]  # Parent role IDs for inheritance
    conditions: Dict[str, Any]
    is_system_role: bool
    created_at: datetime
    updated_at: datetime
    metadata: Dict[str, Any]


@dataclass
class Resource:
    id: str
    type: ResourceType
    name: str
    owner_id: str
    organization_id: str
    department_id: Optional[str]
    attributes: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


@dataclass
class AuthorizationContext:
    user_id: str
    user_roles: List[str]
    user_attributes: Dict[str, Any]
    resource: Optional[Resource]
    action: ActionType
    environment: Dict[str, Any]
    request_context: Dict[str, Any]


@dataclass
class AuthorizationResult:
    allowed: bool
    reason: str
    applied_policies: List[str]
    conditions: List[str]
    metadata: Dict[str, Any]


class PolicyEngine(ABC):
    """Abstract base class for policy engines."""
    
    @abstractmethod
    async def evaluate(self, context: AuthorizationContext) -> AuthorizationResult:
        """Evaluate authorization request."""
        pass


class RBACEngine(PolicyEngine):
    """Role-Based Access Control engine."""
    
    def __init__(self, permission_manager, role_manager):
        self.permission_manager = permission_manager
        self.role_manager = role_manager
        self.logger = logging.getLogger(__name__)
    
    async def evaluate(self, context: AuthorizationContext) -> AuthorizationResult:
        """Evaluate RBAC authorization."""
        try:
            # Get user roles (including inherited roles)
            all_roles = await self.role_manager.get_effective_roles(context.user_id)
            
            # Get permissions for all roles
            all_permissions = set()
            applied_policies = []
            
            for role_id in all_roles:
                role = await self.role_manager.get_role(role_id)
                if role:
                    # Check role conditions
                    if await self._check_role_conditions(role, context):
                        all_permissions.update(role.permissions)
                        applied_policies.append(f"role:{role.name}")
            
            # Check if any permission allows the action
            for permission_id in all_permissions:
                permission = await self.permission_manager.get_permission(permission_id)
                if permission and await self._check_permission(permission, context):
                    return AuthorizationResult(
                        allowed=True,
                        reason=f"Allowed by permission: {permission.name}",
                        applied_policies=applied_policies,
                        conditions=[],
                        metadata={'permission_id': permission_id}
                    )
            
            return AuthorizationResult(
                allowed=False,
                reason="No matching permissions found",
                applied_policies=applied_policies,
                conditions=[],
                metadata={}
            )
            
        except Exception as e:
            self.logger.error(f"RBAC evaluation error: {e}")
            return AuthorizationResult(
                allowed=False,
                reason=f"Evaluation error: {str(e)}",
                applied_policies=[],
                conditions=[],
                metadata={}
            )
    
    async def _check_role_conditions(self, role: Role, context: AuthorizationContext) -> bool:
        """Check if role conditions are met."""
        if not role.conditions:
            return True
        
        # Time-based conditions
        if 'time_restrictions' in role.conditions:
            time_restrictions = role.conditions['time_restrictions']
            current_hour = datetime.now().hour
            
            if 'allowed_hours' in time_restrictions:
                allowed_hours = time_restrictions['allowed_hours']
                if current_hour not in allowed_hours:
                    return False
        
        # IP-based conditions
        if 'ip_restrictions' in role.conditions:
            ip_restrictions = role.conditions['ip_restrictions']
            user_ip = context.request_context.get('ip_address')
            
            if 'allowed_ips' in ip_restrictions:
                allowed_ips = ip_restrictions['allowed_ips']
                if user_ip not in allowed_ips:
                    return False
        
        # Organization-based conditions
        if 'organization_restrictions' in role.conditions:
            org_restrictions = role.conditions['organization_restrictions']
            user_org = context.user_attributes.get('organization_id')
            
            if 'allowed_organizations' in org_restrictions:
                allowed_orgs = org_restrictions['allowed_organizations']
                if user_org not in allowed_orgs:
                    return False
        
        return True
    
    async def _check_permission(self, permission: Permission, context: AuthorizationContext) -> bool:
        """Check if permission applies to the context."""
        # Check resource type
        if context.resource and permission.resource_type != context.resource.type:
            return False
        
        # Check action
        if context.action not in permission.actions:
            return False
        
        # Check permission conditions
        if permission.conditions:
            return await self._evaluate_permission_conditions(permission.conditions, context)
        
        return True
    
    async def _evaluate_permission_conditions(self, conditions: Dict[str, Any], 
                                           context: AuthorizationContext) -> bool:
        """Evaluate permission conditions."""
        # Resource ownership condition
        if 'owner_only' in conditions and conditions['owner_only']:
            if not context.resource or context.resource.owner_id != context.user_id:
                return False
        
        # Department condition
        if 'same_department' in conditions and conditions['same_department']:
            user_dept = context.user_attributes.get('department_id')
            resource_dept = context.resource.department_id if context.resource else None
            if user_dept != resource_dept:
                return False
        
        # Organization condition
        if 'same_organization' in conditions and conditions['same_organization']:
            user_org = context.user_attributes.get('organization_id')
            resource_org = context.resource.organization_id if context.resource else None
            if user_org != resource_org:
                return False
        
        return True


class ABACEngine(PolicyEngine):
    """Attribute-Based Access Control engine."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.policies: List[Dict[str, Any]] = []
    
    def add_policy(self, policy: Dict[str, Any]):
        """Add ABAC policy."""
        self.policies.append(policy)
    
    async def evaluate(self, context: AuthorizationContext) -> AuthorizationResult:
        """Evaluate ABAC authorization."""
        try:
            applied_policies = []
            
            for policy in self.policies:
                if await self._evaluate_policy(policy, context):
                    effect = policy.get('effect', 'allow')
                    policy_name = policy.get('name', 'unnamed_policy')
                    applied_policies.append(policy_name)
                    
                    if effect == 'allow':
                        return AuthorizationResult(
                            allowed=True,
                            reason=f"Allowed by ABAC policy: {policy_name}",
                            applied_policies=applied_policies,
                            conditions=policy.get('conditions', []),
                            metadata={'policy': policy_name}
                        )
                    elif effect == 'deny':
                        return AuthorizationResult(
                            allowed=False,
                            reason=f"Denied by ABAC policy: {policy_name}",
                            applied_policies=applied_policies,
                            conditions=policy.get('conditions', []),
                            metadata={'policy': policy_name}
                        )
            
            return AuthorizationResult(
                allowed=False,
                reason="No matching ABAC policies",
                applied_policies=applied_policies,
                conditions=[],
                metadata={}
            )
            
        except Exception as e:
            self.logger.error(f"ABAC evaluation error: {e}")
            return AuthorizationResult(
                allowed=False,
                reason=f"Evaluation error: {str(e)}",
                applied_policies=[],
                conditions=[],
                metadata={}
            )
    
    async def _evaluate_policy(self, policy: Dict[str, Any], context: AuthorizationContext) -> bool:
        """Evaluate ABAC policy against context."""
        rules = policy.get('rules', [])
        
        for rule in rules:
            if not await self._evaluate_rule(rule, context):
                return False
        
        return True
    
    async def _evaluate_rule(self, rule: Dict[str, Any], context: AuthorizationContext) -> bool:
        """Evaluate individual ABAC rule."""
        rule_type = rule.get('type')
        
        if rule_type == 'user_attribute':
            return self._evaluate_user_attribute_rule(rule, context)
        elif rule_type == 'resource_attribute':
            return self._evaluate_resource_attribute_rule(rule, context)
        elif rule_type == 'environment':
            return self._evaluate_environment_rule(rule, context)
        elif rule_type == 'action':
            return self._evaluate_action_rule(rule, context)
        
        return False
    
    def _evaluate_user_attribute_rule(self, rule: Dict[str, Any], context: AuthorizationContext) -> bool:
        """Evaluate user attribute rule."""
        attribute = rule.get('attribute')
        operator = rule.get('operator', 'equals')
        value = rule.get('value')
        
        user_value = context.user_attributes.get(attribute)
        
        if operator == 'equals':
            return user_value == value
        elif operator == 'not_equals':
            return user_value != value
        elif operator == 'in':
            return user_value in value if isinstance(value, list) else False
        elif operator == 'not_in':
            return user_value not in value if isinstance(value, list) else True
        elif operator == 'greater_than':
            return user_value > value if isinstance(user_value, (int, float)) else False
        elif operator == 'less_than':
            return user_value < value if isinstance(user_value, (int, float)) else False
        
        return False
    
    def _evaluate_resource_attribute_rule(self, rule: Dict[str, Any], context: AuthorizationContext) -> bool:
        """Evaluate resource attribute rule."""
        if not context.resource:
            return False
        
        attribute = rule.get('attribute')
        operator = rule.get('operator', 'equals')
        value = rule.get('value')
        
        resource_value = context.resource.attributes.get(attribute)
        
        if operator == 'equals':
            return resource_value == value
        elif operator == 'not_equals':
            return resource_value != value
        elif operator == 'in':
            return resource_value in value if isinstance(value, list) else False
        elif operator == 'contains':
            return value in resource_value if isinstance(resource_value, str) else False
        
        return False
    
    def _evaluate_environment_rule(self, rule: Dict[str, Any], context: AuthorizationContext) -> bool:
        """Evaluate environment rule."""
        attribute = rule.get('attribute')
        operator = rule.get('operator', 'equals')
        value = rule.get('value')
        
        env_value = context.environment.get(attribute)
        
        if attribute == 'time':
            current_time = datetime.now()
            if operator == 'between':
                start_time, end_time = value
                return start_time <= current_time.hour <= end_time
        elif attribute == 'day_of_week':
            current_day = datetime.now().weekday()
            if operator == 'in':
                return current_day in value
        
        return False
    
    def _evaluate_action_rule(self, rule: Dict[str, Any], context: AuthorizationContext) -> bool:
        """Evaluate action rule."""
        allowed_actions = rule.get('actions', [])
        return context.action.value in allowed_actions


class PermissionManager:
    """Manages permissions and their definitions."""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
        self.permissions: Dict[str, Permission] = {}
        
        # Initialize default permissions
        asyncio.create_task(self._initialize_default_permissions())
    
    async def _initialize_default_permissions(self):
        """Initialize default system permissions."""
        default_permissions = [
            Permission(
                id="read_own_conversations",
                name="Read Own Conversations",
                description="Read conversations owned by the user",
                resource_type=ResourceType.CONVERSATION,
                actions=[ActionType.READ],
                conditions={"owner_only": True},
                metadata={}
            ),
            Permission(
                id="read_department_documents",
                name="Read Department Documents",
                description="Read documents within the same department",
                resource_type=ResourceType.DOCUMENT,
                actions=[ActionType.READ],
                conditions={"same_department": True},
                metadata={}
            ),
            Permission(
                id="manage_organization_users",
                name="Manage Organization Users",
                description="Manage users within the same organization",
                resource_type=ResourceType.USER,
                actions=[ActionType.CREATE, ActionType.READ, ActionType.UPDATE, ActionType.DELETE],
                conditions={"same_organization": True},
                metadata={}
            ),
            Permission(
                id="admin_system",
                name="System Administration",
                description="Full system administration access",
                resource_type=ResourceType.SYSTEM,
                actions=[ActionType.CREATE, ActionType.READ, ActionType.UPDATE, ActionType.DELETE, ActionType.ADMIN],
                conditions={},
                metadata={"system_permission": True}
            )
        ]
        
        for permission in default_permissions:
            await self.create_permission(permission)
    
    async def create_permission(self, permission: Permission) -> bool:
        """Create new permission."""
        try:
            self.permissions[permission.id] = permission
            
            # Store in Redis
            await self.redis_client.setex(
                f"permission:{permission.id}",
                86400,  # 24 hours
                json.dumps(asdict(permission), default=str)
            )
            
            self.logger.info(f"Created permission: {permission.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create permission: {e}")
            return False
    
    async def get_permission(self, permission_id: str) -> Optional[Permission]:
        """Get permission by ID."""
        try:
            # Check memory cache first
            if permission_id in self.permissions:
                return self.permissions[permission_id]
            
            # Check Redis
            data = await self.redis_client.get(f"permission:{permission_id}")
            if data:
                permission_dict = json.loads(data)
                permission_dict['resource_type'] = ResourceType(permission_dict['resource_type'])
                permission_dict['actions'] = [ActionType(action) for action in permission_dict['actions']]
                
                permission = Permission(**permission_dict)
                self.permissions[permission_id] = permission
                return permission
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting permission {permission_id}: {e}")
            return None
    
    async def update_permission(self, permission: Permission) -> bool:
        """Update existing permission."""
        try:
            self.permissions[permission.id] = permission
            
            await self.redis_client.setex(
                f"permission:{permission.id}",
                86400,
                json.dumps(asdict(permission), default=str)
            )
            
            self.logger.info(f"Updated permission: {permission.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update permission: {e}")
            return False
    
    async def delete_permission(self, permission_id: str) -> bool:
        """Delete permission."""
        try:
            if permission_id in self.permissions:
                del self.permissions[permission_id]
            
            await self.redis_client.delete(f"permission:{permission_id}")
            
            self.logger.info(f"Deleted permission: {permission_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete permission: {e}")
            return False
    
    def list_permissions(self) -> List[Permission]:
        """List all permissions."""
        return list(self.permissions.values())


class RoleManager:
    """Manages roles and role hierarchy."""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
        self.roles: Dict[str, Role] = {}
        self.user_roles: Dict[str, List[str]] = {}
        
        # Initialize default roles
        asyncio.create_task(self._initialize_default_roles())
    
    async def _initialize_default_roles(self):
        """Initialize default system roles."""
        default_roles = [
            Role(
                id="employee",
                name="Employee",
                description="Basic employee role",
                permissions=["read_own_conversations"],
                parent_roles=[],
                conditions={},
                is_system_role=True,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                metadata={}
            ),
            Role(
                id="manager",
                name="Manager",
                description="Department manager role",
                permissions=["read_own_conversations", "read_department_documents"],
                parent_roles=["employee"],
                conditions={},
                is_system_role=True,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                metadata={}
            ),
            Role(
                id="hr_admin",
                name="HR Administrator",
                description="HR administration role",
                permissions=["read_own_conversations", "read_department_documents", "manage_organization_users"],
                parent_roles=["manager"],
                conditions={"organization_restrictions": {"allowed_organizations": ["*"]}},
                is_system_role=True,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                metadata={}
            ),
            Role(
                id="system_admin",
                name="System Administrator",
                description="Full system administration role",
                permissions=["admin_system"],
                parent_roles=[],
                conditions={},
                is_system_role=True,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                metadata={}
            )
        ]
        
        for role in default_roles:
            await self.create_role(role)
    
    async def create_role(self, role: Role) -> bool:
        """Create new role."""
        try:
            self.roles[role.id] = role
            
            # Store in Redis
            await self.redis_client.setex(
                f"role:{role.id}",
                86400,  # 24 hours
                json.dumps(asdict(role), default=str)
            )
            
            self.logger.info(f"Created role: {role.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create role: {e}")
            return False
    
    async def get_role(self, role_id: str) -> Optional[Role]:
        """Get role by ID."""
        try:
            # Check memory cache first
            if role_id in self.roles:
                return self.roles[role_id]
            
            # Check Redis
            data = await self.redis_client.get(f"role:{role_id}")
            if data:
                role_dict = json.loads(data)
                role_dict['created_at'] = datetime.fromisoformat(role_dict['created_at'])
                role_dict['updated_at'] = datetime.fromisoformat(role_dict['updated_at'])
                
                role = Role(**role_dict)
                self.roles[role_id] = role
                return role
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting role {role_id}: {e}")
            return None
    
    async def assign_role_to_user(self, user_id: str, role_id: str) -> bool:
        """Assign role to user."""
        try:
            if user_id not in self.user_roles:
                self.user_roles[user_id] = []
            
            if role_id not in self.user_roles[user_id]:
                self.user_roles[user_id].append(role_id)
                
                # Store in Redis
                await self.redis_client.setex(
                    f"user_roles:{user_id}",
                    86400,
                    json.dumps(self.user_roles[user_id])
                )
                
                self.logger.info(f"Assigned role {role_id} to user {user_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to assign role: {e}")
            return False
    
    async def remove_role_from_user(self, user_id: str, role_id: str) -> bool:
        """Remove role from user."""
        try:
            if user_id in self.user_roles and role_id in self.user_roles[user_id]:
                self.user_roles[user_id].remove(role_id)
                
                # Update Redis
                await self.redis_client.setex(
                    f"user_roles:{user_id}",
                    86400,
                    json.dumps(self.user_roles[user_id])
                )
                
                self.logger.info(f"Removed role {role_id} from user {user_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to remove role: {e}")
            return False
    
    async def get_user_roles(self, user_id: str) -> List[str]:
        """Get direct roles assigned to user."""
        try:
            # Check memory cache first
            if user_id in self.user_roles:
                return self.user_roles[user_id]
            
            # Check Redis
            data = await self.redis_client.get(f"user_roles:{user_id}")
            if data:
                roles = json.loads(data)
                self.user_roles[user_id] = roles
                return roles
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting user roles: {e}")
            return []
    
    async def get_effective_roles(self, user_id: str) -> List[str]:
        """Get all effective roles for user (including inherited roles)."""
        direct_roles = await self.get_user_roles(user_id)
        effective_roles = set(direct_roles)
        
        # Add inherited roles
        for role_id in direct_roles:
            inherited = await self._get_inherited_roles(role_id)
            effective_roles.update(inherited)
        
        return list(effective_roles)
    
    async def _get_inherited_roles(self, role_id: str) -> Set[str]:
        """Get inherited roles from role hierarchy."""
        inherited = set()
        role = await self.get_role(role_id)
        
        if role:
            for parent_role_id in role.parent_roles:
                inherited.add(parent_role_id)
                # Recursively get parent's inherited roles
                parent_inherited = await self._get_inherited_roles(parent_role_id)
                inherited.update(parent_inherited)
        
        return inherited


class AuthorizationFramework:
    """Main authorization framework."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.logger = logging.getLogger(__name__)
        
        # Initialize Redis
        self.redis_client = None
        asyncio.create_task(self._initialize_redis(redis_url))
        
        # Initialize managers
        self.permission_manager = None
        self.role_manager = None
        
        # Initialize policy engines
        self.rbac_engine = None
        self.abac_engine = ABACEngine()
        
        # Resource registry
        self.resources: Dict[str, Resource] = {}
    
    async def _initialize_redis(self, redis_url: str):
        """Initialize Redis and managers."""
        try:
            self.redis_client = aioredis.from_url(redis_url)
            
            # Initialize managers
            self.permission_manager = PermissionManager(self.redis_client)
            self.role_manager = RoleManager(self.redis_client)
            
            # Initialize RBAC engine
            self.rbac_engine = RBACEngine(self.permission_manager, self.role_manager)
            
            self.logger.info("Authorization framework initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize authorization framework: {e}")
    
    async def authorize(self, user_id: str, action: ActionType, resource_id: str = None,
                       request_context: Dict[str, Any] = None) -> AuthorizationResult:
        """Main authorization method."""
        try:
            # Get user attributes (in production, fetch from database)
            user_attributes = await self._get_user_attributes(user_id)
            
            # Get resource if specified
            resource = None
            if resource_id:
                resource = await self._get_resource(resource_id)
            
            # Create authorization context
            context = AuthorizationContext(
                user_id=user_id,
                user_roles=await self.role_manager.get_user_roles(user_id),
                user_attributes=user_attributes,
                resource=resource,
                action=action,
                environment=self._get_environment_attributes(),
                request_context=request_context or {}
            )
            
            # Evaluate using RBAC first
            rbac_result = await self.rbac_engine.evaluate(context)
            if rbac_result.allowed:
                return rbac_result
            
            # If RBAC denies, try ABAC
            abac_result = await self.abac_engine.evaluate(context)
            if abac_result.allowed:
                return abac_result
            
            # Both engines deny
            return AuthorizationResult(
                allowed=False,
                reason="Access denied by all policy engines",
                applied_policies=rbac_result.applied_policies + abac_result.applied_policies,
                conditions=[],
                metadata={}
            )
            
        except Exception as e:
            self.logger.error(f"Authorization error: {e}")
            return AuthorizationResult(
                allowed=False,
                reason=f"Authorization error: {str(e)}",
                applied_policies=[],
                conditions=[],
                metadata={}
            )
    
    async def _get_user_attributes(self, user_id: str) -> Dict[str, Any]:
        """Get user attributes for authorization."""
        # In production, this would fetch from user database
        return {
            'organization_id': 'org_123',
            'department_id': 'dept_456',
            'job_title': 'Software Engineer',
            'security_clearance': 'standard',
            'employment_type': 'full_time'
        }
    
    async def _get_resource(self, resource_id: str) -> Optional[Resource]:
        """Get resource by ID."""
        # Check memory cache first
        if resource_id in self.resources:
            return self.resources[resource_id]
        
        # In production, fetch from database
        # For now, return a mock resource
        resource = Resource(
            id=resource_id,
            type=ResourceType.DOCUMENT,
            name="Sample Document",
            owner_id="user_123",
            organization_id="org_123",
            department_id="dept_456",
            attributes={'classification': 'internal'},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self.resources[resource_id] = resource
        return resource
    
    def _get_environment_attributes(self) -> Dict[str, Any]:
        """Get environment attributes."""
        now = datetime.now()
        return {
            'time': now.hour,
            'day_of_week': now.weekday(),
            'is_business_hours': 9 <= now.hour <= 17,
            'is_weekend': now.weekday() >= 5
        }
    
    # Decorator for FastAPI endpoints
    def require_permission(self, action: ActionType, resource_id_param: str = None):
        """Decorator to require specific permission for endpoint access."""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Extract request and user info
                request = None
                user_id = None
                
                for arg in args:
                    if isinstance(arg, Request):
                        request = arg
                        # Extract user ID from token (implementation depends on auth system)
                        user_id = request.state.user_id if hasattr(request.state, 'user_id') else None
                        break
                
                if not user_id:
                    raise HTTPException(status_code=401, detail="Authentication required")
                
                # Get resource ID if specified
                resource_id = None
                if resource_id_param and resource_id_param in kwargs:
                    resource_id = kwargs[resource_id_param]
                
                # Check authorization
                result = await self.authorize(
                    user_id=user_id,
                    action=action,
                    resource_id=resource_id,
                    request_context={
                        'ip_address': request.client.host if request else None,
                        'user_agent': request.headers.get('user-agent') if request else None
                    }
                )
                
                if not result.allowed:
                    raise HTTPException(status_code=403, detail=result.reason)
                
                # Store authorization result in request state
                if request:
                    request.state.authorization_result = result
                
                return await func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    async def add_abac_policy(self, policy: Dict[str, Any]):
        """Add ABAC policy."""
        self.abac_engine.add_policy(policy)
    
    async def create_permission(self, permission: Permission) -> bool:
        """Create new permission."""
        return await self.permission_manager.create_permission(permission)
    
    async def create_role(self, role: Role) -> bool:
        """Create new role."""
        return await self.role_manager.create_role(role)
    
    async def assign_role_to_user(self, user_id: str, role_id: str) -> bool:
        """Assign role to user."""
        return await self.role_manager.assign_role_to_user(user_id, role_id)
    
    def get_authorization_stats(self) -> Dict[str, Any]:
        """Get authorization framework statistics."""
        return {
            'permissions': len(self.permission_manager.permissions),
            'roles': len(self.role_manager.roles),
            'user_role_assignments': len(self.role_manager.user_roles),
            'abac_policies': len(self.abac_engine.policies),
            'cached_resources': len(self.resources)
        }


# Example ABAC policies
EXAMPLE_ABAC_POLICIES = [
    {
        'name': 'business_hours_access',
        'effect': 'allow',
        'rules': [
            {
                'type': 'environment',
                'attribute': 'time',
                'operator': 'between',
                'value': [9, 17]
            },
            {
                'type': 'user_attribute',
                'attribute': 'employment_type',
                'operator': 'equals',
                'value': 'full_time'
            }
        ]
    },
    {
        'name': 'sensitive_document_access',
        'effect': 'deny',
        'rules': [
            {
                'type': 'resource_attribute',
                'attribute': 'classification',
                'operator': 'equals',
                'value': 'confidential'
            },
            {
                'type': 'user_attribute',
                'attribute': 'security_clearance',
                'operator': 'not_equals',
                'value': 'high'
            }
        ]
    }
]


if __name__ == "__main__":
    # Example usage
    auth_framework = AuthorizationFramework()
    
    # Add ABAC policies
    # for policy in EXAMPLE_ABAC_POLICIES:
    #     await auth_framework.add_abac_policy(policy)
    
    # Assign role to user
    # await auth_framework.assign_role_to_user("user123", "employee")
    
    # Check authorization
    # result = await auth_framework.authorize("user123", ActionType.READ, "document123")