"""
Complete Authentication API for CHaBot.
Integrates all authentication components into a unified API.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import FastAPI, HTTPException, Depends, Request, Response, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from pydantic import BaseModel, EmailStr, validator
import bcrypt
import secrets
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import aioredis

# Import our authentication components
from .mfa_service import MFAService, MFAMethod, MFA_CONFIG_EXAMPLE
from .token_manager import EnhancedTokenManager, TOKEN_CONFIG_EXAMPLE
from .authorization_framework import AuthorizationFramework, ActionType, ResourceType
from .security_monitoring import SecurityMonitoringService, EventType, SECURITY_CONFIG_EXAMPLE
from ..integrations.ldap_connector import LDAPConnector, LDAP_CONFIG_EXAMPLES
from ..integrations.saml_sso import SAMLSSOProvider, SAML_CONFIG_EXAMPLE


# Pydantic models
class LoginRequest(BaseModel):
    username: str
    password: str
    remember_me: bool = False
    device_info: Optional[Dict[str, Any]] = None

class LoginResponse(BaseModel):
    access_token: str
    refresh_token: str
    id_token: str
    token_type: str = "Bearer"
    expires_in: int
    user: Dict[str, Any]
    permissions: List[str]
    roles: List[str]
    mfa_required: bool = False
    mfa_challenge: Optional[Dict[str, Any]] = None

class RegisterRequest(BaseModel):
    username: str
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    organization: str
    department: Optional[str] = None
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        if not any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in v):
            raise ValueError('Password must contain at least one special character')
        return v

class MFASetupRequest(BaseModel):
    method: str
    device_name: str
    metadata: Optional[Dict[str, Any]] = None

class MFAVerifyRequest(BaseModel):
    challenge_id: str
    code: str

class PasswordChangeRequest(BaseModel):
    current_password: str
    new_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

class TokenRefreshRequest(BaseModel):
    refresh_token: Optional[str] = None  # Can be in cookie instead

class UserProfileUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None
    department: Optional[str] = None
    phone: Optional[str] = None


# Rate limiting
limiter = Limiter(key_func=get_remote_address)

# Security headers middleware
class SecurityHeadersMiddleware:
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    headers = dict(message.get("headers", []))
                    
                    # Security headers
                    security_headers = {
                        b"x-content-type-options": b"nosniff",
                        b"x-frame-options": b"DENY",
                        b"x-xss-protection": b"1; mode=block",
                        b"strict-transport-security": b"max-age=31536000; includeSubDomains",
                        b"content-security-policy": b"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
                        b"referrer-policy": b"strict-origin-when-cross-origin",
                        b"permissions-policy": b"geolocation=(), microphone=(), camera=()"
                    }
                    
                    headers.update(security_headers)
                    message["headers"] = list(headers.items())
                
                await send(message)
            
            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)


class AuthenticationAPI:
    """Complete Authentication API."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize FastAPI app
        self.app = FastAPI(
            title="CHaBot Authentication API",
            description="Complete authentication system with MFA, RBAC, and security monitoring",
            version="1.0.0"
        )
        
        # Add middleware
        self._setup_middleware()
        
        # Initialize services
        self.redis_client = None
        self.mfa_service = None
        self.token_manager = None
        self.auth_framework = None
        self.security_service = None
        self.ldap_connector = None
        self.saml_provider = None
        
        # User database (in production, use proper database)
        self.users_db = {}
        
        # Initialize components
        asyncio.create_task(self._initialize_services())
        
        # Setup routes
        self._setup_routes()
    
    def _setup_middleware(self):
        """Setup middleware."""
        # CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config.get('allowed_origins', ['http://localhost:3000']),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Security headers
        self.app.add_middleware(SecurityHeadersMiddleware)
        
        # Trusted hosts
        self.app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=self.config.get('allowed_hosts', ['localhost', '127.0.0.1'])
        )
        
        # Compression
        self.app.add_middleware(GZipMiddleware, minimum_size=1000)
        
        # Rate limiting
        self.app.state.limiter = limiter
        self.app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
    
    async def _initialize_services(self):
        """Initialize authentication services."""
        try:
            # Redis
            redis_url = self.config.get('redis_url', 'redis://localhost:6379')
            self.redis_client = aioredis.from_url(redis_url)
            
            # MFA Service
            self.mfa_service = MFAService(
                self.config.get('mfa', MFA_CONFIG_EXAMPLE),
                redis_url
            )
            
            # Token Manager
            self.token_manager = EnhancedTokenManager(
                self.config.get('tokens', TOKEN_CONFIG_EXAMPLE),
                redis_url
            )
            
            # Authorization Framework
            self.auth_framework = AuthorizationFramework(redis_url)
            
            # Security Monitoring
            self.security_service = SecurityMonitoringService(
                self.config.get('security', SECURITY_CONFIG_EXAMPLE),
                redis_url
            )
            
            # LDAP Connector (if configured)
            if 'ldap' in self.config:
                self.ldap_connector = LDAPConnector(self.config['ldap'])
            
            # SAML Provider (if configured)
            if 'saml' in self.config:
                self.saml_provider = SAMLSSOProvider(self.config['saml'])
            
            self.logger.info("Authentication services initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize authentication services: {e}")
            raise
    
    def _setup_routes(self):
        """Setup API routes."""
        
        # Health check
        @self.app.get("/health")
        async def health_check():
            return {"status": "healthy", "timestamp": datetime.now().isoformat()}
        
        # Authentication routes
        @self.app.post("/auth/login", response_model=LoginResponse)
        @limiter.limit("10/minute")
        async def login(request: Request, login_data: LoginRequest):
            return await self._handle_login(request, login_data)
        
        @self.app.post("/auth/register")
        @limiter.limit("5/minute")
        async def register(request: Request, register_data: RegisterRequest):
            return await self._handle_register(request, register_data)
        
        @self.app.post("/auth/logout")
        async def logout(request: Request, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            return await self._handle_logout(request, credentials)
        
        @self.app.post("/auth/refresh")
        @limiter.limit("20/minute")
        async def refresh_token(request: Request, refresh_data: TokenRefreshRequest = None):
            return await self._handle_token_refresh(request, refresh_data)
        
        # MFA routes
        @self.app.post("/auth/mfa/setup")
        async def setup_mfa(request: Request, mfa_data: MFASetupRequest, 
                          credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            return await self._handle_mfa_setup(request, mfa_data, credentials)
        
        @self.app.post("/auth/mfa/verify")
        @limiter.limit("10/minute")
        async def verify_mfa(request: Request, verify_data: MFAVerifyRequest):
            return await self._handle_mfa_verify(request, verify_data)
        
        @self.app.get("/auth/mfa/devices")
        async def get_mfa_devices(request: Request, 
                                credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            return await self._handle_get_mfa_devices(request, credentials)
        
        # User management routes
        @self.app.get("/auth/me")
        async def get_current_user(request: Request, 
                                 credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            return await self._handle_get_current_user(request, credentials)
        
        @self.app.put("/auth/profile")
        async def update_profile(request: Request, profile_data: UserProfileUpdate,
                               credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            return await self._handle_update_profile(request, profile_data, credentials)
        
        @self.app.post("/auth/change-password")
        async def change_password(request: Request, password_data: PasswordChangeRequest,
                                credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            return await self._handle_change_password(request, password_data, credentials)
        
        # LDAP routes (if enabled)
        if self.ldap_connector:
            @self.app.post("/auth/ldap/sync")
            async def sync_ldap_users(request: Request,
                                    credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
                return await self._handle_ldap_sync(request, credentials)
        
        # SAML routes (if enabled)
        if self.saml_provider:
            @self.app.get("/auth/saml/metadata")
            async def get_saml_metadata():
                return Response(
                    content=self.saml_provider.get_sp_metadata(),
                    media_type="application/xml"
                )
            
            @self.app.get("/auth/saml/login")
            async def initiate_saml_login(relay_state: Optional[str] = None):
                return await self._handle_saml_login(relay_state)
            
            @self.app.post("/auth/saml/acs")
            async def saml_acs(request: Request):
                return await self._handle_saml_acs(request)
        
        # Admin routes
        @self.app.get("/auth/admin/users")
        async def list_users(request: Request,
                           credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            return await self._handle_list_users(request, credentials)
        
        @self.app.get("/auth/admin/security/dashboard")
        async def security_dashboard(request: Request,
                                   credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            return await self._handle_security_dashboard(request, credentials)
        
        @self.app.get("/auth/admin/compliance/report")
        async def compliance_report(request: Request, standard: str,
                                  credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            return await self._handle_compliance_report(request, standard, credentials)
    
    async def _handle_login(self, request: Request, login_data: LoginRequest) -> LoginResponse:
        """Handle user login."""
        try:
            # Log security event
            await self.security_service.log_security_event(
                EventType.LOGIN_SUCCESS if True else EventType.LOGIN_FAILURE,  # Will be updated based on result
                user_id=login_data.username,
                request=request,
                details={"login_method": "password", "remember_me": login_data.remember_me}
            )
            
            # Try LDAP authentication first (if configured)
            user = None
            if self.ldap_connector:
                success, ldap_user = await self.ldap_connector.authenticate_user(
                    login_data.username, login_data.password
                )
                if success and ldap_user:
                    user = {
                        "id": ldap_user.username,
                        "username": ldap_user.username,
                        "email": ldap_user.email,
                        "first_name": ldap_user.full_name.split()[0] if ldap_user.full_name else "",
                        "last_name": " ".join(ldap_user.full_name.split()[1:]) if ldap_user.full_name else "",
                        "organization": ldap_user.organization,
                        "department": ldap_user.department,
                        "roles": ["employee"],  # Default role
                        "is_active": ldap_user.account_enabled
                    }
            
            # Fallback to local authentication
            if not user:
                user = await self._authenticate_local_user(login_data.username, login_data.password)
            
            if not user:
                await self.security_service.log_security_event(
                    EventType.LOGIN_FAILURE,
                    user_id=login_data.username,
                    request=request,
                    details={"reason": "invalid_credentials"}
                )
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid username or password"
                )
            
            # Check if MFA is required
            if self.mfa_service.is_mfa_enabled(user["id"]):
                challenge = await self.mfa_service.initiate_mfa_challenge(user["id"])
                
                return LoginResponse(
                    access_token="",
                    refresh_token="",
                    id_token="",
                    expires_in=0,
                    user=user,
                    permissions=[],
                    roles=user.get("roles", []),
                    mfa_required=True,
                    mfa_challenge=challenge
                )
            
            # Create token set
            device_info = login_data.device_info or {}
            device_info.update({
                "ip_address": request.client.host,
                "user_agent": request.headers.get("user-agent", "")
            })
            
            tokens = await self.token_manager.create_token_set(
                user["id"],
                scopes=["read", "write"],
                device_info=device_info
            )
            
            # Get user permissions
            permissions = await self._get_user_permissions(user["id"])
            
            # Log successful login
            await self.security_service.log_security_event(
                EventType.LOGIN_SUCCESS,
                user_id=user["id"],
                request=request,
                details={"login_method": "password"}
            )
            
            return LoginResponse(
                access_token=tokens["access_token"],
                refresh_token=tokens["refresh_token"],
                id_token=tokens["id_token"],
                token_type=tokens["token_type"],
                expires_in=tokens["expires_in"],
                user=user,
                permissions=permissions,
                roles=user.get("roles", []),
                mfa_required=False
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Login error: {e}")
            await self.security_service.log_security_event(
                EventType.LOGIN_FAILURE,
                user_id=login_data.username,
                request=request,
                details={"reason": "system_error", "error": str(e)}
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Login failed due to system error"
            )
    
    async def _authenticate_local_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user against local database."""
        # In production, this would query the actual user database
        user = self.users_db.get(username)
        if user and bcrypt.checkpw(password.encode(), user["password_hash"].encode()):
            return user
        return None
    
    async def _handle_register(self, request: Request, register_data: RegisterRequest):
        """Handle user registration."""
        try:
            # Check if user already exists
            if register_data.username in self.users_db:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already exists"
                )
            
            # Hash password
            password_hash = bcrypt.hashpw(register_data.password.encode(), bcrypt.gensalt()).decode()
            
            # Create user
            user = {
                "id": register_data.username,
                "username": register_data.username,
                "email": register_data.email,
                "first_name": register_data.first_name,
                "last_name": register_data.last_name,
                "organization": register_data.organization,
                "department": register_data.department,
                "password_hash": password_hash,
                "roles": ["employee"],
                "is_active": True,
                "created_at": datetime.now().isoformat()
            }
            
            self.users_db[register_data.username] = user
            
            # Assign default role
            await self.auth_framework.assign_role_to_user(user["id"], "employee")
            
            # Log registration
            await self.security_service.log_security_event(
                EventType.LOGIN_SUCCESS,  # Registration is a form of account creation
                user_id=user["id"],
                request=request,
                details={"action": "registration"}
            )
            
            return {"message": "User registered successfully", "user_id": user["id"]}
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Registration error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Registration failed"
            )
    
    async def _handle_logout(self, request: Request, credentials: HTTPAuthorizationCredentials):
        """Handle user logout."""
        try:
            # Verify token and get user
            token_info = await self.token_manager.verify_token(credentials.credentials)
            if not token_info["valid"]:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            user_id = token_info["user_id"]
            
            # Revoke all user tokens
            await self.token_manager.revoke_user_tokens(user_id, "user_logout")
            
            # Log logout
            await self.security_service.log_security_event(
                EventType.LOGOUT,
                user_id=user_id,
                request=request
            )
            
            return {"message": "Logged out successfully"}
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Logout error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Logout failed"
            )
    
    async def _handle_token_refresh(self, request: Request, refresh_data: TokenRefreshRequest):
        """Handle token refresh."""
        try:
            # Get refresh token from cookie or request body
            refresh_token = None
            if refresh_data and refresh_data.refresh_token:
                refresh_token = refresh_data.refresh_token
            else:
                # Try to get from cookie
                refresh_token = request.cookies.get("refresh_token")
            
            if not refresh_token:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Refresh token required"
                )
            
            # Refresh tokens
            device_info = {
                "ip_address": request.client.host,
                "user_agent": request.headers.get("user-agent", "")
            }
            
            new_tokens = await self.token_manager.refresh_access_token(refresh_token, device_info)
            
            return new_tokens
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Token refresh error: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token refresh failed"
            )
    
    async def _handle_mfa_setup(self, request: Request, mfa_data: MFASetupRequest, 
                              credentials: HTTPAuthorizationCredentials):
        """Handle MFA setup."""
        try:
            # Verify token
            token_info = await self.token_manager.verify_token(credentials.credentials)
            if not token_info["valid"]:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            user_id = token_info["user_id"]
            
            # Setup MFA device
            result = await self.mfa_service.setup_mfa_device(
                user_id,
                MFAMethod(mfa_data.method),
                mfa_data.device_name,
                mfa_data.metadata
            )
            
            # Log MFA setup
            await self.security_service.log_security_event(
                EventType.MFA_SETUP,
                user_id=user_id,
                request=request,
                details={"method": mfa_data.method, "device_name": mfa_data.device_name}
            )
            
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"MFA setup error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="MFA setup failed"
            )
    
    async def _handle_mfa_verify(self, request: Request, verify_data: MFAVerifyRequest):
        """Handle MFA verification."""
        try:
            result = await self.mfa_service.verify_mfa_challenge(
                verify_data.challenge_id,
                verify_data.code
            )
            
            if result["verified"]:
                # Log successful MFA
                await self.security_service.log_security_event(
                    EventType.MFA_SUCCESS,
                    request=request,
                    details={"challenge_id": verify_data.challenge_id}
                )
            else:
                # Log failed MFA
                await self.security_service.log_security_event(
                    EventType.MFA_FAILURE,
                    request=request,
                    details={"challenge_id": verify_data.challenge_id, "attempts_remaining": result.get("attempts_remaining")}
                )
            
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"MFA verification error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="MFA verification failed"
            )
    
    async def _get_user_permissions(self, user_id: str) -> List[str]:
        """Get user permissions."""
        # In production, this would query the authorization framework
        return ["read_conversations", "write_conversations", "read_documents"]
    
    async def _handle_get_current_user(self, request: Request, credentials: HTTPAuthorizationCredentials):
        """Get current user information."""
        try:
            # Verify token
            token_info = await self.token_manager.verify_token(credentials.credentials)
            if not token_info["valid"]:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            user_id = token_info["user_id"]
            user = self.users_db.get(user_id)
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            # Get permissions and roles
            permissions = await self._get_user_permissions(user_id)
            roles = user.get("roles", [])
            
            # Remove sensitive data
            safe_user = {k: v for k, v in user.items() if k != "password_hash"}
            
            return {
                "user": safe_user,
                "permissions": permissions,
                "roles": roles
            }
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Get current user error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get user information"
            )
    
    async def _handle_security_dashboard(self, request: Request, credentials: HTTPAuthorizationCredentials):
        """Get security dashboard data."""
        try:
            # Verify token and check admin permission
            token_info = await self.token_manager.verify_token(credentials.credentials, ["admin"])
            if not token_info["valid"]:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            # Check authorization
            result = await self.auth_framework.authorize(
                token_info["user_id"],
                ActionType.READ,
                resource_id=None,
                request_context={
                    "ip_address": request.client.host,
                    "user_agent": request.headers.get("user-agent", "")
                }
            )
            
            if not result.allowed:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions"
                )
            
            # Get dashboard data
            dashboard_data = await self.security_service.get_security_dashboard()
            
            return dashboard_data
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Security dashboard error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get security dashboard"
            )
    
    def get_app(self) -> FastAPI:
        """Get FastAPI application."""
        return self.app


# Configuration example
AUTH_API_CONFIG = {
    'redis_url': 'redis://localhost:6379',
    'allowed_origins': ['http://localhost:3000', 'https://chabot.company.com'],
    'allowed_hosts': ['localhost', '127.0.0.1', 'chabot.company.com'],
    'mfa': MFA_CONFIG_EXAMPLE,
    'tokens': TOKEN_CONFIG_EXAMPLE,
    'security': SECURITY_CONFIG_EXAMPLE,
    'ldap': LDAP_CONFIG_EXAMPLES['active_directory'],
    'saml': SAML_CONFIG_EXAMPLE
}


# Factory function
def create_auth_api(config: Dict[str, Any] = None) -> FastAPI:
    """Create authentication API with configuration."""
    config = config or AUTH_API_CONFIG
    auth_api = AuthenticationAPI(config)
    return auth_api.get_app()


if __name__ == "__main__":
    import uvicorn
    
    app = create_auth_api()
    uvicorn.run(app, host="0.0.0.0", port=8001)