"""
Enhanced Token Management System for CHaBot.
Provides secure token handling, refresh token rotation, blacklisting, and introspection.
"""

import asyncio
import logging
import secrets
import hashlib
import hmac
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import uuid
import jwt
import aioredis
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from fastapi import HTTPException, Request
import json
import base64


class TokenType(Enum):
    ACCESS = "access"
    REFRESH = "refresh"
    ID = "id"
    MFA = "mfa"
    RESET = "reset"
    VERIFICATION = "verification"


class TokenStatus(Enum):
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"
    BLACKLISTED = "blacklisted"


@dataclass
class TokenMetadata:
    token_id: str
    user_id: str
    token_type: TokenType
    issued_at: datetime
    expires_at: datetime
    last_used: Optional[datetime]
    device_id: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    scopes: List[str]
    audience: str
    issuer: str
    status: TokenStatus
    metadata: Dict[str, Any]


@dataclass
class RefreshTokenFamily:
    family_id: str
    user_id: str
    created_at: datetime
    last_rotated: datetime
    rotation_count: int
    is_compromised: bool
    device_fingerprint: str
    tokens: List[str]


class TokenBlacklist:
    """Token blacklist management."""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
    
    async def blacklist_token(self, token_id: str, expires_at: datetime, reason: str = "revoked"):
        """Add token to blacklist."""
        try:
            # Calculate TTL based on token expiration
            ttl = int((expires_at - datetime.utcnow()).total_seconds())
            if ttl > 0:
                await self.redis_client.setex(
                    f"blacklist:{token_id}",
                    ttl,
                    json.dumps({
                        'reason': reason,
                        'blacklisted_at': datetime.utcnow().isoformat()
                    })
                )
                self.logger.info(f"Token {token_id} blacklisted: {reason}")
        except Exception as e:
            self.logger.error(f"Failed to blacklist token: {e}")
    
    async def is_blacklisted(self, token_id: str) -> bool:
        """Check if token is blacklisted."""
        try:
            result = await self.redis_client.get(f"blacklist:{token_id}")
            return result is not None
        except Exception as e:
            self.logger.error(f"Error checking blacklist: {e}")
            return False
    
    async def get_blacklist_reason(self, token_id: str) -> Optional[str]:
        """Get blacklist reason for token."""
        try:
            result = await self.redis_client.get(f"blacklist:{token_id}")
            if result:
                data = json.loads(result)
                return data.get('reason')
            return None
        except Exception as e:
            self.logger.error(f"Error getting blacklist reason: {e}")
            return None
    
    async def remove_from_blacklist(self, token_id: str):
        """Remove token from blacklist."""
        try:
            await self.redis_client.delete(f"blacklist:{token_id}")
            self.logger.info(f"Token {token_id} removed from blacklist")
        except Exception as e:
            self.logger.error(f"Failed to remove token from blacklist: {e}")


class RefreshTokenManager:
    """Refresh token rotation and family management."""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
        self.token_families: Dict[str, RefreshTokenFamily] = {}
    
    async def create_token_family(self, user_id: str, device_fingerprint: str) -> str:
        """Create new refresh token family."""
        family_id = str(uuid.uuid4())
        
        family = RefreshTokenFamily(
            family_id=family_id,
            user_id=user_id,
            created_at=datetime.utcnow(),
            last_rotated=datetime.utcnow(),
            rotation_count=0,
            is_compromised=False,
            device_fingerprint=device_fingerprint,
            tokens=[]
        )
        
        self.token_families[family_id] = family
        
        # Store in Redis
        await self.redis_client.setex(
            f"token_family:{family_id}",
            86400 * 30,  # 30 days
            json.dumps(asdict(family), default=str)
        )
        
        return family_id
    
    async def add_token_to_family(self, family_id: str, token_id: str):
        """Add token to family."""
        family = await self.get_token_family(family_id)
        if family:
            family.tokens.append(token_id)
            await self._update_family(family)
    
    async def rotate_refresh_token(self, old_token_id: str, family_id: str) -> str:
        """Rotate refresh token within family."""
        family = await self.get_token_family(family_id)
        if not family:
            raise ValueError("Token family not found")
        
        if family.is_compromised:
            raise ValueError("Token family is compromised")
        
        # Check if old token is in family
        if old_token_id not in family.tokens:
            # Potential token reuse - mark family as compromised
            family.is_compromised = True
            await self._update_family(family)
            await self._revoke_family_tokens(family_id)
            raise ValueError("Token reuse detected - family compromised")
        
        # Generate new token ID
        new_token_id = str(uuid.uuid4())
        
        # Update family
        family.tokens = [t for t in family.tokens if t != old_token_id]  # Remove old
        family.tokens.append(new_token_id)  # Add new
        family.last_rotated = datetime.utcnow()
        family.rotation_count += 1
        
        await self._update_family(family)
        
        return new_token_id
    
    async def get_token_family(self, family_id: str) -> Optional[RefreshTokenFamily]:
        """Get token family."""
        try:
            if family_id in self.token_families:
                return self.token_families[family_id]
            
            # Try Redis
            data = await self.redis_client.get(f"token_family:{family_id}")
            if data:
                family_data = json.loads(data)
                family = RefreshTokenFamily(**family_data)
                self.token_families[family_id] = family
                return family
            
            return None
        except Exception as e:
            self.logger.error(f"Error getting token family: {e}")
            return None
    
    async def _update_family(self, family: RefreshTokenFamily):
        """Update token family in storage."""
        self.token_families[family.family_id] = family
        await self.redis_client.setex(
            f"token_family:{family.family_id}",
            86400 * 30,  # 30 days
            json.dumps(asdict(family), default=str)
        )
    
    async def _revoke_family_tokens(self, family_id: str):
        """Revoke all tokens in family."""
        family = await self.get_token_family(family_id)
        if family:
            for token_id in family.tokens:
                # Add to blacklist
                await self.redis_client.setex(
                    f"blacklist:{token_id}",
                    86400,  # 24 hours
                    json.dumps({
                        'reason': 'family_compromised',
                        'blacklisted_at': datetime.utcnow().isoformat()
                    })
                )
    
    async def cleanup_expired_families(self):
        """Clean up expired token families."""
        cutoff_time = datetime.utcnow() - timedelta(days=30)
        
        expired_families = []
        for family_id, family in self.token_families.items():
            if family.last_rotated < cutoff_time:
                expired_families.append(family_id)
        
        for family_id in expired_families:
            del self.token_families[family_id]
            await self.redis_client.delete(f"token_family:{family_id}")
        
        if expired_families:
            self.logger.info(f"Cleaned up {len(expired_families)} expired token families")


class TokenIntrospection:
    """Token introspection service."""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
    
    async def store_token_metadata(self, token_metadata: TokenMetadata):
        """Store token metadata for introspection."""
        try:
            # Calculate TTL based on token expiration
            ttl = int((token_metadata.expires_at - datetime.utcnow()).total_seconds())
            if ttl > 0:
                await self.redis_client.setex(
                    f"token_meta:{token_metadata.token_id}",
                    ttl,
                    json.dumps(asdict(token_metadata), default=str)
                )
        except Exception as e:
            self.logger.error(f"Failed to store token metadata: {e}")
    
    async def get_token_metadata(self, token_id: str) -> Optional[TokenMetadata]:
        """Get token metadata."""
        try:
            data = await self.redis_client.get(f"token_meta:{token_id}")
            if data:
                metadata_dict = json.loads(data)
                # Convert datetime strings back to datetime objects
                metadata_dict['issued_at'] = datetime.fromisoformat(metadata_dict['issued_at'])
                metadata_dict['expires_at'] = datetime.fromisoformat(metadata_dict['expires_at'])
                if metadata_dict['last_used']:
                    metadata_dict['last_used'] = datetime.fromisoformat(metadata_dict['last_used'])
                metadata_dict['token_type'] = TokenType(metadata_dict['token_type'])
                metadata_dict['status'] = TokenStatus(metadata_dict['status'])
                
                return TokenMetadata(**metadata_dict)
            return None
        except Exception as e:
            self.logger.error(f"Error getting token metadata: {e}")
            return None
    
    async def update_token_usage(self, token_id: str):
        """Update token last used timestamp."""
        try:
            metadata = await self.get_token_metadata(token_id)
            if metadata:
                metadata.last_used = datetime.utcnow()
                await self.store_token_metadata(metadata)
        except Exception as e:
            self.logger.error(f"Error updating token usage: {e}")
    
    async def introspect_token(self, token: str) -> Dict[str, Any]:
        """Introspect token and return detailed information."""
        try:
            # Decode token without verification to get token ID
            unverified_payload = jwt.decode(token, options={"verify_signature": False})
            token_id = unverified_payload.get('jti')
            
            if not token_id:
                return {'active': False, 'error': 'Invalid token format'}
            
            # Get metadata
            metadata = await self.get_token_metadata(token_id)
            if not metadata:
                return {'active': False, 'error': 'Token not found'}
            
            # Check if token is blacklisted
            blacklisted = await self.redis_client.get(f"blacklist:{token_id}")
            if blacklisted:
                return {
                    'active': False,
                    'error': 'Token revoked',
                    'reason': json.loads(blacklisted).get('reason', 'unknown')
                }
            
            # Check expiration
            if datetime.utcnow() > metadata.expires_at:
                return {'active': False, 'error': 'Token expired'}
            
            # Return introspection data
            return {
                'active': True,
                'token_id': metadata.token_id,
                'user_id': metadata.user_id,
                'token_type': metadata.token_type.value,
                'issued_at': metadata.issued_at.isoformat(),
                'expires_at': metadata.expires_at.isoformat(),
                'last_used': metadata.last_used.isoformat() if metadata.last_used else None,
                'scopes': metadata.scopes,
                'audience': metadata.audience,
                'issuer': metadata.issuer,
                'device_id': metadata.device_id,
                'ip_address': metadata.ip_address,
                'user_agent': metadata.user_agent
            }
            
        except Exception as e:
            self.logger.error(f"Token introspection error: {e}")
            return {'active': False, 'error': 'Introspection failed'}


class EnhancedTokenManager:
    """Enhanced token management system."""
    
    def __init__(self, config: Dict[str, Any], redis_url: str = "redis://localhost:6379"):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # JWT configuration
        self.jwt_secret = config.get('jwt_secret', 'default-secret')
        self.jwt_algorithm = config.get('jwt_algorithm', 'HS256')
        self.access_token_ttl = config.get('access_token_ttl', 900)  # 15 minutes
        self.refresh_token_ttl = config.get('refresh_token_ttl', 86400 * 30)  # 30 days
        self.id_token_ttl = config.get('id_token_ttl', 3600)  # 1 hour
        
        # Initialize Redis
        self.redis_client = None
        asyncio.create_task(self._initialize_redis(redis_url))
        
        # Initialize components
        self.blacklist = None
        self.refresh_manager = None
        self.introspection = None
        
        # Token storage
        self.active_tokens: Dict[str, TokenMetadata] = {}
    
    async def _initialize_redis(self, redis_url: str):
        """Initialize Redis connection and components."""
        try:
            self.redis_client = aioredis.from_url(redis_url)
            
            # Initialize components
            self.blacklist = TokenBlacklist(self.redis_client)
            self.refresh_manager = RefreshTokenManager(self.redis_client)
            self.introspection = TokenIntrospection(self.redis_client)
            
            self.logger.info("Enhanced token manager initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize token manager: {e}")
    
    async def create_token_set(self, user_id: str, scopes: List[str], 
                             device_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create complete token set (access, refresh, ID tokens)."""
        try:
            device_info = device_info or {}
            device_fingerprint = self._generate_device_fingerprint(device_info)
            
            # Create refresh token family
            family_id = await self.refresh_manager.create_token_family(user_id, device_fingerprint)
            
            # Generate token IDs
            access_token_id = str(uuid.uuid4())
            refresh_token_id = str(uuid.uuid4())
            id_token_id = str(uuid.uuid4())
            
            # Add refresh token to family
            await self.refresh_manager.add_token_to_family(family_id, refresh_token_id)
            
            # Create tokens
            now = datetime.utcnow()
            
            # Access token
            access_token = await self._create_token(
                token_id=access_token_id,
                user_id=user_id,
                token_type=TokenType.ACCESS,
                expires_at=now + timedelta(seconds=self.access_token_ttl),
                scopes=scopes,
                device_info=device_info,
                extra_claims={'family_id': family_id}
            )
            
            # Refresh token
            refresh_token = await self._create_token(
                token_id=refresh_token_id,
                user_id=user_id,
                token_type=TokenType.REFRESH,
                expires_at=now + timedelta(seconds=self.refresh_token_ttl),
                scopes=['refresh'],
                device_info=device_info,
                extra_claims={'family_id': family_id}
            )
            
            # ID token
            id_token = await self._create_token(
                token_id=id_token_id,
                user_id=user_id,
                token_type=TokenType.ID,
                expires_at=now + timedelta(seconds=self.id_token_ttl),
                scopes=['openid'],
                device_info=device_info,
                extra_claims={'family_id': family_id}
            )
            
            return {
                'access_token': access_token['token'],
                'refresh_token': refresh_token['token'],
                'id_token': id_token['token'],
                'token_type': 'Bearer',
                'expires_in': self.access_token_ttl,
                'scope': ' '.join(scopes)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to create token set: {e}")
            raise HTTPException(status_code=500, detail="Token creation failed")
    
    async def _create_token(self, token_id: str, user_id: str, token_type: TokenType,
                          expires_at: datetime, scopes: List[str], device_info: Dict[str, Any],
                          extra_claims: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create individual token."""
        now = datetime.utcnow()
        
        # Create JWT payload
        payload = {
            'jti': token_id,
            'sub': user_id,
            'iat': int(now.timestamp()),
            'exp': int(expires_at.timestamp()),
            'iss': self.config.get('issuer', 'chabot'),
            'aud': self.config.get('audience', 'chabot-api'),
            'token_type': token_type.value,
            'scope': ' '.join(scopes)
        }
        
        # Add extra claims
        if extra_claims:
            payload.update(extra_claims)
        
        # Create JWT token
        token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
        
        # Create metadata
        metadata = TokenMetadata(
            token_id=token_id,
            user_id=user_id,
            token_type=token_type,
            issued_at=now,
            expires_at=expires_at,
            last_used=None,
            device_id=device_info.get('device_id'),
            ip_address=device_info.get('ip_address'),
            user_agent=device_info.get('user_agent'),
            scopes=scopes,
            audience=payload['aud'],
            issuer=payload['iss'],
            status=TokenStatus.ACTIVE,
            metadata=device_info
        )
        
        # Store metadata
        await self.introspection.store_token_metadata(metadata)
        self.active_tokens[token_id] = metadata
        
        return {
            'token': token,
            'metadata': metadata
        }
    
    async def refresh_access_token(self, refresh_token: str, device_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """Refresh access token using refresh token."""
        try:
            # Verify refresh token
            payload = jwt.decode(refresh_token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            refresh_token_id = payload.get('jti')
            user_id = payload.get('sub')
            family_id = payload.get('family_id')
            
            if not all([refresh_token_id, user_id, family_id]):
                raise ValueError("Invalid refresh token")
            
            # Check if token is blacklisted
            if await self.blacklist.is_blacklisted(refresh_token_id):
                raise ValueError("Refresh token revoked")
            
            # Rotate refresh token
            new_refresh_token_id = await self.refresh_manager.rotate_refresh_token(
                refresh_token_id, family_id
            )
            
            # Blacklist old refresh token
            await self.blacklist.blacklist_token(
                refresh_token_id,
                datetime.fromtimestamp(payload['exp']),
                "rotated"
            )
            
            # Get user scopes (in production, fetch from database)
            scopes = ['read', 'write']  # Default scopes
            
            # Create new token set
            device_info = device_info or {}
            now = datetime.utcnow()
            
            # New access token
            access_token_id = str(uuid.uuid4())
            access_token = await self._create_token(
                token_id=access_token_id,
                user_id=user_id,
                token_type=TokenType.ACCESS,
                expires_at=now + timedelta(seconds=self.access_token_ttl),
                scopes=scopes,
                device_info=device_info,
                extra_claims={'family_id': family_id}
            )
            
            # New refresh token
            new_refresh_token = await self._create_token(
                token_id=new_refresh_token_id,
                user_id=user_id,
                token_type=TokenType.REFRESH,
                expires_at=now + timedelta(seconds=self.refresh_token_ttl),
                scopes=['refresh'],
                device_info=device_info,
                extra_claims={'family_id': family_id}
            )
            
            return {
                'access_token': access_token['token'],
                'refresh_token': new_refresh_token['token'],
                'token_type': 'Bearer',
                'expires_in': self.access_token_ttl,
                'scope': ' '.join(scopes)
            }
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Refresh token expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid refresh token")
        except Exception as e:
            self.logger.error(f"Token refresh error: {e}")
            raise HTTPException(status_code=401, detail=str(e))
    
    async def verify_token(self, token: str, required_scopes: List[str] = None) -> Dict[str, Any]:
        """Verify and validate token."""
        try:
            # Decode and verify token
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            token_id = payload.get('jti')
            user_id = payload.get('sub')
            
            if not token_id:
                raise ValueError("Invalid token format")
            
            # Check blacklist
            if await self.blacklist.is_blacklisted(token_id):
                reason = await self.blacklist.get_blacklist_reason(token_id)
                raise ValueError(f"Token revoked: {reason}")
            
            # Check scopes if required
            if required_scopes:
                token_scopes = payload.get('scope', '').split()
                if not all(scope in token_scopes for scope in required_scopes):
                    raise ValueError("Insufficient scope")
            
            # Update token usage
            await self.introspection.update_token_usage(token_id)
            
            return {
                'valid': True,
                'user_id': user_id,
                'token_id': token_id,
                'scopes': payload.get('scope', '').split(),
                'expires_at': datetime.fromtimestamp(payload['exp']),
                'token_type': payload.get('token_type', 'access')
            }
            
        except jwt.ExpiredSignatureError:
            return {'valid': False, 'error': 'Token expired'}
        except jwt.InvalidTokenError:
            return {'valid': False, 'error': 'Invalid token'}
        except Exception as e:
            return {'valid': False, 'error': str(e)}
    
    async def revoke_token(self, token: str, reason: str = "user_revoked") -> bool:
        """Revoke token."""
        try:
            # Decode token to get ID and expiration
            payload = jwt.decode(token, options={"verify_signature": False})
            token_id = payload.get('jti')
            exp = payload.get('exp')
            
            if token_id and exp:
                expires_at = datetime.fromtimestamp(exp)
                await self.blacklist.blacklist_token(token_id, expires_at, reason)
                
                # If it's a refresh token, mark family as compromised
                if payload.get('token_type') == 'refresh':
                    family_id = payload.get('family_id')
                    if family_id:
                        family = await self.refresh_manager.get_token_family(family_id)
                        if family:
                            family.is_compromised = True
                            await self.refresh_manager._update_family(family)
                            await self.refresh_manager._revoke_family_tokens(family_id)
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Token revocation error: {e}")
            return False
    
    async def revoke_user_tokens(self, user_id: str, reason: str = "user_logout") -> int:
        """Revoke all tokens for a user."""
        revoked_count = 0
        
        try:
            # Find all active tokens for user
            for token_id, metadata in self.active_tokens.items():
                if metadata.user_id == user_id and metadata.status == TokenStatus.ACTIVE:
                    await self.blacklist.blacklist_token(token_id, metadata.expires_at, reason)
                    metadata.status = TokenStatus.REVOKED
                    revoked_count += 1
            
            # Revoke refresh token families
            for family_id, family in self.refresh_manager.token_families.items():
                if family.user_id == user_id:
                    family.is_compromised = True
                    await self.refresh_manager._update_family(family)
                    await self.refresh_manager._revoke_family_tokens(family_id)
            
            self.logger.info(f"Revoked {revoked_count} tokens for user {user_id}")
            return revoked_count
            
        except Exception as e:
            self.logger.error(f"Error revoking user tokens: {e}")
            return 0
    
    def _generate_device_fingerprint(self, device_info: Dict[str, Any]) -> str:
        """Generate device fingerprint."""
        fingerprint_data = {
            'user_agent': device_info.get('user_agent', ''),
            'ip_address': device_info.get('ip_address', ''),
            'device_id': device_info.get('device_id', '')
        }
        
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
        return hashlib.sha256(fingerprint_str.encode()).hexdigest()
    
    async def get_token_info(self, token: str) -> Dict[str, Any]:
        """Get detailed token information."""
        return await self.introspection.introspect_token(token)
    
    async def cleanup_expired_tokens(self):
        """Clean up expired tokens and families."""
        try:
            # Clean up expired token families
            await self.refresh_manager.cleanup_expired_families()
            
            # Clean up expired token metadata from memory
            now = datetime.utcnow()
            expired_tokens = []
            
            for token_id, metadata in self.active_tokens.items():
                if now > metadata.expires_at:
                    expired_tokens.append(token_id)
            
            for token_id in expired_tokens:
                del self.active_tokens[token_id]
            
            if expired_tokens:
                self.logger.info(f"Cleaned up {len(expired_tokens)} expired tokens")
                
        except Exception as e:
            self.logger.error(f"Token cleanup error: {e}")
    
    def get_token_stats(self) -> Dict[str, Any]:
        """Get token management statistics."""
        active_count = len([t for t in self.active_tokens.values() if t.status == TokenStatus.ACTIVE])
        
        return {
            'total_tokens': len(self.active_tokens),
            'active_tokens': active_count,
            'token_families': len(self.refresh_manager.token_families),
            'compromised_families': len([f for f in self.refresh_manager.token_families.values() if f.is_compromised])
        }


# Configuration example
TOKEN_CONFIG_EXAMPLE = {
    'jwt_secret': 'your-super-secret-jwt-key',
    'jwt_algorithm': 'HS256',
    'access_token_ttl': 900,  # 15 minutes
    'refresh_token_ttl': 86400 * 30,  # 30 days
    'id_token_ttl': 3600,  # 1 hour
    'issuer': 'chabot-auth-service',
    'audience': 'chabot-api'
}


if __name__ == "__main__":
    # Example usage
    token_manager = EnhancedTokenManager(TOKEN_CONFIG_EXAMPLE)
    
    # Create token set
    # tokens = await token_manager.create_token_set(
    #     "user123", 
    #     ["read", "write"], 
    #     {"device_id": "device123", "ip_address": "***********"}
    # )
    
    # Verify token
    # result = await token_manager.verify_token(tokens['access_token'], ["read"])
    
    # Refresh token
    # new_tokens = await token_manager.refresh_access_token(tokens['refresh_token'])
    
    # Revoke token
    # await token_manager.revoke_token(tokens['access_token'], "user_logout")