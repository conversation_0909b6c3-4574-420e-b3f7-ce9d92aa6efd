"""
Security Monitoring and Compliance System for CHaBot.
Provides audit logging, threat detection, compliance reporting, and security alerts.
"""

import asyncio
import logging
import json
import hashlib
import hmac
from typing import Dict, List, Any, Optional, Set, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import uuid
import re
from collections import defaultdict, deque
import aioredis
import aiofiles
from fastapi import Request
import geoip2.database
import user_agents
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart


class EventType(Enum):
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    MFA_SETUP = "mfa_setup"
    MFA_SUCCESS = "mfa_success"
    MFA_FAILURE = "mfa_failure"
    PERMISSION_GRANTED = "permission_granted"
    PERMISSION_DENIED = "permission_denied"
    RESOURCE_ACCESS = "resource_access"
    ADMIN_ACTION = "admin_action"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    SECURITY_VIOLATION = "security_violation"
    DATA_EXPORT = "data_export"
    CONFIGURATION_CHANGE = "configuration_change"


class ThreatLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ComplianceStandard(Enum):
    GDPR = "gdpr"
    HIPAA = "hipaa"
    SOX = "sox"
    PCI_DSS = "pci_dss"
    ISO27001 = "iso27001"
    NIST = "nist"


@dataclass
class SecurityEvent:
    id: str
    event_type: EventType
    user_id: Optional[str]
    session_id: Optional[str]
    ip_address: str
    user_agent: str
    timestamp: datetime
    details: Dict[str, Any]
    risk_score: float
    threat_level: ThreatLevel
    location: Dict[str, Any]
    device_fingerprint: str
    metadata: Dict[str, Any]


@dataclass
class ThreatIndicator:
    id: str
    name: str
    description: str
    pattern: str
    threat_level: ThreatLevel
    detection_rules: List[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    is_active: bool


@dataclass
class ComplianceRule:
    id: str
    standard: ComplianceStandard
    rule_name: str
    description: str
    requirements: List[str]
    validation_logic: Dict[str, Any]
    remediation_steps: List[str]
    is_mandatory: bool


@dataclass
class SecurityAlert:
    id: str
    alert_type: str
    severity: ThreatLevel
    title: str
    description: str
    affected_users: List[str]
    indicators: List[str]
    timestamp: datetime
    status: str
    assigned_to: Optional[str]
    resolution_notes: Optional[str]
    metadata: Dict[str, Any]


class ThreatDetectionEngine:
    """Advanced threat detection engine."""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
        
        # Threat indicators
        self.threat_indicators: Dict[str, ThreatIndicator] = {}
        
        # Detection rules
        self.detection_rules = []
        
        # User behavior baselines
        self.user_baselines: Dict[str, Dict[str, Any]] = {}
        
        # Initialize threat indicators
        asyncio.create_task(self._initialize_threat_indicators())
    
    async def _initialize_threat_indicators(self):
        """Initialize default threat indicators."""
        default_indicators = [
            ThreatIndicator(
                id="brute_force_login",
                name="Brute Force Login Attack",
                description="Multiple failed login attempts from same IP",
                pattern="failed_login_attempts",
                threat_level=ThreatLevel.HIGH,
                detection_rules=[
                    {
                        "type": "frequency",
                        "event_type": "login_failure",
                        "threshold": 5,
                        "time_window": 300,  # 5 minutes
                        "group_by": "ip_address"
                    }
                ],
                created_at=datetime.now(),
                updated_at=datetime.now(),
                is_active=True
            ),
            ThreatIndicator(
                id="impossible_travel",
                name="Impossible Travel",
                description="User login from geographically impossible location",
                pattern="location_anomaly",
                threat_level=ThreatLevel.CRITICAL,
                detection_rules=[
                    {
                        "type": "geolocation",
                        "max_speed_kmh": 1000,  # Max realistic travel speed
                        "time_threshold": 3600  # 1 hour
                    }
                ],
                created_at=datetime.now(),
                updated_at=datetime.now(),
                is_active=True
            ),
            ThreatIndicator(
                id="privilege_escalation",
                name="Privilege Escalation Attempt",
                description="User attempting to access resources above their privilege level",
                pattern="permission_violation",
                threat_level=ThreatLevel.HIGH,
                detection_rules=[
                    {
                        "type": "permission_anomaly",
                        "threshold": 3,
                        "time_window": 600  # 10 minutes
                    }
                ],
                created_at=datetime.now(),
                updated_at=datetime.now(),
                is_active=True
            ),
            ThreatIndicator(
                id="data_exfiltration",
                name="Data Exfiltration Attempt",
                description="Unusual data access or export patterns",
                pattern="data_access_anomaly",
                threat_level=ThreatLevel.CRITICAL,
                detection_rules=[
                    {
                        "type": "volume_anomaly",
                        "threshold_multiplier": 5,  # 5x normal volume
                        "baseline_period": 86400  # 24 hours
                    }
                ],
                created_at=datetime.now(),
                updated_at=datetime.now(),
                is_active=True
            ),
            ThreatIndicator(
                id="account_takeover",
                name="Account Takeover",
                description="Signs of compromised user account",
                pattern="behavioral_anomaly",
                threat_level=ThreatLevel.CRITICAL,
                detection_rules=[
                    {
                        "type": "behavioral_change",
                        "deviation_threshold": 0.8,
                        "factors": ["login_time", "device_type", "location", "access_patterns"]
                    }
                ],
                created_at=datetime.now(),
                updated_at=datetime.now(),
                is_active=True
            )
        ]
        
        for indicator in default_indicators:
            self.threat_indicators[indicator.id] = indicator
    
    async def analyze_event(self, event: SecurityEvent) -> List[ThreatIndicator]:
        """Analyze security event for threats."""
        detected_threats = []
        
        for indicator in self.threat_indicators.values():
            if not indicator.is_active:
                continue
            
            if await self._check_threat_indicator(event, indicator):
                detected_threats.append(indicator)
        
        return detected_threats
    
    async def _check_threat_indicator(self, event: SecurityEvent, indicator: ThreatIndicator) -> bool:
        """Check if event matches threat indicator."""
        for rule in indicator.detection_rules:
            if await self._evaluate_detection_rule(event, rule):
                return True
        return False
    
    async def _evaluate_detection_rule(self, event: SecurityEvent, rule: Dict[str, Any]) -> bool:
        """Evaluate detection rule against event."""
        rule_type = rule.get("type")
        
        if rule_type == "frequency":
            return await self._check_frequency_rule(event, rule)
        elif rule_type == "geolocation":
            return await self._check_geolocation_rule(event, rule)
        elif rule_type == "permission_anomaly":
            return await self._check_permission_anomaly(event, rule)
        elif rule_type == "volume_anomaly":
            return await self._check_volume_anomaly(event, rule)
        elif rule_type == "behavioral_change":
            return await self._check_behavioral_change(event, rule)
        
        return False
    
    async def _check_frequency_rule(self, event: SecurityEvent, rule: Dict[str, Any]) -> bool:
        """Check frequency-based detection rule."""
        event_type = rule.get("event_type")
        threshold = rule.get("threshold")
        time_window = rule.get("time_window")
        group_by = rule.get("group_by")
        
        if event.event_type.value != event_type:
            return False
        
        # Get grouping key
        if group_by == "ip_address":
            group_key = event.ip_address
        elif group_by == "user_id":
            group_key = event.user_id
        else:
            group_key = "global"
        
        # Count events in time window
        redis_key = f"threat_detection:{event_type}:{group_key}"
        
        # Add current event
        await self.redis_client.zadd(redis_key, {event.id: event.timestamp.timestamp()})
        
        # Remove old events
        cutoff_time = event.timestamp.timestamp() - time_window
        await self.redis_client.zremrangebyscore(redis_key, 0, cutoff_time)
        
        # Count events in window
        count = await self.redis_client.zcard(redis_key)
        
        # Set expiration
        await self.redis_client.expire(redis_key, time_window)
        
        return count >= threshold
    
    async def _check_geolocation_rule(self, event: SecurityEvent, rule: Dict[str, Any]) -> bool:
        """Check geolocation-based detection rule."""
        if not event.user_id:
            return False
        
        max_speed = rule.get("max_speed_kmh")
        time_threshold = rule.get("time_threshold")
        
        # Get user's last location
        last_location_key = f"user_location:{event.user_id}"
        last_location_data = await self.redis_client.get(last_location_key)
        
        if not last_location_data:
            # Store current location
            await self.redis_client.setex(
                last_location_key,
                86400,  # 24 hours
                json.dumps({
                    "latitude": event.location.get("latitude"),
                    "longitude": event.location.get("longitude"),
                    "timestamp": event.timestamp.timestamp()
                })
            )
            return False
        
        last_location = json.loads(last_location_data)
        
        # Calculate distance and time difference
        distance_km = self._calculate_distance(
            last_location["latitude"], last_location["longitude"],
            event.location.get("latitude"), event.location.get("longitude")
        )
        
        time_diff = event.timestamp.timestamp() - last_location["timestamp"]
        
        if time_diff < time_threshold:
            return False
        
        # Calculate required speed
        required_speed = distance_km / (time_diff / 3600)  # km/h
        
        # Update location
        await self.redis_client.setex(
            last_location_key,
            86400,
            json.dumps({
                "latitude": event.location.get("latitude"),
                "longitude": event.location.get("longitude"),
                "timestamp": event.timestamp.timestamp()
            })
        )
        
        return required_speed > max_speed
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two coordinates using Haversine formula."""
        import math
        
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Earth's radius in kilometers
        r = 6371
        
        return c * r
    
    async def _check_permission_anomaly(self, event: SecurityEvent, rule: Dict[str, Any]) -> bool:
        """Check for permission anomalies."""
        if event.event_type != EventType.PERMISSION_DENIED:
            return False
        
        threshold = rule.get("threshold")
        time_window = rule.get("time_window")
        
        if not event.user_id:
            return False
        
        # Count permission denials for user
        redis_key = f"permission_denials:{event.user_id}"
        
        await self.redis_client.zadd(redis_key, {event.id: event.timestamp.timestamp()})
        
        # Remove old events
        cutoff_time = event.timestamp.timestamp() - time_window
        await self.redis_client.zremrangebyscore(redis_key, 0, cutoff_time)
        
        count = await self.redis_client.zcard(redis_key)
        await self.redis_client.expire(redis_key, time_window)
        
        return count >= threshold
    
    async def _check_volume_anomaly(self, event: SecurityEvent, rule: Dict[str, Any]) -> bool:
        """Check for data volume anomalies."""
        if event.event_type != EventType.DATA_EXPORT:
            return False
        
        threshold_multiplier = rule.get("threshold_multiplier")
        baseline_period = rule.get("baseline_period")
        
        if not event.user_id:
            return False
        
        # Get baseline data volume for user
        baseline_key = f"data_baseline:{event.user_id}"
        baseline_data = await self.redis_client.get(baseline_key)
        
        current_volume = event.details.get("data_size", 0)
        
        if not baseline_data:
            # Initialize baseline
            await self.redis_client.setex(
                baseline_key,
                baseline_period,
                json.dumps({"total_volume": current_volume, "event_count": 1})
            )
            return False
        
        baseline = json.loads(baseline_data)
        avg_volume = baseline["total_volume"] / baseline["event_count"]
        
        # Update baseline
        baseline["total_volume"] += current_volume
        baseline["event_count"] += 1
        
        await self.redis_client.setex(
            baseline_key,
            baseline_period,
            json.dumps(baseline)
        )
        
        return current_volume > (avg_volume * threshold_multiplier)
    
    async def _check_behavioral_change(self, event: SecurityEvent, rule: Dict[str, Any]) -> bool:
        """Check for behavioral changes."""
        if not event.user_id:
            return False
        
        deviation_threshold = rule.get("deviation_threshold")
        factors = rule.get("factors", [])
        
        # Get user baseline
        baseline_key = f"user_baseline:{event.user_id}"
        baseline_data = await self.redis_client.get(baseline_key)
        
        if not baseline_data:
            # Initialize baseline
            baseline = self._extract_behavioral_features(event)
            await self.redis_client.setex(
                baseline_key,
                86400 * 7,  # 7 days
                json.dumps(baseline)
            )
            return False
        
        baseline = json.loads(baseline_data)
        current_features = self._extract_behavioral_features(event)
        
        # Calculate deviation
        deviation_score = self._calculate_behavioral_deviation(baseline, current_features, factors)
        
        # Update baseline (exponential moving average)
        alpha = 0.1  # Learning rate
        for factor in factors:
            if factor in baseline and factor in current_features:
                baseline[factor] = alpha * current_features[factor] + (1 - alpha) * baseline[factor]
        
        await self.redis_client.setex(
            baseline_key,
            86400 * 7,
            json.dumps(baseline)
        )
        
        return deviation_score > deviation_threshold
    
    def _extract_behavioral_features(self, event: SecurityEvent) -> Dict[str, Any]:
        """Extract behavioral features from event."""
        features = {}
        
        # Login time (hour of day)
        features["login_time"] = event.timestamp.hour
        
        # Device type
        if event.user_agent:
            ua = user_agents.parse(event.user_agent)
            features["device_type"] = ua.device.family
            features["browser"] = ua.browser.family
            features["os"] = ua.os.family
        
        # Location
        if event.location:
            features["country"] = event.location.get("country")
            features["city"] = event.location.get("city")
        
        # Access patterns
        features["event_type"] = event.event_type.value
        
        return features
    
    def _calculate_behavioral_deviation(self, baseline: Dict[str, Any], 
                                      current: Dict[str, Any], factors: List[str]) -> float:
        """Calculate behavioral deviation score."""
        total_deviation = 0
        factor_count = 0
        
        for factor in factors:
            if factor in baseline and factor in current:
                if isinstance(baseline[factor], (int, float)):
                    # Numerical feature
                    if baseline[factor] != 0:
                        deviation = abs(current[factor] - baseline[factor]) / abs(baseline[factor])
                    else:
                        deviation = 1 if current[factor] != 0 else 0
                else:
                    # Categorical feature
                    deviation = 0 if baseline[factor] == current[factor] else 1
                
                total_deviation += deviation
                factor_count += 1
        
        return total_deviation / factor_count if factor_count > 0 else 0


class AuditLogger:
    """Comprehensive audit logging system."""
    
    def __init__(self, redis_client, log_file_path: str = "/var/log/chabot/audit.log"):
        self.redis_client = redis_client
        self.log_file_path = log_file_path
        self.logger = logging.getLogger(__name__)
        
        # Event buffer for batch processing
        self.event_buffer = deque(maxlen=1000)
        
        # Start background log processor
        asyncio.create_task(self._process_audit_logs())
    
    async def log_event(self, event: SecurityEvent):
        """Log security event."""
        try:
            # Add to buffer
            self.event_buffer.append(event)
            
            # Store in Redis for real-time access
            await self.redis_client.zadd(
                "audit_events",
                {event.id: event.timestamp.timestamp()}
            )
            
            await self.redis_client.setex(
                f"audit_event:{event.id}",
                86400 * 30,  # 30 days
                json.dumps(asdict(event), default=str)
            )
            
            # Keep only recent events in sorted set
            cutoff_time = (datetime.now() - timedelta(days=30)).timestamp()
            await self.redis_client.zremrangebyscore("audit_events", 0, cutoff_time)
            
        except Exception as e:
            self.logger.error(f"Failed to log audit event: {e}")
    
    async def _process_audit_logs(self):
        """Process audit logs in background."""
        while True:
            try:
                if self.event_buffer:
                    # Process batch of events
                    events_to_process = []
                    while self.event_buffer and len(events_to_process) < 100:
                        events_to_process.append(self.event_buffer.popleft())
                    
                    await self._write_events_to_file(events_to_process)
                
                await asyncio.sleep(10)  # Process every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Audit log processing error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _write_events_to_file(self, events: List[SecurityEvent]):
        """Write events to audit log file."""
        try:
            async with aiofiles.open(self.log_file_path, 'a') as f:
                for event in events:
                    log_entry = {
                        "timestamp": event.timestamp.isoformat(),
                        "event_id": event.id,
                        "event_type": event.event_type.value,
                        "user_id": event.user_id,
                        "session_id": event.session_id,
                        "ip_address": event.ip_address,
                        "user_agent": event.user_agent,
                        "risk_score": event.risk_score,
                        "threat_level": event.threat_level.value,
                        "location": event.location,
                        "details": event.details,
                        "metadata": event.metadata
                    }
                    
                    await f.write(json.dumps(log_entry) + '\n')
                    
        except Exception as e:
            self.logger.error(f"Failed to write audit log: {e}")
    
    async def search_events(self, filters: Dict[str, Any], limit: int = 100) -> List[SecurityEvent]:
        """Search audit events."""
        try:
            # Get event IDs from time range
            start_time = filters.get('start_time', datetime.now() - timedelta(days=1))
            end_time = filters.get('end_time', datetime.now())
            
            event_ids = await self.redis_client.zrangebyscore(
                "audit_events",
                start_time.timestamp(),
                end_time.timestamp(),
                start=0,
                num=limit
            )
            
            events = []
            for event_id in event_ids:
                event_data = await self.redis_client.get(f"audit_event:{event_id}")
                if event_data:
                    event_dict = json.loads(event_data)
                    
                    # Apply filters
                    if self._matches_filters(event_dict, filters):
                        # Convert back to SecurityEvent
                        event_dict['timestamp'] = datetime.fromisoformat(event_dict['timestamp'])
                        event_dict['event_type'] = EventType(event_dict['event_type'])
                        event_dict['threat_level'] = ThreatLevel(event_dict['threat_level'])
                        
                        events.append(SecurityEvent(**event_dict))
            
            return events
            
        except Exception as e:
            self.logger.error(f"Event search error: {e}")
            return []
    
    def _matches_filters(self, event_dict: Dict[str, Any], filters: Dict[str, Any]) -> bool:
        """Check if event matches search filters."""
        # User ID filter
        if 'user_id' in filters and event_dict.get('user_id') != filters['user_id']:
            return False
        
        # Event type filter
        if 'event_type' in filters and event_dict.get('event_type') != filters['event_type']:
            return False
        
        # IP address filter
        if 'ip_address' in filters and event_dict.get('ip_address') != filters['ip_address']:
            return False
        
        # Threat level filter
        if 'threat_level' in filters and event_dict.get('threat_level') != filters['threat_level']:
            return False
        
        # Risk score filter
        if 'min_risk_score' in filters and event_dict.get('risk_score', 0) < filters['min_risk_score']:
            return False
        
        return True


class ComplianceManager:
    """Compliance monitoring and reporting."""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
        
        # Compliance rules
        self.compliance_rules: Dict[str, ComplianceRule] = {}
        
        # Initialize compliance rules
        asyncio.create_task(self._initialize_compliance_rules())
    
    async def _initialize_compliance_rules(self):
        """Initialize compliance rules."""
        default_rules = [
            ComplianceRule(
                id="gdpr_data_access_log",
                standard=ComplianceStandard.GDPR,
                rule_name="Data Access Logging",
                description="All personal data access must be logged",
                requirements=["Log all data access", "Include user identification", "Retain logs for 3 years"],
                validation_logic={"event_types": ["resource_access"], "data_types": ["personal_data"]},
                remediation_steps=["Enable comprehensive logging", "Configure log retention"],
                is_mandatory=True
            ),
            ComplianceRule(
                id="hipaa_audit_trail",
                standard=ComplianceStandard.HIPAA,
                rule_name="Audit Trail Requirements",
                description="Maintain audit trail for all PHI access",
                requirements=["Log PHI access", "Include user authentication", "Secure log storage"],
                validation_logic={"event_types": ["resource_access"], "data_types": ["phi"]},
                remediation_steps=["Implement audit logging", "Secure log storage", "Regular log review"],
                is_mandatory=True
            ),
            ComplianceRule(
                id="sox_access_control",
                standard=ComplianceStandard.SOX,
                rule_name="Access Control Documentation",
                description="Document and monitor access to financial systems",
                requirements=["Role-based access", "Regular access review", "Segregation of duties"],
                validation_logic={"event_types": ["permission_granted", "admin_action"]},
                remediation_steps=["Implement RBAC", "Conduct access reviews", "Document procedures"],
                is_mandatory=True
            )
        ]
        
        for rule in default_rules:
            self.compliance_rules[rule.id] = rule
    
    async def check_compliance(self, events: List[SecurityEvent]) -> Dict[str, Any]:
        """Check compliance against events."""
        compliance_results = {}
        
        for rule_id, rule in self.compliance_rules.items():
            result = await self._check_compliance_rule(rule, events)
            compliance_results[rule_id] = result
        
        return compliance_results
    
    async def _check_compliance_rule(self, rule: ComplianceRule, events: List[SecurityEvent]) -> Dict[str, Any]:
        """Check specific compliance rule."""
        validation_logic = rule.validation_logic
        required_event_types = validation_logic.get("event_types", [])
        required_data_types = validation_logic.get("data_types", [])
        
        compliant_events = []
        non_compliant_events = []
        
        for event in events:
            if event.event_type.value in required_event_types:
                # Check if event meets compliance requirements
                if self._event_meets_requirements(event, rule):
                    compliant_events.append(event.id)
                else:
                    non_compliant_events.append(event.id)
        
        compliance_score = len(compliant_events) / (len(compliant_events) + len(non_compliant_events)) if (compliant_events or non_compliant_events) else 1.0
        
        return {
            "rule_id": rule.id,
            "standard": rule.standard.value,
            "compliance_score": compliance_score,
            "compliant_events": len(compliant_events),
            "non_compliant_events": len(non_compliant_events),
            "is_compliant": compliance_score >= 0.95,  # 95% threshold
            "remediation_needed": compliance_score < 0.95,
            "remediation_steps": rule.remediation_steps if compliance_score < 0.95 else []
        }
    
    def _event_meets_requirements(self, event: SecurityEvent, rule: ComplianceRule) -> bool:
        """Check if event meets compliance rule requirements."""
        # Basic compliance checks
        
        # Must have user identification
        if not event.user_id:
            return False
        
        # Must have timestamp
        if not event.timestamp:
            return False
        
        # Must have sufficient detail
        if not event.details:
            return False
        
        # Rule-specific checks
        if rule.standard == ComplianceStandard.GDPR:
            # GDPR requires explicit consent tracking for data processing
            if event.event_type == EventType.RESOURCE_ACCESS:
                return event.details.get("consent_recorded", False)
        
        elif rule.standard == ComplianceStandard.HIPAA:
            # HIPAA requires minimum necessary principle
            if event.event_type == EventType.RESOURCE_ACCESS:
                return event.details.get("business_justification") is not None
        
        return True
    
    async def generate_compliance_report(self, standard: ComplianceStandard, 
                                       start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate compliance report for specific standard."""
        # Get relevant events
        filters = {
            'start_time': start_date,
            'end_time': end_date
        }
        
        # This would typically fetch from audit logger
        events = []  # Placeholder
        
        # Get relevant rules for standard
        relevant_rules = [rule for rule in self.compliance_rules.values() if rule.standard == standard]
        
        compliance_results = {}
        for rule in relevant_rules:
            result = await self._check_compliance_rule(rule, events)
            compliance_results[rule.id] = result
        
        # Calculate overall compliance score
        scores = [result["compliance_score"] for result in compliance_results.values()]
        overall_score = sum(scores) / len(scores) if scores else 0
        
        return {
            "standard": standard.value,
            "report_period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "overall_compliance_score": overall_score,
            "is_compliant": overall_score >= 0.95,
            "rule_results": compliance_results,
            "recommendations": self._generate_compliance_recommendations(compliance_results),
            "generated_at": datetime.now().isoformat()
        }
    
    def _generate_compliance_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate compliance recommendations."""
        recommendations = []
        
        for rule_id, result in results.items():
            if result["remediation_needed"]:
                rule = self.compliance_rules[rule_id]
                recommendations.extend(result["remediation_steps"])
        
        return list(set(recommendations))  # Remove duplicates


class SecurityMonitoringService:
    """Main security monitoring service."""
    
    def __init__(self, config: Dict[str, Any], redis_url: str = "redis://localhost:6379"):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Initialize Redis
        self.redis_client = None
        asyncio.create_task(self._initialize_redis(redis_url))
        
        # Initialize components
        self.threat_detection = None
        self.audit_logger = None
        self.compliance_manager = None
        
        # Alert management
        self.active_alerts: Dict[str, SecurityAlert] = {}
        
        # GeoIP database for location detection
        self.geoip_db = None
        self._initialize_geoip()
    
    async def _initialize_redis(self, redis_url: str):
        """Initialize Redis and components."""
        try:
            self.redis_client = aioredis.from_url(redis_url)
            
            # Initialize components
            self.threat_detection = ThreatDetectionEngine(self.redis_client)
            self.audit_logger = AuditLogger(self.redis_client, self.config.get('audit_log_path', '/var/log/chabot/audit.log'))
            self.compliance_manager = ComplianceManager(self.redis_client)
            
            self.logger.info("Security monitoring service initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize security monitoring: {e}")
    
    def _initialize_geoip(self):
        """Initialize GeoIP database."""
        try:
            geoip_path = self.config.get('geoip_db_path', '/usr/share/GeoIP/GeoLite2-City.mmdb')
            self.geoip_db = geoip2.database.Reader(geoip_path)
        except Exception as e:
            self.logger.warning(f"GeoIP database not available: {e}")
    
    async def log_security_event(self, event_type: EventType, user_id: str = None,
                                session_id: str = None, request: Request = None,
                                details: Dict[str, Any] = None) -> SecurityEvent:
        """Log security event and analyze for threats."""
        try:
            # Extract request information
            ip_address = request.client.host if request else "unknown"
            user_agent = request.headers.get("user-agent", "") if request else ""
            
            # Get location information
            location = self._get_location_info(ip_address)
            
            # Generate device fingerprint
            device_fingerprint = self._generate_device_fingerprint(user_agent, ip_address)
            
            # Calculate risk score
            risk_score = self._calculate_risk_score(event_type, user_id, ip_address, details or {})
            
            # Determine threat level
            threat_level = self._determine_threat_level(risk_score)
            
            # Create security event
            event = SecurityEvent(
                id=str(uuid.uuid4()),
                event_type=event_type,
                user_id=user_id,
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                timestamp=datetime.now(),
                details=details or {},
                risk_score=risk_score,
                threat_level=threat_level,
                location=location,
                device_fingerprint=device_fingerprint,
                metadata={}
            )
            
            # Log event
            await self.audit_logger.log_event(event)
            
            # Analyze for threats
            detected_threats = await self.threat_detection.analyze_event(event)
            
            # Generate alerts if threats detected
            if detected_threats:
                await self._generate_security_alerts(event, detected_threats)
            
            return event
            
        except Exception as e:
            self.logger.error(f"Failed to log security event: {e}")
            raise
    
    def _get_location_info(self, ip_address: str) -> Dict[str, Any]:
        """Get location information from IP address."""
        if not self.geoip_db or ip_address in ["127.0.0.1", "localhost", "unknown"]:
            return {"country": "unknown", "city": "unknown", "latitude": 0, "longitude": 0}
        
        try:
            response = self.geoip_db.city(ip_address)
            return {
                "country": response.country.name,
                "city": response.city.name,
                "latitude": float(response.location.latitude or 0),
                "longitude": float(response.location.longitude or 0)
            }
        except Exception:
            return {"country": "unknown", "city": "unknown", "latitude": 0, "longitude": 0}
    
    def _generate_device_fingerprint(self, user_agent: str, ip_address: str) -> str:
        """Generate device fingerprint."""
        fingerprint_data = f"{user_agent}:{ip_address}"
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()
    
    def _calculate_risk_score(self, event_type: EventType, user_id: str, 
                            ip_address: str, details: Dict[str, Any]) -> float:
        """Calculate risk score for event."""
        base_scores = {
            EventType.LOGIN_SUCCESS: 0.1,
            EventType.LOGIN_FAILURE: 0.3,
            EventType.LOGOUT: 0.0,
            EventType.PASSWORD_CHANGE: 0.2,
            EventType.MFA_SETUP: 0.1,
            EventType.MFA_SUCCESS: 0.1,
            EventType.MFA_FAILURE: 0.4,
            EventType.PERMISSION_GRANTED: 0.2,
            EventType.PERMISSION_DENIED: 0.5,
            EventType.RESOURCE_ACCESS: 0.2,
            EventType.ADMIN_ACTION: 0.3,
            EventType.SUSPICIOUS_ACTIVITY: 0.8,
            EventType.SECURITY_VIOLATION: 0.9,
            EventType.DATA_EXPORT: 0.4,
            EventType.CONFIGURATION_CHANGE: 0.5
        }
        
        risk_score = base_scores.get(event_type, 0.5)
        
        # Adjust based on factors
        if details.get("failed_attempts", 0) > 3:
            risk_score += 0.3
        
        if details.get("admin_action"):
            risk_score += 0.2
        
        if details.get("sensitive_data"):
            risk_score += 0.3
        
        return min(risk_score, 1.0)
    
    def _determine_threat_level(self, risk_score: float) -> ThreatLevel:
        """Determine threat level from risk score."""
        if risk_score >= 0.8:
            return ThreatLevel.CRITICAL
        elif risk_score >= 0.6:
            return ThreatLevel.HIGH
        elif risk_score >= 0.3:
            return ThreatLevel.MEDIUM
        else:
            return ThreatLevel.LOW
    
    async def _generate_security_alerts(self, event: SecurityEvent, threats: List[ThreatIndicator]):
        """Generate security alerts for detected threats."""
        for threat in threats:
            alert = SecurityAlert(
                id=str(uuid.uuid4()),
                alert_type=threat.name,
                severity=threat.threat_level,
                title=f"Security Alert: {threat.name}",
                description=f"{threat.description}. Event: {event.event_type.value}",
                affected_users=[event.user_id] if event.user_id else [],
                indicators=[threat.id],
                timestamp=datetime.now(),
                status="open",
                assigned_to=None,
                resolution_notes=None,
                metadata={
                    "event_id": event.id,
                    "threat_indicator": threat.id,
                    "risk_score": event.risk_score
                }
            )
            
            self.active_alerts[alert.id] = alert
            
            # Store in Redis
            await self.redis_client.setex(
                f"security_alert:{alert.id}",
                86400 * 7,  # 7 days
                json.dumps(asdict(alert), default=str)
            )
            
            # Send notification if critical
            if alert.severity == ThreatLevel.CRITICAL:
                await self._send_security_notification(alert)
    
    async def _send_security_notification(self, alert: SecurityAlert):
        """Send security notification for critical alerts."""
        try:
            notification_config = self.config.get('notifications', {})
            
            if notification_config.get('email'):
                await self._send_email_notification(alert, notification_config['email'])
            
            # Could also send to Slack, PagerDuty, etc.
            
        except Exception as e:
            self.logger.error(f"Failed to send security notification: {e}")
    
    async def _send_email_notification(self, alert: SecurityAlert, email_config: Dict[str, Any]):
        """Send email notification for security alert."""
        try:
            smtp_server = email_config.get('smtp_server')
            smtp_port = email_config.get('smtp_port', 587)
            username = email_config.get('username')
            password = email_config.get('password')
            to_addresses = email_config.get('security_team', [])
            
            if not all([smtp_server, username, password, to_addresses]):
                return
            
            msg = MIMEMultipart()
            msg['From'] = username
            msg['To'] = ', '.join(to_addresses)
            msg['Subject'] = f"SECURITY ALERT: {alert.title}"
            
            body = f"""
            Security Alert Details:
            
            Alert Type: {alert.alert_type}
            Severity: {alert.severity.value.upper()}
            Description: {alert.description}
            Timestamp: {alert.timestamp.isoformat()}
            Affected Users: {', '.join(alert.affected_users)}
            
            Please investigate immediately.
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(username, password)
                server.send_message(msg)
            
            self.logger.info(f"Security notification sent for alert {alert.id}")
            
        except Exception as e:
            self.logger.error(f"Failed to send email notification: {e}")
    
    async def get_security_dashboard(self) -> Dict[str, Any]:
        """Get security monitoring dashboard data."""
        try:
            # Get recent events
            recent_events = await self.audit_logger.search_events({
                'start_time': datetime.now() - timedelta(hours=24)
            }, limit=1000)
            
            # Calculate metrics
            total_events = len(recent_events)
            high_risk_events = len([e for e in recent_events if e.risk_score >= 0.6])
            failed_logins = len([e for e in recent_events if e.event_type == EventType.LOGIN_FAILURE])
            
            # Get active alerts
            active_alerts = len([a for a in self.active_alerts.values() if a.status == "open"])
            critical_alerts = len([a for a in self.active_alerts.values() 
                                 if a.status == "open" and a.severity == ThreatLevel.CRITICAL])
            
            # Event type distribution
            event_types = defaultdict(int)
            for event in recent_events:
                event_types[event.event_type.value] += 1
            
            # Threat level distribution
            threat_levels = defaultdict(int)
            for event in recent_events:
                threat_levels[event.threat_level.value] += 1
            
            return {
                "summary": {
                    "total_events_24h": total_events,
                    "high_risk_events": high_risk_events,
                    "failed_logins": failed_logins,
                    "active_alerts": active_alerts,
                    "critical_alerts": critical_alerts
                },
                "event_distribution": dict(event_types),
                "threat_distribution": dict(threat_levels),
                "recent_alerts": [
                    asdict(alert) for alert in list(self.active_alerts.values())[-10:]
                ],
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to generate security dashboard: {e}")
            return {}
    
    async def generate_compliance_report(self, standard: ComplianceStandard) -> Dict[str, Any]:
        """Generate compliance report."""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)  # Last 30 days
        
        return await self.compliance_manager.generate_compliance_report(
            standard, start_date, end_date
        )


# Configuration example
SECURITY_CONFIG_EXAMPLE = {
    'audit_log_path': '/var/log/chabot/audit.log',
    'geoip_db_path': '/usr/share/GeoIP/GeoLite2-City.mmdb',
    'notifications': {
        'email': {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': '<EMAIL>',
            'password': 'app-password',
            'security_team': ['<EMAIL>', '<EMAIL>']
        }
    }
}


if __name__ == "__main__":
    # Example usage
    security_service = SecurityMonitoringService(SECURITY_CONFIG_EXAMPLE)
    
    # Log security event
    # event = await security_service.log_security_event(
    #     EventType.LOGIN_FAILURE,
    #     user_id="user123",
    #     details={"failed_attempts": 5}
    # )
    
    # Get dashboard
    # dashboard = await security_service.get_security_dashboard()
    
    # Generate compliance report
    # report = await security_service.generate_compliance_report(ComplianceStandard.GDPR)