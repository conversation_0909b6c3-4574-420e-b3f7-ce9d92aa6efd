"""
Multi-Factor Authentication (MFA) Service for CHaBot.
Supports TOTP, SMS, Email, and Hardware Token authentication.
"""

import asyncio
import logging
import secrets
import qrcode
import io
import base64
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import pyotp
import aioredis
from twilio.rest import Client as TwilioClient
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from cryptography.fernet import Fernet
import hashlib
import hmac
from fastapi import HTTPException
import jwt


class MFAMethod(Enum):
    TOTP = "totp"
    SMS = "sms"
    EMAIL = "email"
    BACKUP_CODES = "backup_codes"
    HARDWARE_TOKEN = "hardware_token"
    PUSH_NOTIFICATION = "push_notification"


class MFAStatus(Enum):
    DISABLED = "disabled"
    ENABLED = "enabled"
    PENDING_SETUP = "pending_setup"
    TEMPORARILY_DISABLED = "temporarily_disabled"


@dataclass
class MFADevice:
    id: str
    user_id: str
    method: MFAMethod
    name: str
    secret: str
    backup_codes: List[str]
    is_primary: bool
    is_verified: bool
    created_at: datetime
    last_used: Optional[datetime]
    metadata: Dict[str, Any]


@dataclass
class MFAChallenge:
    challenge_id: str
    user_id: str
    method: MFAMethod
    code: str
    expires_at: datetime
    attempts: int
    max_attempts: int
    metadata: Dict[str, Any]


class TOTPService:
    """Time-based One-Time Password service."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_secret(self) -> str:
        """Generate TOTP secret."""
        return pyotp.random_base32()
    
    def generate_qr_code(self, secret: str, user_email: str, issuer: str = "CHaBot") -> str:
        """Generate QR code for TOTP setup."""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=issuer
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
    
    def verify_code(self, secret: str, code: str, window: int = 1) -> bool:
        """Verify TOTP code."""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(code, valid_window=window)
        except Exception as e:
            self.logger.error(f"TOTP verification error: {e}")
            return False
    
    def get_current_code(self, secret: str) -> str:
        """Get current TOTP code (for testing)."""
        totp = pyotp.TOTP(secret)
        return totp.now()


class SMSService:
    """SMS-based MFA service."""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Initialize Twilio client
        if config.get('twilio_account_sid') and config.get('twilio_auth_token'):
            self.twilio_client = TwilioClient(
                config['twilio_account_sid'],
                config['twilio_auth_token']
            )
            self.from_number = config.get('twilio_from_number')
        else:
            self.twilio_client = None
    
    def generate_code(self, length: int = 6) -> str:
        """Generate SMS verification code."""
        return ''.join([str(secrets.randbelow(10)) for _ in range(length)])
    
    async def send_code(self, phone_number: str, code: str) -> bool:
        """Send SMS verification code."""
        if not self.twilio_client:
            self.logger.error("SMS service not configured")
            return False
        
        try:
            message = self.twilio_client.messages.create(
                body=f"Your CHaBot verification code is: {code}",
                from_=self.from_number,
                to=phone_number
            )
            
            self.logger.info(f"SMS sent successfully: {message.sid}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send SMS: {e}")
            return False


class EmailMFAService:
    """Email-based MFA service."""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        self.smtp_server = config.get('smtp_server')
        self.smtp_port = config.get('smtp_port', 587)
        self.smtp_username = config.get('smtp_username')
        self.smtp_password = config.get('smtp_password')
        self.from_email = config.get('from_email')
    
    def generate_code(self, length: int = 8) -> str:
        """Generate email verification code."""
        return ''.join([str(secrets.randbelow(10)) for _ in range(length)])
    
    async def send_code(self, email: str, code: str) -> bool:
        """Send email verification code."""
        if not all([self.smtp_server, self.smtp_username, self.smtp_password]):
            self.logger.error("Email service not configured")
            return False
        
        try:
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = email
            msg['Subject'] = "CHaBot Verification Code"
            
            body = f"""
            <html>
            <body>
                <h2>CHaBot Verification Code</h2>
                <p>Your verification code is: <strong>{code}</strong></p>
                <p>This code will expire in 5 minutes.</p>
                <p>If you didn't request this code, please ignore this email.</p>
            </body>
            </html>
            """
            
            msg.attach(MIMEText(body, 'html'))
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            self.logger.info(f"Email verification code sent to {email}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send email: {e}")
            return False


class BackupCodesService:
    """Backup codes service for MFA recovery."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_backup_codes(self, count: int = 10) -> List[str]:
        """Generate backup codes."""
        codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = ''.join(secrets.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for _ in range(8))
            codes.append(code)
        return codes
    
    def hash_backup_codes(self, codes: List[str]) -> List[str]:
        """Hash backup codes for storage."""
        return [hashlib.sha256(code.encode()).hexdigest() for code in codes]
    
    def verify_backup_code(self, code: str, hashed_codes: List[str]) -> bool:
        """Verify backup code against hashed codes."""
        code_hash = hashlib.sha256(code.upper().encode()).hexdigest()
        return code_hash in hashed_codes


class MFAService:
    """Main Multi-Factor Authentication service."""
    
    def __init__(self, config: Dict[str, Any], redis_url: str = "redis://localhost:6379"):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Initialize services
        self.totp_service = TOTPService()
        self.sms_service = SMSService(config.get('sms', {}))
        self.email_service = EmailMFAService(config.get('email', {}))
        self.backup_codes_service = BackupCodesService()
        
        # Redis for challenge storage
        self.redis_client = None
        asyncio.create_task(self._initialize_redis(redis_url))
        
        # Encryption for sensitive data
        self.cipher_suite = Fernet(Fernet.generate_key())
        
        # MFA devices storage (in production, use database)
        self.mfa_devices: Dict[str, List[MFADevice]] = {}
        
        # Active challenges
        self.active_challenges: Dict[str, MFAChallenge] = {}
    
    async def _initialize_redis(self, redis_url: str):
        """Initialize Redis connection."""
        try:
            self.redis_client = aioredis.from_url(redis_url)
            self.logger.info("Redis connection initialized for MFA service")
        except Exception as e:
            self.logger.error(f"Failed to initialize Redis: {e}")
    
    async def setup_mfa_device(self, user_id: str, method: MFAMethod, 
                             device_name: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Setup new MFA device for user."""
        try:
            device_id = secrets.token_hex(16)
            
            if method == MFAMethod.TOTP:
                return await self._setup_totp_device(user_id, device_id, device_name, metadata)
            elif method == MFAMethod.SMS:
                return await self._setup_sms_device(user_id, device_id, device_name, metadata)
            elif method == MFAMethod.EMAIL:
                return await self._setup_email_device(user_id, device_id, device_name, metadata)
            elif method == MFAMethod.BACKUP_CODES:
                return await self._setup_backup_codes(user_id, device_id, device_name)
            else:
                raise ValueError(f"Unsupported MFA method: {method}")
                
        except Exception as e:
            self.logger.error(f"Failed to setup MFA device: {e}")
            raise HTTPException(status_code=500, detail="Failed to setup MFA device")
    
    async def _setup_totp_device(self, user_id: str, device_id: str, 
                               device_name: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Setup TOTP device."""
        secret = self.totp_service.generate_secret()
        user_email = metadata.get('email', f'user_{user_id}@chabot.com')
        
        device = MFADevice(
            id=device_id,
            user_id=user_id,
            method=MFAMethod.TOTP,
            name=device_name,
            secret=self.cipher_suite.encrypt(secret.encode()).decode(),
            backup_codes=[],
            is_primary=len(self.mfa_devices.get(user_id, [])) == 0,
            is_verified=False,
            created_at=datetime.now(),
            last_used=None,
            metadata=metadata
        )
        
        # Store device (pending verification)
        if user_id not in self.mfa_devices:
            self.mfa_devices[user_id] = []
        self.mfa_devices[user_id].append(device)
        
        # Generate QR code
        qr_code = self.totp_service.generate_qr_code(secret, user_email)
        
        return {
            'device_id': device_id,
            'secret': secret,
            'qr_code': qr_code,
            'backup_codes': [],
            'setup_complete': False
        }
    
    async def _setup_sms_device(self, user_id: str, device_id: str, 
                              device_name: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Setup SMS device."""
        phone_number = metadata.get('phone_number')
        if not phone_number:
            raise ValueError("Phone number required for SMS MFA")
        
        device = MFADevice(
            id=device_id,
            user_id=user_id,
            method=MFAMethod.SMS,
            name=device_name,
            secret=phone_number,  # Store phone number as secret
            backup_codes=[],
            is_primary=len(self.mfa_devices.get(user_id, [])) == 0,
            is_verified=False,
            created_at=datetime.now(),
            last_used=None,
            metadata=metadata
        )
        
        if user_id not in self.mfa_devices:
            self.mfa_devices[user_id] = []
        self.mfa_devices[user_id].append(device)
        
        return {
            'device_id': device_id,
            'phone_number': phone_number,
            'setup_complete': False
        }
    
    async def _setup_email_device(self, user_id: str, device_id: str, 
                                device_name: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Setup email device."""
        email = metadata.get('email')
        if not email:
            raise ValueError("Email required for email MFA")
        
        device = MFADevice(
            id=device_id,
            user_id=user_id,
            method=MFAMethod.EMAIL,
            name=device_name,
            secret=email,  # Store email as secret
            backup_codes=[],
            is_primary=len(self.mfa_devices.get(user_id, [])) == 0,
            is_verified=False,
            created_at=datetime.now(),
            last_used=None,
            metadata=metadata
        )
        
        if user_id not in self.mfa_devices:
            self.mfa_devices[user_id] = []
        self.mfa_devices[user_id].append(device)
        
        return {
            'device_id': device_id,
            'email': email,
            'setup_complete': False
        }
    
    async def _setup_backup_codes(self, user_id: str, device_id: str, device_name: str) -> Dict[str, Any]:
        """Setup backup codes."""
        backup_codes = self.backup_codes_service.generate_backup_codes()
        hashed_codes = self.backup_codes_service.hash_backup_codes(backup_codes)
        
        device = MFADevice(
            id=device_id,
            user_id=user_id,
            method=MFAMethod.BACKUP_CODES,
            name=device_name,
            secret='',
            backup_codes=hashed_codes,
            is_primary=False,  # Backup codes are never primary
            is_verified=True,  # Backup codes are immediately verified
            created_at=datetime.now(),
            last_used=None,
            metadata={}
        )
        
        if user_id not in self.mfa_devices:
            self.mfa_devices[user_id] = []
        self.mfa_devices[user_id].append(device)
        
        return {
            'device_id': device_id,
            'backup_codes': backup_codes,
            'setup_complete': True
        }
    
    async def verify_mfa_setup(self, user_id: str, device_id: str, verification_code: str) -> bool:
        """Verify MFA device setup."""
        try:
            device = self._get_device(user_id, device_id)
            if not device:
                return False
            
            if device.method == MFAMethod.TOTP:
                secret = self.cipher_suite.decrypt(device.secret.encode()).decode()
                verified = self.totp_service.verify_code(secret, verification_code)
            elif device.method == MFAMethod.SMS:
                # For SMS, we need to send a code first and then verify
                verified = await self._verify_sms_code(device, verification_code)
            elif device.method == MFAMethod.EMAIL:
                # For email, we need to send a code first and then verify
                verified = await self._verify_email_code(device, verification_code)
            else:
                verified = False
            
            if verified:
                device.is_verified = True
                self.logger.info(f"MFA device {device_id} verified for user {user_id}")
            
            return verified
            
        except Exception as e:
            self.logger.error(f"MFA verification error: {e}")
            return False
    
    async def initiate_mfa_challenge(self, user_id: str, device_id: str = None) -> Dict[str, Any]:
        """Initiate MFA challenge."""
        try:
            # Get device (primary if not specified)
            if device_id:
                device = self._get_device(user_id, device_id)
            else:
                device = self._get_primary_device(user_id)
            
            if not device or not device.is_verified:
                raise ValueError("No verified MFA device found")
            
            challenge_id = secrets.token_hex(16)
            
            if device.method == MFAMethod.TOTP:
                return await self._initiate_totp_challenge(challenge_id, device)
            elif device.method == MFAMethod.SMS:
                return await self._initiate_sms_challenge(challenge_id, device)
            elif device.method == MFAMethod.EMAIL:
                return await self._initiate_email_challenge(challenge_id, device)
            else:
                raise ValueError(f"Unsupported MFA method: {device.method}")
                
        except Exception as e:
            self.logger.error(f"Failed to initiate MFA challenge: {e}")
            raise HTTPException(status_code=500, detail="Failed to initiate MFA challenge")
    
    async def _initiate_totp_challenge(self, challenge_id: str, device: MFADevice) -> Dict[str, Any]:
        """Initiate TOTP challenge."""
        challenge = MFAChallenge(
            challenge_id=challenge_id,
            user_id=device.user_id,
            method=device.method,
            code='',  # TOTP doesn't need server-side code
            expires_at=datetime.now() + timedelta(minutes=5),
            attempts=0,
            max_attempts=3,
            metadata={'device_id': device.id}
        )
        
        self.active_challenges[challenge_id] = challenge
        
        return {
            'challenge_id': challenge_id,
            'method': device.method.value,
            'message': 'Enter the code from your authenticator app',
            'expires_in': 300
        }
    
    async def _initiate_sms_challenge(self, challenge_id: str, device: MFADevice) -> Dict[str, Any]:
        """Initiate SMS challenge."""
        code = self.sms_service.generate_code()
        
        challenge = MFAChallenge(
            challenge_id=challenge_id,
            user_id=device.user_id,
            method=device.method,
            code=code,
            expires_at=datetime.now() + timedelta(minutes=5),
            attempts=0,
            max_attempts=3,
            metadata={'device_id': device.id}
        )
        
        self.active_challenges[challenge_id] = challenge
        
        # Send SMS
        success = await self.sms_service.send_code(device.secret, code)
        
        if not success:
            raise ValueError("Failed to send SMS code")
        
        return {
            'challenge_id': challenge_id,
            'method': device.method.value,
            'message': f'Code sent to {device.secret[-4:]}',
            'expires_in': 300
        }
    
    async def _initiate_email_challenge(self, challenge_id: str, device: MFADevice) -> Dict[str, Any]:
        """Initiate email challenge."""
        code = self.email_service.generate_code()
        
        challenge = MFAChallenge(
            challenge_id=challenge_id,
            user_id=device.user_id,
            method=device.method,
            code=code,
            expires_at=datetime.now() + timedelta(minutes=5),
            attempts=0,
            max_attempts=3,
            metadata={'device_id': device.id}
        )
        
        self.active_challenges[challenge_id] = challenge
        
        # Send email
        success = await self.email_service.send_code(device.secret, code)
        
        if not success:
            raise ValueError("Failed to send email code")
        
        return {
            'challenge_id': challenge_id,
            'method': device.method.value,
            'message': f'Code sent to {device.secret}',
            'expires_in': 300
        }
    
    async def verify_mfa_challenge(self, challenge_id: str, code: str) -> Dict[str, Any]:
        """Verify MFA challenge."""
        try:
            challenge = self.active_challenges.get(challenge_id)
            if not challenge:
                raise ValueError("Invalid or expired challenge")
            
            if datetime.now() > challenge.expires_at:
                del self.active_challenges[challenge_id]
                raise ValueError("Challenge expired")
            
            if challenge.attempts >= challenge.max_attempts:
                del self.active_challenges[challenge_id]
                raise ValueError("Maximum attempts exceeded")
            
            challenge.attempts += 1
            
            # Verify code based on method
            verified = False
            
            if challenge.method == MFAMethod.TOTP:
                device = self._get_device(challenge.user_id, challenge.metadata['device_id'])
                if device:
                    secret = self.cipher_suite.decrypt(device.secret.encode()).decode()
                    verified = self.totp_service.verify_code(secret, code)
            
            elif challenge.method in [MFAMethod.SMS, MFAMethod.EMAIL]:
                verified = challenge.code == code
            
            elif challenge.method == MFAMethod.BACKUP_CODES:
                device = self._get_device(challenge.user_id, challenge.metadata['device_id'])
                if device:
                    verified = self.backup_codes_service.verify_backup_code(code, device.backup_codes)
                    if verified:
                        # Remove used backup code
                        code_hash = hashlib.sha256(code.upper().encode()).hexdigest()
                        device.backup_codes.remove(code_hash)
            
            if verified:
                # Update device last used
                device = self._get_device(challenge.user_id, challenge.metadata['device_id'])
                if device:
                    device.last_used = datetime.now()
                
                # Clean up challenge
                del self.active_challenges[challenge_id]
                
                # Generate MFA token
                mfa_token = self._generate_mfa_token(challenge.user_id)
                
                return {
                    'verified': True,
                    'mfa_token': mfa_token,
                    'expires_in': 3600  # 1 hour
                }
            else:
                return {
                    'verified': False,
                    'attempts_remaining': challenge.max_attempts - challenge.attempts
                }
                
        except Exception as e:
            self.logger.error(f"MFA challenge verification error: {e}")
            raise HTTPException(status_code=400, detail=str(e))
    
    def _generate_mfa_token(self, user_id: str) -> str:
        """Generate MFA verification token."""
        payload = {
            'user_id': user_id,
            'mfa_verified': True,
            'exp': datetime.utcnow() + timedelta(hours=1),
            'iat': datetime.utcnow()
        }
        
        return jwt.encode(payload, self.config.get('jwt_secret', 'secret'), algorithm='HS256')
    
    def verify_mfa_token(self, token: str) -> Optional[str]:
        """Verify MFA token."""
        try:
            payload = jwt.decode(token, self.config.get('jwt_secret', 'secret'), algorithms=['HS256'])
            if payload.get('mfa_verified'):
                return payload.get('user_id')
            return None
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    def _get_device(self, user_id: str, device_id: str) -> Optional[MFADevice]:
        """Get MFA device by ID."""
        user_devices = self.mfa_devices.get(user_id, [])
        for device in user_devices:
            if device.id == device_id:
                return device
        return None
    
    def _get_primary_device(self, user_id: str) -> Optional[MFADevice]:
        """Get primary MFA device for user."""
        user_devices = self.mfa_devices.get(user_id, [])
        for device in user_devices:
            if device.is_primary and device.is_verified:
                return device
        return None
    
    def get_user_devices(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's MFA devices."""
        user_devices = self.mfa_devices.get(user_id, [])
        return [
            {
                'id': device.id,
                'name': device.name,
                'method': device.method.value,
                'is_primary': device.is_primary,
                'is_verified': device.is_verified,
                'created_at': device.created_at.isoformat(),
                'last_used': device.last_used.isoformat() if device.last_used else None
            }
            for device in user_devices
        ]
    
    async def disable_mfa_device(self, user_id: str, device_id: str) -> bool:
        """Disable MFA device."""
        try:
            user_devices = self.mfa_devices.get(user_id, [])
            self.mfa_devices[user_id] = [d for d in user_devices if d.id != device_id]
            return True
        except Exception as e:
            self.logger.error(f"Failed to disable MFA device: {e}")
            return False
    
    def is_mfa_enabled(self, user_id: str) -> bool:
        """Check if user has MFA enabled."""
        user_devices = self.mfa_devices.get(user_id, [])
        return any(device.is_verified for device in user_devices)
    
    def get_mfa_status(self, user_id: str) -> Dict[str, Any]:
        """Get user's MFA status."""
        user_devices = self.mfa_devices.get(user_id, [])
        verified_devices = [d for d in user_devices if d.is_verified]
        
        return {
            'enabled': len(verified_devices) > 0,
            'device_count': len(verified_devices),
            'methods': list(set(d.method.value for d in verified_devices)),
            'primary_method': verified_devices[0].method.value if verified_devices else None
        }


# Configuration example
MFA_CONFIG_EXAMPLE = {
    'jwt_secret': 'your-jwt-secret-key',
    'sms': {
        'twilio_account_sid': 'your-twilio-account-sid',
        'twilio_auth_token': 'your-twilio-auth-token',
        'twilio_from_number': '+**********'
    },
    'email': {
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'smtp_username': '<EMAIL>',
        'smtp_password': 'your-app-password',
        'from_email': '<EMAIL>'
    }
}


if __name__ == "__main__":
    # Example usage
    mfa_service = MFAService(MFA_CONFIG_EXAMPLE)
    
    # Setup TOTP device
    # setup_result = await mfa_service.setup_mfa_device(
    #     "user123", 
    #     MFAMethod.TOTP, 
    #     "My Authenticator", 
    #     {"email": "<EMAIL>"}
    # )
    
    # Verify setup
    # verified = await mfa_service.verify_mfa_setup("user123", setup_result['device_id'], "123456")
    
    # Initiate challenge
    # challenge = await mfa_service.initiate_mfa_challenge("user123")
    
    # Verify challenge
    # result = await mfa_service.verify_mfa_challenge(challenge['challenge_id'], "123456")