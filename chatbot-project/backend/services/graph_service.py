from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from ai.knowledge.graph_search.neo4j_client import Neo4jClient
from ai.knowledge.graph_search.indexing.graph_indexer import GraphIndexer
from ai.knowledge.graph_search.queries.graph_queries import GraphQueries

class GraphSearchRequest(BaseModel):
    query_type: str
    parameters: Dict

class OrganizationSearchRequest(BaseModel):
    organization_name: str

class PersonSearchRequest(BaseModel):
    person_name: str

class PolicySearchRequest(BaseModel):
    policy_name: str

class HierarchyRequest(BaseModel):
    root_organization: Optional[str] = None

class GraphService:
    def __init__(self):
        self.client = None
        self.indexer = None
        self.queries = None
        self.initialized = False
    
    def initialize(self, uri: str = "bolt://localhost:7687", user: str = "neo4j", password: str = "password"):
        """Initialize Neo4j connection and services"""
        try:
            self.client = Neo4jClient(uri, user, password)
            self.indexer = GraphIndexer(self.client)
            self.queries = GraphQueries(self.client)
            self.initialized = True
            return {"status": "initialized", "message": "Graph database connected successfully"}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to initialize graph database: {str(e)}")
    
    def index_all_data(self):
        """Index all organizational data into graph database"""
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Graph service not initialized")
        
        try:
            return self.indexer.index_all()
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Indexing failed: {str(e)}")
    
    def search_organization(self, request: OrganizationSearchRequest):
        """Search for organization details"""
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Graph service not initialized")
        
        try:
            return self.queries.find_organization_details(request.organization_name)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Organization search failed: {str(e)}")
    
    def search_person(self, request: PersonSearchRequest):
        """Search for person profile"""
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Graph service not initialized")
        
        try:
            return self.queries.find_person_profile(request.person_name)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Person search failed: {str(e)}")
    
    def search_policy(self, request: PolicySearchRequest):
        """Search for policy implementation"""
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Graph service not initialized")
        
        try:
            return self.queries.find_policy_implementation(request.policy_name)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Policy search failed: {str(e)}")
    
    def get_hierarchy(self, request: HierarchyRequest):
        """Get organizational hierarchy"""
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Graph service not initialized")
        
        try:
            return self.queries.find_organizational_hierarchy(request.root_organization)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Hierarchy search failed: {str(e)}")
    
    def get_statistics(self):
        """Get graph database statistics"""
        if not self.initialized:
            return {"status": "not_initialized", "message": "Graph service not initialized"}
        
        try:
            return self.client.get_graph_statistics()
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Statistics retrieval failed: {str(e)}")

app = FastAPI(title="Graph Database Service")
graph_service = GraphService()

@app.post("/initialize")
async def initialize_graph_service():
    try:
        return graph_service.initialize()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/index")
async def index_all_data():
    try:
        return graph_service.index_all_data()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search/organization")
async def search_organization(request: OrganizationSearchRequest):
    try:
        return graph_service.search_organization(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search/person")
async def search_person(request: PersonSearchRequest):
    try:
        return graph_service.search_person(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search/policy")
async def search_policy(request: PolicySearchRequest):
    try:
        return graph_service.search_policy(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/hierarchy")
async def get_hierarchy(request: HierarchyRequest):
    try:
        return graph_service.get_hierarchy(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/statistics")
async def get_statistics():
    try:
        return graph_service.get_statistics()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "graph_service",
        "initialized": graph_service.initialized
    }