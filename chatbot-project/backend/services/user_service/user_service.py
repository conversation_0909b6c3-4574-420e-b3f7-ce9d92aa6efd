"""User management service."""

from fastapi import FastAPI, HTTPException
from typing import List, Dict, Any, Optional
from datetime import datetime

app = FastAPI(title="CHaBot User Service", version="1.0.0")

class UserService:
    def __init__(self):
        self.users = {}
        self.user_profiles = {}
    
    async def create_user(self, username: str, email: str, role: str = "employee", 
                         organization_id: str = None, department_id: str = None) -> Dict[str, Any]:
        """Create a new user."""
        user_id = f"user_{len(self.users) + 1}"
        
        user = {
            "id": user_id,
            "username": username,
            "email": email,
            "role": role,
            "organization_id": organization_id,
            "department_id": department_id,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "last_login": None,
            "is_active": True
        }
        
        self.users[user_id] = user
        
        # Create user profile
        profile = {
            "user_id": user_id,
            "preferences": {},
            "context": {},
            "interaction_history": [],
            "personalization": {}
        }
        
        self.user_profiles[user_id] = profile
        
        return user
    
    async def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID."""
        return self.users.get(user_id)
    
    async def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username."""
        for user in self.users.values():
            if user["username"] == username:
                return user
        return None
    
    async def update_user(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """Update user information."""
        if user_id not in self.users:
            return False
        
        user = self.users[user_id]
        for key, value in updates.items():
            if key in user:
                user[key] = value
        
        user["updated_at"] = datetime.utcnow().isoformat()
        return True
    
    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user profile."""
        return self.user_profiles.get(user_id)
    
    async def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> bool:
        """Update user preferences."""
        if user_id not in self.user_profiles:
            return False
        
        self.user_profiles[user_id]["preferences"].update(preferences)
        return True
    
    async def record_user_interaction(self, user_id: str, interaction: Dict[str, Any]):
        """Record user interaction for personalization."""
        if user_id not in self.user_profiles:
            return
        
        interaction["timestamp"] = datetime.utcnow().isoformat()
        self.user_profiles[user_id]["interaction_history"].append(interaction)
        
        # Keep only recent interactions
        history = self.user_profiles[user_id]["interaction_history"]
        if len(history) > 100:
            self.user_profiles[user_id]["interaction_history"] = history[-100:]
    
    async def get_users_by_organization(self, organization_id: str) -> List[Dict[str, Any]]:
        """Get all users in an organization."""
        return [
            user for user in self.users.values()
            if user.get("organization_id") == organization_id
        ]
    
    async def get_users_by_department(self, department_id: str) -> List[Dict[str, Any]]:
        """Get all users in a department."""
        return [
            user for user in self.users.values()
            if user.get("department_id") == department_id
        ]
    
    async def update_last_login(self, user_id: str):
        """Update user's last login time."""
        if user_id in self.users:
            self.users[user_id]["last_login"] = datetime.utcnow().isoformat()
    
    async def deactivate_user(self, user_id: str) -> bool:
        """Deactivate a user."""
        if user_id not in self.users:
            return False
        
        self.users[user_id]["is_active"] = False
        self.users[user_id]["updated_at"] = datetime.utcnow().isoformat()
        return True
    
    async def get_user_context(self, user_id: str) -> Dict[str, Any]:
        """Get user context for personalized responses."""
        user = self.users.get(user_id)
        profile = self.user_profiles.get(user_id)
        
        if not user or not profile:
            return {}
        
        return {
            "user_id": user_id,
            "role": user["role"],
            "organization_id": user.get("organization_id"),
            "department_id": user.get("department_id"),
            "preferences": profile.get("preferences", {}),
            "recent_interactions": profile.get("interaction_history", [])[-5:]
        }

@app.post("/users")
async def create_user(username: str, email: str, role: str = "employee", 
                     organization_id: str = None, department_id: str = None):
    service = UserService()
    return await service.create_user(username, email, role, organization_id, department_id)

@app.get("/users/{user_id}")
async def get_user(user_id: str):
    service = UserService()
    user = await service.get_user(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@app.get("/users/{user_id}/context")
async def get_user_context(user_id: str):
    service = UserService()
    return await service.get_user_context(user_id)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8007)