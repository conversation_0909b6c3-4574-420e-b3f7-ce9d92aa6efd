"""Admin service."""

from fastapi import FastAPI
from backend.api.admin.admin_endpoints import router
from backend.utils.logging import StructuredLogger
from typing import Dict, Any, List
from datetime import datetime

app = FastAPI(title="CHaBot Admin Service", version="1.0.0")
logger = StructuredLogger("admin_service")

app.include_router(router)

class AdminService:
    def __init__(self):
        self.maintenance_mode = False
        self.start_time = datetime.utcnow()
    
    async def get_user_stats(self) -> Dict[str, Any]:
        return {"total_users": 150, "active_users": 45, "new_users_today": 3}
    
    async def get_system_stats(self) -> Dict[str, Any]:
        uptime = datetime.utcnow() - self.start_time
        return {"total_conversations": 1250, "total_messages": 8500, "active_agents": 4, "system_uptime": str(uptime)}
    
    async def set_maintenance_mode(self, enabled: bool):
        self.maintenance_mode = enabled
    
    async def get_logs(self, level: str, limit: int) -> List[Dict[str, Any]]:
        return [{"timestamp": datetime.utcnow().isoformat(), "level": level, "message": "Sample log", "service": "admin"}]

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)