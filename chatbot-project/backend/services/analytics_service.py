"""
Advanced Analytics Service for CHaBot System.
Provides comprehensive analytics, monitoring, and insights.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import statistics
import numpy as np
from collections import defaultdict, Counter
import pandas as pd
from fastapi import FastAPI, HTTPException, Depends, Query
from pydantic import BaseModel
import redis
import psycopg2
from psycopg2.extras import RealDictCursor


class MetricType(Enum):
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class Metric:
    name: str
    type: MetricType
    value: float
    timestamp: datetime
    labels: Dict[str, str]
    description: str = ""


@dataclass
class ConversationAnalytics:
    conversation_id: str
    user_id: str
    start_time: datetime
    end_time: Optional[datetime]
    message_count: int
    avg_response_time: float
    user_satisfaction: Optional[float]
    topics: List[str]
    agents_used: List[str]
    success_rate: float
    organization: str
    department: str


@dataclass
class AgentPerformanceMetrics:
    agent_id: str
    agent_type: str
    total_tasks: int
    successful_tasks: int
    failed_tasks: int
    avg_response_time: float
    avg_confidence: float
    resource_usage: Dict[str, float]
    collaboration_score: float
    last_active: datetime


@dataclass
class SystemHealthMetrics:
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    active_connections: int
    queue_sizes: Dict[str, int]
    error_rate: float
    throughput: float


class AnalyticsService:
    """Advanced analytics service for CHaBot system."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", 
                 postgres_url: str = "postgresql://localhost:5432/chatbot_db"):
        self.logger = logging.getLogger(__name__)
        self.redis_client = redis.from_url(redis_url)
        self.postgres_url = postgres_url
        
        # Metrics storage
        self.metrics_buffer = []
        self.conversation_analytics = {}
        self.agent_metrics = {}
        self.system_metrics = []
        
        # Analytics configuration
        self.config = {
            "metrics_retention_days": 30,
            "analytics_update_interval": 60,  # seconds
            "batch_size": 1000,
            "alert_thresholds": {
                "error_rate": 0.05,  # 5%
                "response_time_p95": 5.0,  # 5 seconds
                "cpu_usage": 80.0,  # 80%
                "memory_usage": 85.0,  # 85%
                "agent_failure_rate": 0.10  # 10%
            }
        }
        
        # Start background tasks
        asyncio.create_task(self.start_analytics_processor())
    
    async def start_analytics_processor(self):
        """Start background analytics processing."""
        while True:
            try:
                await self.process_metrics()
                await self.update_analytics()
                await self.check_alerts()
                await asyncio.sleep(self.config["analytics_update_interval"])
            except Exception as e:
                self.logger.error(f"Analytics processor error: {e}")
                await asyncio.sleep(10)
    
    # Metrics Collection
    def record_metric(self, name: str, value: float, metric_type: MetricType, 
                     labels: Dict[str, str] = None, description: str = ""):
        """Record a metric."""
        metric = Metric(
            name=name,
            type=metric_type,
            value=value,
            timestamp=datetime.now(),
            labels=labels or {},
            description=description
        )
        
        self.metrics_buffer.append(metric)
        
        # Store in Redis for real-time access
        redis_key = f"metric:{name}:{int(metric.timestamp.timestamp())}"
        self.redis_client.setex(
            redis_key, 
            3600,  # 1 hour TTL
            json.dumps(asdict(metric), default=str)
        )
    
    def record_conversation_start(self, conversation_id: str, user_id: str, 
                                organization: str, department: str):
        """Record conversation start."""
        analytics = ConversationAnalytics(
            conversation_id=conversation_id,
            user_id=user_id,
            start_time=datetime.now(),
            end_time=None,
            message_count=0,
            avg_response_time=0.0,
            user_satisfaction=None,
            topics=[],
            agents_used=[],
            success_rate=0.0,
            organization=organization,
            department=department
        )
        
        self.conversation_analytics[conversation_id] = analytics
        
        # Record metrics
        self.record_metric(
            "conversations_started",
            1,
            MetricType.COUNTER,
            {"organization": organization, "department": department}
        )
    
    def record_message_exchange(self, conversation_id: str, response_time: float, 
                              agents_used: List[str], topics: List[str], success: bool):
        """Record message exchange in conversation."""
        if conversation_id in self.conversation_analytics:
            analytics = self.conversation_analytics[conversation_id]
            analytics.message_count += 1
            
            # Update average response time
            if analytics.avg_response_time == 0:
                analytics.avg_response_time = response_time
            else:
                analytics.avg_response_time = (
                    (analytics.avg_response_time * (analytics.message_count - 1) + response_time) 
                    / analytics.message_count
                )
            
            # Update agents used
            for agent in agents_used:
                if agent not in analytics.agents_used:
                    analytics.agents_used.append(agent)
            
            # Update topics
            for topic in topics:
                if topic not in analytics.topics:
                    analytics.topics.append(topic)
            
            # Update success rate
            current_successes = analytics.success_rate * (analytics.message_count - 1)
            if success:
                current_successes += 1
            analytics.success_rate = current_successes / analytics.message_count
        
        # Record individual metrics
        self.record_metric("response_time", response_time, MetricType.TIMER)
        self.record_metric("message_processed", 1, MetricType.COUNTER, 
                          {"success": str(success)})
        
        for agent in agents_used:
            self.record_metric("agent_usage", 1, MetricType.COUNTER, 
                              {"agent": agent})
    
    def record_conversation_end(self, conversation_id: str, user_satisfaction: float = None):
        """Record conversation end."""
        if conversation_id in self.conversation_analytics:
            analytics = self.conversation_analytics[conversation_id]
            analytics.end_time = datetime.now()
            analytics.user_satisfaction = user_satisfaction
            
            # Calculate conversation duration
            duration = (analytics.end_time - analytics.start_time).total_seconds()
            
            self.record_metric("conversation_duration", duration, MetricType.TIMER,
                              {"organization": analytics.organization})
            
            if user_satisfaction is not None:
                self.record_metric("user_satisfaction", user_satisfaction, MetricType.GAUGE,
                                  {"organization": analytics.organization})
    
    def record_agent_performance(self, agent_id: str, agent_type: str, 
                               task_success: bool, response_time: float, 
                               confidence: float, resource_usage: Dict[str, float]):
        """Record agent performance metrics."""
        if agent_id not in self.agent_metrics:
            self.agent_metrics[agent_id] = AgentPerformanceMetrics(
                agent_id=agent_id,
                agent_type=agent_type,
                total_tasks=0,
                successful_tasks=0,
                failed_tasks=0,
                avg_response_time=0.0,
                avg_confidence=0.0,
                resource_usage={},
                collaboration_score=0.0,
                last_active=datetime.now()
            )
        
        metrics = self.agent_metrics[agent_id]
        metrics.total_tasks += 1
        metrics.last_active = datetime.now()
        
        if task_success:
            metrics.successful_tasks += 1
        else:
            metrics.failed_tasks += 1
        
        # Update averages
        if metrics.avg_response_time == 0:
            metrics.avg_response_time = response_time
        else:
            metrics.avg_response_time = (
                (metrics.avg_response_time * (metrics.total_tasks - 1) + response_time) 
                / metrics.total_tasks
            )
        
        if metrics.avg_confidence == 0:
            metrics.avg_confidence = confidence
        else:
            metrics.avg_confidence = (
                (metrics.avg_confidence * (metrics.total_tasks - 1) + confidence) 
                / metrics.total_tasks
            )
        
        # Update resource usage
        for resource, usage in resource_usage.items():
            if resource not in metrics.resource_usage:
                metrics.resource_usage[resource] = usage
            else:
                metrics.resource_usage[resource] = (
                    (metrics.resource_usage[resource] * (metrics.total_tasks - 1) + usage) 
                    / metrics.total_tasks
                )
        
        # Record individual metrics
        self.record_metric("agent_task_completion", 1, MetricType.COUNTER,
                          {"agent": agent_id, "success": str(task_success)})
        self.record_metric("agent_response_time", response_time, MetricType.TIMER,
                          {"agent": agent_id})
        self.record_metric("agent_confidence", confidence, MetricType.GAUGE,
                          {"agent": agent_id})
    
    def record_system_health(self, cpu_usage: float, memory_usage: float, 
                           disk_usage: float, network_io: Dict[str, float],
                           active_connections: int, queue_sizes: Dict[str, int],
                           error_rate: float, throughput: float):
        """Record system health metrics."""
        health_metrics = SystemHealthMetrics(
            timestamp=datetime.now(),
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            disk_usage=disk_usage,
            network_io=network_io,
            active_connections=active_connections,
            queue_sizes=queue_sizes,
            error_rate=error_rate,
            throughput=throughput
        )
        
        self.system_metrics.append(health_metrics)
        
        # Keep only recent metrics
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.system_metrics = [
            m for m in self.system_metrics if m.timestamp > cutoff_time
        ]
        
        # Record individual metrics
        self.record_metric("system_cpu_usage", cpu_usage, MetricType.GAUGE)
        self.record_metric("system_memory_usage", memory_usage, MetricType.GAUGE)
        self.record_metric("system_disk_usage", disk_usage, MetricType.GAUGE)
        self.record_metric("system_active_connections", active_connections, MetricType.GAUGE)
        self.record_metric("system_error_rate", error_rate, MetricType.GAUGE)
        self.record_metric("system_throughput", throughput, MetricType.GAUGE)
    
    # Analytics Processing
    async def process_metrics(self):
        """Process accumulated metrics."""
        if not self.metrics_buffer:
            return
        
        # Batch process metrics
        batch = self.metrics_buffer[:self.config["batch_size"]]
        self.metrics_buffer = self.metrics_buffer[self.config["batch_size"]:]
        
        # Store in database
        await self.store_metrics_in_db(batch)
        
        # Update real-time aggregations
        await self.update_real_time_aggregations(batch)
    
    async def store_metrics_in_db(self, metrics: List[Metric]):
        """Store metrics in PostgreSQL database."""
        try:
            conn = psycopg2.connect(self.postgres_url)
            cursor = conn.cursor()
            
            # Insert metrics
            for metric in metrics:
                cursor.execute("""
                    INSERT INTO metrics (name, type, value, timestamp, labels, description)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    metric.name,
                    metric.type.value,
                    metric.value,
                    metric.timestamp,
                    json.dumps(metric.labels),
                    metric.description
                ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Failed to store metrics in database: {e}")
    
    async def update_real_time_aggregations(self, metrics: List[Metric]):
        """Update real-time metric aggregations."""
        # Group metrics by name and calculate aggregations
        metric_groups = defaultdict(list)
        for metric in metrics:
            metric_groups[metric.name].append(metric.value)
        
        for metric_name, values in metric_groups.items():
            # Calculate aggregations
            aggregations = {
                "count": len(values),
                "sum": sum(values),
                "avg": statistics.mean(values),
                "min": min(values),
                "max": max(values),
                "p50": statistics.median(values),
                "p95": np.percentile(values, 95) if len(values) > 1 else values[0],
                "p99": np.percentile(values, 99) if len(values) > 1 else values[0]
            }
            
            # Store aggregations in Redis
            redis_key = f"aggregation:{metric_name}"
            self.redis_client.setex(redis_key, 300, json.dumps(aggregations))  # 5 min TTL
    
    async def update_analytics(self):
        """Update analytics dashboards and reports."""
        # Update conversation analytics
        await self.update_conversation_analytics()
        
        # Update agent performance analytics
        await self.update_agent_analytics()
        
        # Update system health analytics
        await self.update_system_analytics()
        
        # Generate insights
        await self.generate_insights()
    
    async def update_conversation_analytics(self):
        """Update conversation analytics."""
        # Calculate conversation metrics
        active_conversations = len([
            c for c in self.conversation_analytics.values() 
            if c.end_time is None
        ])
        
        completed_conversations = [
            c for c in self.conversation_analytics.values() 
            if c.end_time is not None
        ]
        
        if completed_conversations:
            avg_duration = statistics.mean([
                (c.end_time - c.start_time).total_seconds() 
                for c in completed_conversations
            ])
            
            avg_satisfaction = statistics.mean([
                c.user_satisfaction for c in completed_conversations 
                if c.user_satisfaction is not None
            ])
            
            avg_success_rate = statistics.mean([
                c.success_rate for c in completed_conversations
            ])
            
            # Store in Redis
            conversation_stats = {
                "active_conversations": active_conversations,
                "completed_conversations": len(completed_conversations),
                "avg_duration": avg_duration,
                "avg_satisfaction": avg_satisfaction,
                "avg_success_rate": avg_success_rate,
                "timestamp": datetime.now().isoformat()
            }
            
            self.redis_client.setex(
                "analytics:conversations", 
                300, 
                json.dumps(conversation_stats)
            )
    
    async def update_agent_analytics(self):
        """Update agent performance analytics."""
        if not self.agent_metrics:
            return
        
        # Calculate agent performance statistics
        total_tasks = sum(m.total_tasks for m in self.agent_metrics.values())
        total_successful = sum(m.successful_tasks for m in self.agent_metrics.values())
        
        agent_stats = {
            "total_agents": len(self.agent_metrics),
            "active_agents": len([
                m for m in self.agent_metrics.values() 
                if (datetime.now() - m.last_active).total_seconds() < 300
            ]),
            "total_tasks": total_tasks,
            "overall_success_rate": total_successful / total_tasks if total_tasks > 0 else 0,
            "avg_response_time": statistics.mean([
                m.avg_response_time for m in self.agent_metrics.values()
            ]),
            "avg_confidence": statistics.mean([
                m.avg_confidence for m in self.agent_metrics.values()
            ]),
            "timestamp": datetime.now().isoformat()
        }
        
        # Agent type breakdown
        agent_types = defaultdict(list)
        for metrics in self.agent_metrics.values():
            agent_types[metrics.agent_type].append(metrics)
        
        type_stats = {}
        for agent_type, agents in agent_types.items():
            type_stats[agent_type] = {
                "count": len(agents),
                "avg_success_rate": statistics.mean([
                    a.successful_tasks / a.total_tasks if a.total_tasks > 0 else 0 
                    for a in agents
                ]),
                "avg_response_time": statistics.mean([a.avg_response_time for a in agents])
            }
        
        agent_stats["by_type"] = type_stats
        
        # Store in Redis
        self.redis_client.setex(
            "analytics:agents", 
            300, 
            json.dumps(agent_stats)
        )
    
    async def update_system_analytics(self):
        """Update system health analytics."""
        if not self.system_metrics:
            return
        
        recent_metrics = [
            m for m in self.system_metrics 
            if (datetime.now() - m.timestamp).total_seconds() < 3600  # Last hour
        ]
        
        if recent_metrics:
            system_stats = {
                "avg_cpu_usage": statistics.mean([m.cpu_usage for m in recent_metrics]),
                "max_cpu_usage": max([m.cpu_usage for m in recent_metrics]),
                "avg_memory_usage": statistics.mean([m.memory_usage for m in recent_metrics]),
                "max_memory_usage": max([m.memory_usage for m in recent_metrics]),
                "avg_error_rate": statistics.mean([m.error_rate for m in recent_metrics]),
                "avg_throughput": statistics.mean([m.throughput for m in recent_metrics]),
                "timestamp": datetime.now().isoformat()
            }
            
            # Store in Redis
            self.redis_client.setex(
                "analytics:system", 
                300, 
                json.dumps(system_stats)
            )
    
    async def generate_insights(self):
        """Generate analytical insights."""
        insights = []
        
        # Conversation insights
        if self.conversation_analytics:
            completed = [c for c in self.conversation_analytics.values() if c.end_time]
            if completed:
                avg_messages = statistics.mean([c.message_count for c in completed])
                if avg_messages > 10:
                    insights.append({
                        "type": "conversation",
                        "level": "info",
                        "message": f"Users are having longer conversations (avg {avg_messages:.1f} messages)",
                        "recommendation": "Consider conversation flow optimization"
                    })
        
        # Agent insights
        if self.agent_metrics:
            low_performing_agents = [
                m for m in self.agent_metrics.values()
                if m.total_tasks > 10 and (m.successful_tasks / m.total_tasks) < 0.8
            ]
            
            if low_performing_agents:
                insights.append({
                    "type": "agent",
                    "level": "warning",
                    "message": f"{len(low_performing_agents)} agents have success rate below 80%",
                    "recommendation": "Review agent configurations and training"
                })
        
        # System insights
        if self.system_metrics:
            recent = self.system_metrics[-10:]  # Last 10 measurements
            if recent:
                avg_cpu = statistics.mean([m.cpu_usage for m in recent])
                if avg_cpu > 80:
                    insights.append({
                        "type": "system",
                        "level": "warning",
                        "message": f"High CPU usage detected ({avg_cpu:.1f}%)",
                        "recommendation": "Consider scaling up resources"
                    })
        
        # Store insights
        self.redis_client.setex(
            "analytics:insights", 
            600, 
            json.dumps(insights)
        )
    
    async def check_alerts(self):
        """Check for alert conditions."""
        alerts = []
        thresholds = self.config["alert_thresholds"]
        
        # Check system metrics
        if self.system_metrics:
            latest = self.system_metrics[-1]
            
            if latest.cpu_usage > thresholds["cpu_usage"]:
                alerts.append({
                    "type": "system",
                    "severity": "warning",
                    "message": f"High CPU usage: {latest.cpu_usage:.1f}%",
                    "timestamp": latest.timestamp.isoformat()
                })
            
            if latest.memory_usage > thresholds["memory_usage"]:
                alerts.append({
                    "type": "system",
                    "severity": "warning",
                    "message": f"High memory usage: {latest.memory_usage:.1f}%",
                    "timestamp": latest.timestamp.isoformat()
                })
            
            if latest.error_rate > thresholds["error_rate"]:
                alerts.append({
                    "type": "system",
                    "severity": "critical",
                    "message": f"High error rate: {latest.error_rate:.2%}",
                    "timestamp": latest.timestamp.isoformat()
                })
        
        # Check agent performance
        for agent_id, metrics in self.agent_metrics.items():
            if metrics.total_tasks > 10:
                failure_rate = metrics.failed_tasks / metrics.total_tasks
                if failure_rate > thresholds["agent_failure_rate"]:
                    alerts.append({
                        "type": "agent",
                        "severity": "warning",
                        "message": f"Agent {agent_id} has high failure rate: {failure_rate:.2%}",
                        "timestamp": datetime.now().isoformat()
                    })
        
        if alerts:
            # Store alerts
            self.redis_client.lpush("alerts", *[json.dumps(alert) for alert in alerts])
            self.redis_client.ltrim("alerts", 0, 99)  # Keep last 100 alerts
            
            # Log critical alerts
            for alert in alerts:
                if alert["severity"] == "critical":
                    self.logger.critical(f"ALERT: {alert['message']}")
    
    # API Endpoints
    async def get_dashboard_data(self, time_range: str = "1h") -> Dict[str, Any]:
        """Get dashboard data for specified time range."""
        # Get cached analytics
        conversation_stats = self.redis_client.get("analytics:conversations")
        agent_stats = self.redis_client.get("analytics:agents")
        system_stats = self.redis_client.get("analytics:system")
        insights = self.redis_client.get("analytics:insights")
        
        dashboard_data = {
            "conversations": json.loads(conversation_stats) if conversation_stats else {},
            "agents": json.loads(agent_stats) if agent_stats else {},
            "system": json.loads(system_stats) if system_stats else {},
            "insights": json.loads(insights) if insights else [],
            "time_range": time_range,
            "last_updated": datetime.now().isoformat()
        }
        
        return dashboard_data
    
    async def get_agent_performance_report(self, agent_id: str = None) -> Dict[str, Any]:
        """Get detailed agent performance report."""
        if agent_id:
            if agent_id in self.agent_metrics:
                return asdict(self.agent_metrics[agent_id])
            else:
                raise HTTPException(status_code=404, detail="Agent not found")
        else:
            return {
                agent_id: asdict(metrics) 
                for agent_id, metrics in self.agent_metrics.items()
            }
    
    async def get_conversation_analytics(self, organization: str = None) -> Dict[str, Any]:
        """Get conversation analytics."""
        conversations = list(self.conversation_analytics.values())
        
        if organization:
            conversations = [c for c in conversations if c.organization == organization]
        
        if not conversations:
            return {"message": "No conversation data available"}
        
        # Calculate analytics
        completed = [c for c in conversations if c.end_time is not None]
        
        analytics = {
            "total_conversations": len(conversations),
            "completed_conversations": len(completed),
            "active_conversations": len(conversations) - len(completed),
        }
        
        if completed:
            analytics.update({
                "avg_duration": statistics.mean([
                    (c.end_time - c.start_time).total_seconds() for c in completed
                ]),
                "avg_messages": statistics.mean([c.message_count for c in completed]),
                "avg_success_rate": statistics.mean([c.success_rate for c in completed]),
                "avg_satisfaction": statistics.mean([
                    c.user_satisfaction for c in completed 
                    if c.user_satisfaction is not None
                ]) if any(c.user_satisfaction for c in completed) else None
            })
        
        return analytics
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get current system health status."""
        if not self.system_metrics:
            return {"status": "no_data"}
        
        latest = self.system_metrics[-1]
        
        # Determine overall health status
        status = "healthy"
        if (latest.cpu_usage > 80 or latest.memory_usage > 85 or 
            latest.error_rate > 0.05):
            status = "warning"
        if (latest.cpu_usage > 95 or latest.memory_usage > 95 or 
            latest.error_rate > 0.10):
            status = "critical"
        
        return {
            "status": status,
            "timestamp": latest.timestamp.isoformat(),
            "cpu_usage": latest.cpu_usage,
            "memory_usage": latest.memory_usage,
            "disk_usage": latest.disk_usage,
            "error_rate": latest.error_rate,
            "throughput": latest.throughput,
            "active_connections": latest.active_connections
        }
    
    async def get_alerts(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent alerts."""
        alert_data = self.redis_client.lrange("alerts", 0, limit - 1)
        alerts = [json.loads(alert) for alert in alert_data]
        return alerts


# FastAPI application
app = FastAPI(title="CHaBot Analytics Service", version="1.0.0")

# Global analytics service instance
analytics_service = AnalyticsService()


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "analytics"}


@app.get("/dashboard")
async def get_dashboard(time_range: str = Query("1h", regex="^(1h|6h|24h|7d|30d)$")):
    """Get dashboard data."""
    return await analytics_service.get_dashboard_data(time_range)


@app.get("/agents/performance")
async def get_agent_performance(agent_id: Optional[str] = None):
    """Get agent performance metrics."""
    return await analytics_service.get_agent_performance_report(agent_id)


@app.get("/conversations/analytics")
async def get_conversation_analytics(organization: Optional[str] = None):
    """Get conversation analytics."""
    return await analytics_service.get_conversation_analytics(organization)


@app.get("/system/health")
async def get_system_health():
    """Get system health status."""
    return await analytics_service.get_system_health()


@app.get("/alerts")
async def get_alerts(limit: int = Query(20, ge=1, le=100)):
    """Get recent alerts."""
    return await analytics_service.get_alerts(limit)


# Metrics recording endpoints
class MetricRequest(BaseModel):
    name: str
    value: float
    metric_type: str
    labels: Dict[str, str] = {}
    description: str = ""


@app.post("/metrics/record")
async def record_metric(metric: MetricRequest):
    """Record a custom metric."""
    try:
        metric_type = MetricType(metric.metric_type)
        analytics_service.record_metric(
            metric.name, metric.value, metric_type, 
            metric.labels, metric.description
        )
        return {"status": "recorded"}
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid metric type")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)