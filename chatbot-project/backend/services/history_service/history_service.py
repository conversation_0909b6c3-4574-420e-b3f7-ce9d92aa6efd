"""Conversation history service."""

from fastapi import FastAPI, HTTPException
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

app = FastAPI(title="CHaBot History Service", version="1.0.0")

class HistoryService:
    def __init__(self):
        self.conversations = {}
        self.messages = {}
    
    async def get_conversation_history(self, conversation_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get conversation history."""
        messages = self.messages.get(conversation_id, [])
        return messages[-limit:] if messages else []
    
    async def get_user_conversations(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get user's conversation list."""
        user_conversations = [
            conv for conv in self.conversations.values() 
            if conv.get("user_id") == user_id
        ]
        return sorted(user_conversations, key=lambda x: x.get("updated_at", ""), reverse=True)[:limit]
    
    async def search_conversations(self, user_id: str, query: str) -> List[Dict[str, Any]]:
        """Search conversations by content."""
        results = []
        for conv_id, messages in self.messages.items():
            conv = self.conversations.get(conv_id)
            if conv and conv.get("user_id") == user_id:
                for message in messages:
                    if query.lower() in message.get("content", "").lower():
                        results.append({
                            "conversation_id": conv_id,
                            "message": message,
                            "conversation_title": conv.get("title", "")
                        })
        return results
    
    async def get_context_window(self, conversation_id: str, message_id: str, 
                                window_size: int = 5) -> List[Dict[str, Any]]:
        """Get context window around a specific message."""
        messages = self.messages.get(conversation_id, [])
        
        # Find message index
        message_idx = None
        for i, msg in enumerate(messages):
            if msg.get("id") == message_id:
                message_idx = i
                break
        
        if message_idx is None:
            return []
        
        # Get context window
        start_idx = max(0, message_idx - window_size)
        end_idx = min(len(messages), message_idx + window_size + 1)
        
        return messages[start_idx:end_idx]

@app.get("/conversations/{conversation_id}/history")
async def get_history(conversation_id: str, limit: int = 50):
    service = HistoryService()
    return await service.get_conversation_history(conversation_id, limit)

@app.get("/users/{user_id}/conversations")
async def get_user_conversations(user_id: str, limit: int = 20):
    service = HistoryService()
    return await service.get_user_conversations(user_id, limit)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)