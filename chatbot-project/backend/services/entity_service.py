from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from ai.nlp.entity.models.enhanced_entity_extractor import EnhancedEntityExtractor

class EntityExtractionRequest(BaseModel):
    text: str
    context: str = ""

class MultiDocumentRequest(BaseModel):
    documents: List[str]

class EntitySearchRequest(BaseModel):
    entity_type: str
    documents: List[str]

class EntityDetailsRequest(BaseModel):
    entity_text: str
    entity_type: str

class RelationshipSearchRequest(BaseModel):
    entity1: str
    entity2: str
    documents: List[str]

class EntityExtractionResponse(BaseModel):
    entities: Dict[str, List[Dict]]
    processing_info: Dict

class MultiDocumentResponse(BaseModel):
    document_entities: Dict
    resolved_entities: Dict
    relationships: List[Dict]
    summary: Dict

class EntityService:
    def __init__(self):
        self.entity_extractor = EnhancedEntityExtractor()
        
    def extract_entities(self, request: EntityExtractionRequest) -> EntityExtractionResponse:
        entities = self.entity_extractor.extract_and_process_entities(
            request.text, 
            request.context
        )
        
        processing_info = {
            "text_length": len(request.text),
            "entity_types_found": list(entities.keys()),
            "total_entities": sum(len(entity_list) for entity_list in entities.values())
        }
        
        return EntityExtractionResponse(
            entities=entities,
            processing_info=processing_info
        )
    
    def process_multiple_documents(self, request: MultiDocumentRequest) -> MultiDocumentResponse:
        result = self.entity_extractor.process_multiple_documents(request.documents)
        
        return MultiDocumentResponse(
            document_entities=result["document_entities"],
            resolved_entities=result["resolved_entities"],
            relationships=result["relationships"],
            summary=result["summary"]
        )
    
    def search_entities(self, request: EntitySearchRequest) -> List[Dict]:
        return self.entity_extractor.search_entities_by_type(
            request.entity_type,
            request.documents
        )
    
    def get_entity_details(self, request: EntityDetailsRequest) -> Optional[Dict]:
        return self.entity_extractor.get_entity_details(
            request.entity_text,
            request.entity_type
        )
    
    def find_relationships(self, request: RelationshipSearchRequest) -> List[Dict]:
        return self.entity_extractor.find_entity_relationships(
            request.entity1,
            request.entity2,
            request.documents
        )

app = FastAPI(title="Entity Extraction Service")
entity_service = EntityService()

@app.post("/extract", response_model=EntityExtractionResponse)
async def extract_entities(request: EntityExtractionRequest):
    try:
        return entity_service.extract_entities(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/process-documents", response_model=MultiDocumentResponse)
async def process_multiple_documents(request: MultiDocumentRequest):
    try:
        return entity_service.process_multiple_documents(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search")
async def search_entities(request: EntitySearchRequest):
    try:
        return entity_service.search_entities(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/details")
async def get_entity_details(request: EntityDetailsRequest):
    try:
        result = entity_service.get_entity_details(request)
        if result is None:
            raise HTTPException(status_code=404, detail="Entity not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/relationships")
async def find_relationships(request: RelationshipSearchRequest):
    try:
        return entity_service.find_relationships(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "entity_service"}