from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.orchestrator.orchestrator_agent import OrchestratorAgent
from agents.specialized.knowledge_agent import KnowledgeAgent
from agents.specialized.reasoning_agent import ReasoningAgent
from agents.communication.agent_coordinator import AgentCoordinator
from agents.memory.conversation_memory import ConversationMemory
from ai.llm.inference.llm_engine import LLMEngine
from ai.knowledge.document_processor import DocumentProcessor

class ChatRequest(BaseModel):
    query: str
    user_id: str
    user_context: Optional[Dict] = None
    conversation_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    confidence: float
    agents_used: List[str]
    reasoning_trace: Optional[Dict] = None
    sources: int

class AgenticChatService:
    def __init__(self):
        self.coordinator = AgentCoordinator()
        self.memory = ConversationMemory()
        self.llm_engine = LLMEngine()
        self.doc_processor = DocumentProcessor()
        self.initialized = False
    
    async def initialize(self):
        """Initialize the agentic chat system"""
        try:
            # Initialize LLM
            await self.llm_engine.initialize()
            
            # Load documents
            documents = self.doc_processor.load_documents()
            
            # Initialize agents
            orchestrator = OrchestratorAgent()
            knowledge_agent = KnowledgeAgent()
            reasoning_agent = ReasoningAgent()
            
            # Initialize knowledge agent
            await knowledge_agent.initialize(documents)
            
            # Register agents with coordinator
            self.coordinator.register_agent("OrchestratorAgent", orchestrator)
            self.coordinator.register_agent("KnowledgeAgent", knowledge_agent)
            self.coordinator.register_agent("ReasoningAgent", reasoning_agent)
            
            self.initialized = True
            
            return {
                "status": "initialized",
                "agents_registered": 3,
                "documents_loaded": len(documents),
                "llm_ready": True
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Initialization failed: {str(e)}")
    
    async def process_chat(self, request: ChatRequest) -> ChatResponse:
        """Process chat request using agentic system"""
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Service not initialized")
        
        try:
            # Get conversation context
            conversation_context = self.memory.get_context_for_query(request.user_id, request.query)
            
            # Prepare task for agents
            task = {
                "query": request.query,
                "user_context": request.user_context or {},
                "conversation_context": conversation_context,
                "user_id": request.user_id
            }
            
            # Coordinate multi-agent processing
            agent_results = await self.coordinator.coordinate_multi_agent_task(task)
            
            # Generate final response using LLM
            final_response = await self.generate_final_response(
                request.query, 
                agent_results, 
                request.user_context
            )
            
            # Store interaction in memory
            self.memory.store_interaction(
                request.user_id,
                request.query,
                final_response["response"],
                {"agents_used": agent_results.get("agents_used", [])}
            )
            
            return ChatResponse(
                response=final_response["response"],
                confidence=agent_results.get("overall_confidence", 0.5),
                agents_used=agent_results.get("agents_used", []),
                reasoning_trace=final_response.get("reasoning_trace"),
                sources=len(agent_results.get("combined_results", []))
            )
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Chat processing failed: {str(e)}")
    
    async def generate_final_response(self, query: str, agent_results: Dict[str, Any], 
                                    user_context: Dict = None) -> Dict[str, Any]:
        """Generate final response using LLM with agent results"""
        
        # Prepare context for LLM
        llm_context = {
            "knowledge_results": agent_results.get("combined_results", []),
            "user_context": user_context or {},
            "agent_coordination": True
        }
        
        # Generate response using LLM
        llm_response = await self.llm_engine.generate_response(query, llm_context)
        
        # If LLM fails, use agent results directly
        if "Error" in llm_response or not llm_response.strip():
            fallback_response = self.create_fallback_response(agent_results)
            return {
                "response": fallback_response,
                "reasoning_trace": {"method": "agent_fallback"}
            }
        
        return {
            "response": llm_response,
            "reasoning_trace": {
                "method": "llm_synthesis",
                "agents_consulted": agent_results.get("agents_used", []),
                "confidence": agent_results.get("overall_confidence", 0.5)
            }
        }
    
    def create_fallback_response(self, agent_results: Dict[str, Any]) -> str:
        """Create fallback response from agent results"""
        combined_results = agent_results.get("combined_results", [])
        
        if not combined_results:
            return "I couldn't find specific information for your query. Please try rephrasing your question."
        
        # Use top result
        top_result = combined_results[0]
        response_text = top_result.get("text", "")
        
        if len(response_text) > 300:
            response_text = response_text[:300] + "..."
        
        return f"Based on the available information: {response_text}"
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get service status"""
        coordination_status = self.coordinator.get_coordination_status()
        
        return {
            "initialized": self.initialized,
            "llm_info": self.llm_engine.get_model_info(),
            "coordination_status": coordination_status,
            "memory_active": len(self.memory.conversations) > 0
        }

app = FastAPI(title="Agentic Chat Service")
chat_service = AgenticChatService()

@app.post("/initialize")
async def initialize_service():
    try:
        return await chat_service.initialize()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat", response_model=ChatResponse)
async def process_chat(request: ChatRequest):
    try:
        return await chat_service.process_chat(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status")
async def get_service_status():
    try:
        return chat_service.get_service_status()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "agentic_chat_service",
        "initialized": chat_service.initialized
    }