from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from ai.knowledge.fusion.knowledge_fusion import KnowledgeFusion
from ai.knowledge.document_processor import DocumentProcessor

class KnowledgeRequest(BaseModel):
    query: str
    k: int = 10
    user_context: Optional[Dict] = None

class KnowledgeSummaryRequest(BaseModel):
    query: str
    user_context: Optional[Dict] = None

class KnowledgeService:
    def __init__(self):
        self.fusion_engine = KnowledgeFusion()
        self.doc_processor = DocumentProcessor()
        self.initialized = False
    
    def initialize(self, neo4j_uri: str = None):
        """Initialize knowledge fusion system"""
        try:
            # Load documents
            documents = self.doc_processor.load_documents()
            
            if not documents:
                raise ValueError("No documents found")
            
            # Initialize fusion engine
            self.fusion_engine.initialize(documents, neo4j_uri)
            self.initialized = True
            
            return {
                "status": "initialized",
                "documents_loaded": len(documents),
                "neo4j_available": neo4j_uri is not None
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Initialization failed: {str(e)}")
    
    def fused_search(self, request: KnowledgeRequest):
        """Perform fused knowledge search"""
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Knowledge service not initialized")
        
        try:
            return self.fusion_engine.fused_search(
                request.query,
                request.k,
                request.user_context
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")
    
    def get_knowledge_summary(self, request: KnowledgeSummaryRequest):
        """Get comprehensive knowledge summary"""
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Knowledge service not initialized")
        
        try:
            return self.fusion_engine.get_knowledge_summary(
                request.query,
                request.user_context
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Summary generation failed: {str(e)}")

app = FastAPI(title="Knowledge Fusion Service")
knowledge_service = KnowledgeService()

@app.post("/initialize")
async def initialize_service():
    try:
        return knowledge_service.initialize()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search")
async def fused_search(request: KnowledgeRequest):
    try:
        return knowledge_service.fused_search(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/summary")
async def get_knowledge_summary(request: KnowledgeSummaryRequest):
    try:
        return knowledge_service.get_knowledge_summary(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "knowledge_fusion_service",
        "initialized": knowledge_service.initialized
    }