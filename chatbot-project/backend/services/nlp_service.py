from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Dict, List
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from ai.nlp.intent.models.intent_classifier import IntentClassifier
from ai.nlp.decomposition.query_decomposer import QueryDecomposer

class QueryRequest(BaseModel):
    query: str
    user_context: Dict = {}

class QueryResponse(BaseModel):
    intent: str
    confidence: float
    complexity: str
    keywords: List[str]
    requires_decomposition: bool
    decomposition: Dict = None

class NLPService:
    def __init__(self):
        self.intent_classifier = IntentClassifier()
        self.query_decomposer = QueryDecomposer()
        
    def process_query(self, request: QueryRequest) -> QueryResponse:
        # Analyze intent
        analysis = self.intent_classifier.analyze_query(request.query)
        
        # Decompose if needed
        decomposition = None
        if analysis["requires_decomposition"]:
            decomposition = self.query_decomposer.decompose_query(request.query)
        
        return QueryResponse(
            intent=analysis["primary_intent"],
            confidence=analysis["confidence"],
            complexity=analysis["complexity"],
            keywords=analysis["keywords"],
            requires_decomposition=analysis["requires_decomposition"],
            decomposition=decomposition
        )

app = FastAPI(title="NLP Service")
nlp_service = NLPService()

@app.post("/process", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    try:
        return nlp_service.process_query(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/train")
async def train_model():
    try:
        from ai.nlp.intent.training.trainer import IntentTrainer
        trainer = IntentTrainer()
        model, label_encoder = trainer.train_model()
        return {"status": "Model trained successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "nlp_service"}