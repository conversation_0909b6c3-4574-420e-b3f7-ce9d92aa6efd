"""Memory management service for agents."""

from fastapi import FastAPI, HTTPException
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

app = FastAPI(title="CHaBot Memory Service", version="1.0.0")

class MemoryService:
    def __init__(self):
        self.memories = {}
        self.memory_types = ["conversation", "episodic", "semantic", "working"]
    
    async def store_memory(self, agent_id: str, memory_type: str, content: str, 
                          context: Dict[str, Any] = None, importance: float = 0.5) -> str:
        """Store a memory for an agent."""
        if memory_type not in self.memory_types:
            raise ValueError(f"Invalid memory type: {memory_type}")
        
        memory_id = f"{agent_id}_{memory_type}_{datetime.utcnow().timestamp()}"
        
        memory = {
            "id": memory_id,
            "agent_id": agent_id,
            "memory_type": memory_type,
            "content": content,
            "context": context or {},
            "importance": importance,
            "access_count": 0,
            "created_at": datetime.utcnow().isoformat(),
            "last_accessed": datetime.utcnow().isoformat()
        }
        
        if agent_id not in self.memories:
            self.memories[agent_id] = {}
        if memory_type not in self.memories[agent_id]:
            self.memories[agent_id][memory_type] = []
        
        self.memories[agent_id][memory_type].append(memory)
        
        # Manage memory limits
        await self._manage_memory_limits(agent_id, memory_type)
        
        return memory_id
    
    async def retrieve_memories(self, agent_id: str, memory_type: str = None, 
                               limit: int = 10) -> List[Dict[str, Any]]:
        """Retrieve memories for an agent."""
        if agent_id not in self.memories:
            return []
        
        if memory_type:
            memories = self.memories[agent_id].get(memory_type, [])
        else:
            memories = []
            for mem_type in self.memory_types:
                memories.extend(self.memories[agent_id].get(mem_type, []))
        
        # Sort by importance and recency
        memories.sort(key=lambda x: (x["importance"], x["created_at"]), reverse=True)
        
        # Update access count
        for memory in memories[:limit]:
            memory["access_count"] += 1
            memory["last_accessed"] = datetime.utcnow().isoformat()
        
        return memories[:limit]
    
    async def search_memories(self, agent_id: str, query: str, memory_type: str = None) -> List[Dict[str, Any]]:
        """Search memories by content."""
        memories = await self.retrieve_memories(agent_id, memory_type, limit=100)
        
        matching_memories = []
        for memory in memories:
            if query.lower() in memory["content"].lower():
                matching_memories.append(memory)
        
        return matching_memories
    
    async def update_memory_importance(self, memory_id: str, importance: float):
        """Update memory importance score."""
        for agent_id, agent_memories in self.memories.items():
            for memory_type, memories in agent_memories.items():
                for memory in memories:
                    if memory["id"] == memory_id:
                        memory["importance"] = importance
                        return
    
    async def _manage_memory_limits(self, agent_id: str, memory_type: str):
        """Manage memory limits by removing old, low-importance memories."""
        limits = {
            "conversation": 100,
            "episodic": 50,
            "semantic": 200,
            "working": 20
        }
        
        limit = limits.get(memory_type, 50)
        memories = self.memories[agent_id][memory_type]
        
        if len(memories) > limit:
            # Sort by importance and recency, keep the most important
            memories.sort(key=lambda x: (x["importance"], x["created_at"]), reverse=True)
            self.memories[agent_id][memory_type] = memories[:limit]
    
    async def get_memory_stats(self, agent_id: str) -> Dict[str, Any]:
        """Get memory statistics for an agent."""
        if agent_id not in self.memories:
            return {"total_memories": 0, "by_type": {}}
        
        stats = {"total_memories": 0, "by_type": {}}
        
        for memory_type in self.memory_types:
            count = len(self.memories[agent_id].get(memory_type, []))
            stats["by_type"][memory_type] = count
            stats["total_memories"] += count
        
        return stats

@app.post("/agents/{agent_id}/memories")
async def store_memory(agent_id: str, memory_type: str, content: str, 
                      context: Dict[str, Any] = None, importance: float = 0.5):
    service = MemoryService()
    memory_id = await service.store_memory(agent_id, memory_type, content, context, importance)
    return {"memory_id": memory_id}

@app.get("/agents/{agent_id}/memories")
async def get_memories(agent_id: str, memory_type: str = None, limit: int = 10):
    service = MemoryService()
    return await service.retrieve_memories(agent_id, memory_type, limit)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8006)