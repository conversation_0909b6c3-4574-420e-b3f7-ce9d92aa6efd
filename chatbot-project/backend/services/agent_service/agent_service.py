"""Agent service."""

from fastapi import FastAPI
from backend.api.agent.agent_endpoints import router
from backend.utils.logging import StructuredLogger

app = FastAPI(title="CHaBot Agent Service", version="1.0.0")
logger = StructuredLogger("agent_service")

app.include_router(router)

@app.on_event("startup")
async def startup_event():
    logger.log_structured("info", "Agent service starting up")

@app.on_event("shutdown")
async def shutdown_event():
    logger.log_structured("info", "Agent service shutting down")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)