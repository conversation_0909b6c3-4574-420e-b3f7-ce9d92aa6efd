"""AI service integration for backend."""

from typing import Dict, List, Any
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from ai.reasoning.engine.reasoning_engine import ReasoningEngine
from agents.coordinator.task_planning import TaskPlanner
from agents.tools.registry import ToolRegistry
from ai.reasoning.tree_of_thoughts.tree_of_thoughts import TreeOfThoughts

class AIService:
    def __init__(self):
        self.reasoning_engine = ReasoningEngine()
        self.task_planner = TaskPlanner()
        self.tool_registry = ToolRegistry()
        self.tree_of_thoughts = TreeOfThoughts()
    
    async def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process user query with AI."""
        try:
            # Create reasoning chain
            chain_id = self.reasoning_engine.create_reasoning_chain(query, context or {})
            
            # Plan tasks
            tasks = self.task_planner.create_plan(query, context or {})
            
            # Generate response using Tree of Thoughts
            reasoning_result = self.tree_of_thoughts.get_best_reasoning_path(query, context or {})
            
            return {
                "success": True,
                "query": query,
                "response": f"Based on my analysis: {query}",
                "reasoning_chain_id": chain_id,
                "tasks_created": len(tasks),
                "confidence": reasoning_result.get("confidence", 0.8),
                "reasoning_steps": reasoning_result.get("reasoning", [])
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }
    
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool."""
        return self.tool_registry.execute_tool(tool_name, **parameters)
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tools."""
        return self.tool_registry.list_tools()

# Global AI service instance
ai_service = AIService()