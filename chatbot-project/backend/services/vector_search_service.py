from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from ai.knowledge.vector_search.enhanced_vector_search import EnhancedVectorSearch
from ai.knowledge.document_processor import DocumentProcessor

class SearchRequest(BaseModel):
    query: str
    k: int = 10
    search_type: str = "hybrid"
    user_context: Optional[Dict] = None
    filters: Optional[Dict] = None

class MultiQueryRequest(BaseModel):
    queries: List[str]
    k: int = 10
    user_context: Optional[Dict] = None

class OrganizationSearchRequest(BaseModel):
    query: str
    organization: str
    k: int = 10

class PolicySearchRequest(BaseModel):
    query: str
    policy_type: Optional[str] = None
    k: int = 10

class SimilarDocumentRequest(BaseModel):
    document_text: str
    k: int = 5

class InitializeRequest(BaseModel):
    documents_path: str = "docs/"

class VectorSearchService:
    def __init__(self):
        self.search_engine = EnhancedVectorSearch()
        self.doc_processor = DocumentProcessor()
        self.initialized = False
    
    def initialize_search_engine(self, documents_path: str = "docs/") -> Dict:
        """Initialize the search engine with documents"""
        try:
            # Load documents
            documents = self.doc_processor.load_documents()
            
            if not documents:
                raise ValueError("No documents found to index")
            
            # Initialize search engine
            stats = self.search_engine.initialize_from_documents(documents)
            self.initialized = True
            
            return {
                "status": "initialized",
                "stats": stats
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Initialization failed: {str(e)}")
    
    def search(self, request: SearchRequest) -> List[Dict]:
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Search engine not initialized")
        
        return self.search_engine.search(
            request.query,
            request.k,
            request.user_context,
            request.search_type,
            request.filters
        )
    
    def multi_query_search(self, request: MultiQueryRequest) -> List[Dict]:
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Search engine not initialized")
        
        return self.search_engine.multi_query_search(
            request.queries,
            request.k,
            request.user_context
        )
    
    def organization_search(self, request: OrganizationSearchRequest) -> List[Dict]:
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Search engine not initialized")
        
        return self.search_engine.organization_specific_search(
            request.query,
            request.organization,
            request.k
        )
    
    def policy_search(self, request: PolicySearchRequest) -> List[Dict]:
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Search engine not initialized")
        
        return self.search_engine.policy_search(
            request.query,
            request.policy_type,
            request.k
        )
    
    def find_similar_documents(self, request: SimilarDocumentRequest) -> List[Dict]:
        if not self.initialized:
            raise HTTPException(status_code=400, detail="Search engine not initialized")
        
        return self.search_engine.get_similar_documents(
            request.document_text,
            request.k
        )

app = FastAPI(title="Vector Search Service")
search_service = VectorSearchService()

@app.post("/initialize")
async def initialize_search_engine(request: InitializeRequest):
    try:
        return search_service.initialize_search_engine(request.documents_path)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search")
async def search(request: SearchRequest):
    try:
        return search_service.search(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/multi-search")
async def multi_query_search(request: MultiQueryRequest):
    try:
        return search_service.multi_query_search(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/organization-search")
async def organization_search(request: OrganizationSearchRequest):
    try:
        return search_service.organization_search(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/policy-search")
async def policy_search(request: PolicySearchRequest):
    try:
        return search_service.policy_search(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/similar-documents")
async def find_similar_documents(request: SimilarDocumentRequest):
    try:
        return search_service.find_similar_documents(request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/suggestions/{partial_query}")
async def get_search_suggestions(partial_query: str):
    try:
        if not search_service.initialized:
            return []
        return search_service.search_engine.get_search_suggestions(partial_query)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats")
async def get_search_statistics():
    try:
        return search_service.search_engine.get_index_statistics()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "vector_search_service",
        "initialized": search_service.initialized
    }