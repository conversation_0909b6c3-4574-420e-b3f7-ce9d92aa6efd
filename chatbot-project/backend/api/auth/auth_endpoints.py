"""Authentication endpoints."""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from backend.utils.security import SecurityManager
from backend.utils.validation import UserValidator
from backend.models.user import User
import os

router = APIRouter(prefix="/auth", tags=["authentication"])
security_manager = SecurityManager(os.getenv("SECRET_KEY", "default-secret"))

class LoginRequest(BaseModel):
    username: str
    password: str

class RegisterRequest(BaseModel):
    username: str
    email: str
    password: str
    role: str = "employee"

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int = 3600

@router.post("/login", response_model=TokenResponse)
async def login(request: LoginRequest):
    """User login endpoint."""
    # In production, verify against database
    user = await User.get_by_username(request.username)
    if not user or not security_manager.verify_password(request.password, user.password_hash):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    token = security_manager.generate_token(user.id)
    return TokenResponse(access_token=token)

@router.post("/register", response_model=TokenResponse)
async def register(request: RegisterRequest):
    """User registration endpoint."""
    # Validate input
    try:
        UserValidator(**request.dict())
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # Check if user exists
    existing_user = await User.get_by_username(request.username)
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already exists")
    
    # Create user
    password_hash = security_manager.hash_password(request.password)
    user = await User.create(
        username=request.username,
        email=request.email,
        password_hash=password_hash,
        role=request.role
    )
    
    token = security_manager.generate_token(user.id)
    return TokenResponse(access_token=token)

@router.post("/verify")
async def verify_token(token: str):
    """Verify JWT token."""
    payload = security_manager.verify_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    user = await User.get_by_id(payload["user_id"])
    if not user:
        raise HTTPException(status_code=401, detail="User not found")
    
    return {"user_id": user.id, "username": user.username, "role": user.role}

@router.post("/refresh")
async def refresh_token(token: str):
    """Refresh JWT token."""
    payload = security_manager.verify_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    new_token = security_manager.generate_token(payload["user_id"])
    return TokenResponse(access_token=new_token)