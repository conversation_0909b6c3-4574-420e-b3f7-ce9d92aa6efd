"""Agent management endpoints."""

from fastapi import APIRouter, HTTPException, Head<PERSON>
from pydantic import BaseModel
from typing import List, Dict, Any
from backend.models.agent import Agent
from backend.services.agent_service import AgentService

router = APIRouter(prefix="/agent", tags=["agents"])
agent_service = AgentService()

class AgentStatus(BaseModel):
    agent_id: str
    status: str
    load: float
    last_activity: str

@router.get("/status", response_model=List[AgentStatus])
async def get_agent_status():
    """Get status of all agents."""
    agents = await agent_service.get_all_agents()
    return [
        AgentStatus(
            agent_id=agent.id,
            status=agent.status,
            load=agent.current_load,
            last_activity=agent.last_activity.isoformat()
        )
        for agent in agents
    ]

@router.get("/capabilities", response_model=Dict[str, List[str]])
async def get_agent_capabilities():
    """Get capabilities of all agents."""
    agents = await agent_service.get_all_agents()
    return {
        agent.id: agent.capabilities
        for agent in agents
    }

@router.post("/agents/{agent_id}/restart")
async def restart_agent(agent_id: str, x_user_id: str = Header(...)):
    """Restart a specific agent."""
    agent = await Agent.get_by_id(agent_id)
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    success = await agent_service.restart_agent(agent_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to restart agent")
    
    return {"message": f"Agent {agent_id} restarted successfully"}

@router.get("/agents/{agent_id}/metrics")
async def get_agent_metrics(agent_id: str):
    """Get metrics for a specific agent."""
    agent = await Agent.get_by_id(agent_id)
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    metrics = await agent_service.get_agent_metrics(agent_id)
    return metrics

@router.post("/agents/{agent_id}/configure")
async def configure_agent(agent_id: str, config: Dict[str, Any], x_user_id: str = Header(...)):
    """Configure agent parameters."""
    agent = await Agent.get_by_id(agent_id)
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    
    success = await agent_service.configure_agent(agent_id, config)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to configure agent")
    
    return {"message": f"Agent {agent_id} configured successfully"}

@router.get("/health")
async def agent_health_check():
    """Health check for agent system."""
    health_status = await agent_service.health_check()
    return health_status