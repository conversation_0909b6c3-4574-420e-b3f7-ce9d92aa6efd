"""Chat API endpoints."""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional
from backend.middleware.auth_middleware import get_current_user
from backend.services.ai_service import ai_service

router = APIRouter(prefix="/api/chat", tags=["chat"])

class ChatRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None

class ChatResponse(BaseModel):
    response: str
    confidence: float
    reasoning_steps: list
    success: bool

@router.post("/message", response_model=ChatResponse)
async def send_message(request: ChatRequest, current_user: dict = Depends(get_current_user)):
    """Send message to AI."""
    try:
        result = await ai_service.process_query(request.message, request.context)
        
        return ChatResponse(
            response=result.get("response", "No response generated"),
            confidence=result.get("confidence", 0.0),
            reasoning_steps=result.get("reasoning_steps", []),
            success=result.get("success", False)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tools")
async def get_tools(current_user: dict = Depends(get_current_user)):
    """Get available tools."""
    return {"tools": ai_service.get_available_tools()}

@router.post("/tools/{tool_name}")
async def execute_tool(tool_name: str, parameters: dict, current_user: dict = Depends(get_current_user)):
    """Execute a tool."""
    result = await ai_service.execute_tool(tool_name, parameters)
    return result