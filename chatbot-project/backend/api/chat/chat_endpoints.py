"""Chat endpoints."""

from fastapi import APIRouter, HTTPException, Head<PERSON>
from pydantic import BaseModel
from typing import List, Optional
from backend.utils.validation import MessageValidator
from backend.models.conversation import Conversation
from backend.models.message import Message
from backend.services.agentic_chat_service import AgenticChatService

router = APIRouter(prefix="/chat", tags=["chat"])
chat_service = AgenticChatService()

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    agent_used: str
    confidence: float

@router.post("/send", response_model=ChatResponse)
async def send_message(request: ChatRequest, x_user_id: str = Header(...)):
    """Send a chat message."""
    try:
        # Validate message
        MessageValidator(
            content=request.message,
            user_id=x_user_id,
            conversation_id=request.conversation_id
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # Get or create conversation
    if request.conversation_id:
        conversation = await Conversation.get_by_id(request.conversation_id)
        if not conversation or conversation.user_id != x_user_id:
            raise HTTPException(status_code=404, detail="Conversation not found")
    else:
        conversation = await Conversation.create(user_id=x_user_id)
    
    # Process message through agentic system
    response_data = await chat_service.process_message(
        message=request.message,
        user_id=x_user_id,
        conversation_id=conversation.id
    )
    
    # Save messages
    await Message.create(
        conversation_id=conversation.id,
        content=request.message,
        is_user=True
    )
    
    await Message.create(
        conversation_id=conversation.id,
        content=response_data["response"],
        is_user=False,
        agent_id=response_data["agent_id"]
    )
    
    return ChatResponse(
        response=response_data["response"],
        conversation_id=conversation.id,
        agent_used=response_data["agent_id"],
        confidence=response_data["confidence"]
    )

@router.get("/conversations", response_model=List[dict])
async def get_conversations(x_user_id: str = Header(...)):
    """Get user's conversations."""
    conversations = await Conversation.get_by_user_id(x_user_id)
    return [
        {
            "id": conv.id,
            "created_at": conv.created_at,
            "updated_at": conv.updated_at,
            "message_count": await conv.get_message_count()
        }
        for conv in conversations
    ]

@router.get("/conversations/{conversation_id}/messages", response_model=List[dict])
async def get_conversation_messages(conversation_id: str, x_user_id: str = Header(...)):
    """Get messages from a conversation."""
    conversation = await Conversation.get_by_id(conversation_id)
    if not conversation or conversation.user_id != x_user_id:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    messages = await Message.get_by_conversation_id(conversation_id)
    return [
        {
            "id": msg.id,
            "content": msg.content,
            "is_user": msg.is_user,
            "agent_id": msg.agent_id,
            "created_at": msg.created_at
        }
        for msg in messages
    ]

@router.delete("/conversations/{conversation_id}")
async def delete_conversation(conversation_id: str, x_user_id: str = Header(...)):
    """Delete a conversation."""
    conversation = await Conversation.get_by_id(conversation_id)
    if not conversation or conversation.user_id != x_user_id:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    await conversation.delete()
    return {"message": "Conversation deleted successfully"}