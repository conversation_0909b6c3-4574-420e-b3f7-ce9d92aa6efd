"""API Gateway for CHaBot system."""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import httpx
from typing import Dict, Any
import os

app = FastAPI(title="CHaBot API Gateway", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

security = HTTPBearer()

# Service URLs
SERVICES = {
    "auth": os.getenv("AUTH_SERVICE_URL", "http://localhost:8001"),
    "chat": os.getenv("CHAT_SERVICE_URL", "http://localhost:8002"),
    "agent": os.getenv("AGENT_SERVICE_URL", "http://localhost:8003"),
    "admin": os.getenv("ADMIN_SERVICE_URL", "http://localhost:8004"),
}

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify JWT token."""
    token = credentials.credentials
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{SERVICES['auth']}/verify",
            headers={"Authorization": f"Bearer {token}"}
        )
        if response.status_code != 200:
            raise HTTPException(status_code=401, detail="Invalid token")
        return response.json()

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "services": list(SERVICES.keys())}

@app.api_route("/auth/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def auth_proxy(path: str, request: Request):
    """Proxy requests to auth service."""
    async with httpx.AsyncClient() as client:
        response = await client.request(
            method=request.method,
            url=f"{SERVICES['auth']}/{path}",
            headers=dict(request.headers),
            content=await request.body()
        )
        return response.json()

@app.api_route("/chat/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def chat_proxy(path: str, request: Request, user: Dict = Depends(verify_token)):
    """Proxy requests to chat service."""
    async with httpx.AsyncClient() as client:
        headers = dict(request.headers)
        headers["X-User-ID"] = user["user_id"]
        
        response = await client.request(
            method=request.method,
            url=f"{SERVICES['chat']}/{path}",
            headers=headers,
            content=await request.body()
        )
        return response.json()

@app.api_route("/agent/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def agent_proxy(path: str, request: Request, user: Dict = Depends(verify_token)):
    """Proxy requests to agent service."""
    async with httpx.AsyncClient() as client:
        headers = dict(request.headers)
        headers["X-User-ID"] = user["user_id"]
        
        response = await client.request(
            method=request.method,
            url=f"{SERVICES['agent']}/{path}",
            headers=headers,
            content=await request.body()
        )
        return response.json()

@app.api_route("/admin/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def admin_proxy(path: str, request: Request, user: Dict = Depends(verify_token)):
    """Proxy requests to admin service."""
    if user.get("role") not in ["admin", "hr_admin"]:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    async with httpx.AsyncClient() as client:
        headers = dict(request.headers)
        headers["X-User-ID"] = user["user_id"]
        
        response = await client.request(
            method=request.method,
            url=f"{SERVICES['admin']}/{path}",
            headers=headers,
            content=await request.body()
        )
        return response.json()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)