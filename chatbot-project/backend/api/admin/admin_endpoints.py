"""Admin endpoints."""

from fastapi import APIRouter, HTTPException, <PERSON><PERSON>
from pydantic import BaseModel
from typing import List, Dict, Any
from backend.models.user import User
from backend.models.conversation import Conversation
from backend.services.admin_service import AdminService

router = APIRouter(prefix="/admin", tags=["admin"])
admin_service = AdminService()

class UserStats(BaseModel):
    total_users: int
    active_users: int
    new_users_today: int

class SystemStats(BaseModel):
    total_conversations: int
    total_messages: int
    active_agents: int
    system_uptime: str

@router.get("/stats/users", response_model=UserStats)
async def get_user_stats(x_user_id: str = Header(...)):
    """Get user statistics."""
    stats = await admin_service.get_user_stats()
    return UserStats(**stats)

@router.get("/stats/system", response_model=SystemStats)
async def get_system_stats(x_user_id: str = Header(...)):
    """Get system statistics."""
    stats = await admin_service.get_system_stats()
    return SystemStats(**stats)

@router.get("/users", response_model=List[dict])
async def get_all_users(x_user_id: str = Header(...)):
    """Get all users."""
    users = await User.get_all()
    return [
        {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role,
            "created_at": user.created_at,
            "last_login": user.last_login
        }
        for user in users
    ]

@router.put("/users/{user_id}/role")
async def update_user_role(user_id: str, role: str, x_user_id: str = Header(...)):
    """Update user role."""
    valid_roles = ["employee", "manager", "hr_admin", "admin"]
    if role not in valid_roles:
        raise HTTPException(status_code=400, detail=f"Invalid role. Must be one of: {valid_roles}")
    
    user = await User.get_by_id(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    await user.update_role(role)
    return {"message": f"User role updated to {role}"}

@router.delete("/users/{user_id}")
async def delete_user(user_id: str, x_user_id: str = Header(...)):
    """Delete a user."""
    if user_id == x_user_id:
        raise HTTPException(status_code=400, detail="Cannot delete yourself")
    
    user = await User.get_by_id(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    await user.delete()
    return {"message": "User deleted successfully"}

@router.get("/conversations/recent", response_model=List[dict])
async def get_recent_conversations(limit: int = 50, x_user_id: str = Header(...)):
    """Get recent conversations."""
    conversations = await Conversation.get_recent(limit)
    return [
        {
            "id": conv.id,
            "user_id": conv.user_id,
            "created_at": conv.created_at,
            "message_count": await conv.get_message_count()
        }
        for conv in conversations
    ]

@router.post("/system/maintenance")
async def toggle_maintenance_mode(enabled: bool, x_user_id: str = Header(...)):
    """Toggle maintenance mode."""
    await admin_service.set_maintenance_mode(enabled)
    status = "enabled" if enabled else "disabled"
    return {"message": f"Maintenance mode {status}"}

@router.get("/logs")
async def get_system_logs(level: str = "INFO", limit: int = 100, x_user_id: str = Header(...)):
    """Get system logs."""
    logs = await admin_service.get_logs(level, limit)
    return {"logs": logs}