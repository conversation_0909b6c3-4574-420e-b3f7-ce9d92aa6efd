"""
Conversation model definitions.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class ConversationBase(BaseModel):
    user_id: int
    title: Optional[str] = None


class ConversationCreate(ConversationBase):
    pass


class Conversation(ConversationBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
