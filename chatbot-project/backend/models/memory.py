"""Memory model for agent memory systems."""

from sqlalchemy import Column, String, Text, DateTime, Float, JSON, Boolean
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class Memory(Base):
    __tablename__ = 'memories'
    
    id = Column(String, primary_key=True)
    agent_id = Column(String, nullable=False)
    memory_type = Column(String, nullable=False)  # conversation, episodic, semantic, working
    content = Column(Text, nullable=False)
    context = Column(JSON, default={})
    importance = Column(Float, default=0.5)
    access_count = Column(Integer, default=0)
    last_accessed = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    
    @classmethod
    async def create(cls, **kwargs):
        memory = cls(**kwargs)
        # In production, save to database
        return memory
    
    @classmethod
    async def get_by_agent(cls, agent_id: str, memory_type: str = None):
        # In production, query database
        return []