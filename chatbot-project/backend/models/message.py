"""
Message model definitions.
"""

from datetime import datetime
from typing import Dict, Any
from pydantic import BaseModel
from enum import Enum


class MessageType(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"


class MessageBase(BaseModel):
    conversation_id: int
    user_id: int
    content: str
    message_type: MessageType
    metadata: Dict[str, Any] = {}


class MessageCreate(MessageBase):
    pass


class Message(MessageBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
