"""
Agent model definitions.
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from enum import Enum


class AgentType(str, Enum):
    COORDINATOR = "coordinator"
    ORGANIZATION = "organization"
    DEPARTMENT = "department"
    REASONING = "reasoning"
    TOOL = "tool"
    CRITIC = "critic"


class AgentStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"


class AgentBase(BaseModel):
    agent_id: str
    agent_type: AgentType
    name: str
    description: Optional[str] = None
    capabilities: List[str] = []
    configuration: Dict[str, Any] = {}


class AgentCreate(AgentBase):
    pass


class AgentResponse(AgentBase):
    id: int
    status: AgentStatus
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Agent(AgentResponse):
    pass
