"""
Organization model definitions.
"""

from datetime import datetime
from typing import Dict, Any, Optional
from pydantic import BaseModel


class OrganizationBase(BaseModel):
    name: str
    description: Optional[str] = None
    settings: Dict[str, Any] = {}


class OrganizationCreate(OrganizationBase):
    pass


class Organization(OrganizationBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
