"""
Department model definitions.
"""

from datetime import datetime
from typing import Dict, Any, Optional
from pydantic import BaseModel


class DepartmentBase(BaseModel):
    name: str
    organization_id: int
    description: Optional[str] = None
    settings: Dict[str, Any] = {}


class DepartmentCreate(DepartmentBase):
    pass


class Department(DepartmentBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
