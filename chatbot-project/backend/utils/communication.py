"""Agent communication utilities."""

import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import aioredis

class MessageBroker:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis = None
        self.subscribers = {}
    
    async def connect(self):
        """Connect to Red<PERSON>."""
        self.redis = await aioredis.from_url(self.redis_url)
    
    async def publish(self, channel: str, message: Dict[str, Any]):
        """Publish a message to a channel."""
        if not self.redis:
            await self.connect()
        
        message_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'data': message
        }
        
        await self.redis.publish(channel, json.dumps(message_data))
    
    async def subscribe(self, channel: str, callback):
        """Subscribe to a channel."""
        if not self.redis:
            await self.connect()
        
        pubsub = self.redis.pubsub()
        await pubsub.subscribe(channel)
        
        self.subscribers[channel] = pubsub
        
        async for message in pubsub.listen():
            if message['type'] == 'message':
                data = json.loads(message['data'])
                await callback(data)
    
    async def send_agent_message(self, from_agent: str, to_agent: str, 
                                message_type: str, content: Dict[str, Any]):
        """Send a message between agents."""
        message = {
            'from': from_agent,
            'to': to_agent,
            'type': message_type,
            'content': content,
            'id': f"{from_agent}_{to_agent}_{datetime.utcnow().timestamp()}"
        }
        
        await self.publish(f"agent_{to_agent}", message)
        return message['id']

class AgentCommunicationProtocol:
    def __init__(self, agent_id: str, broker: MessageBroker):
        self.agent_id = agent_id
        self.broker = broker
        self.message_handlers = {}
    
    def register_handler(self, message_type: str, handler):
        """Register a message handler."""
        self.message_handlers[message_type] = handler
    
    async def start_listening(self):
        """Start listening for messages."""
        await self.broker.subscribe(f"agent_{self.agent_id}", self._handle_message)
    
    async def _handle_message(self, message_data: Dict[str, Any]):
        """Handle incoming messages."""
        message = message_data['data']
        message_type = message.get('type')
        
        if message_type in self.message_handlers:
            await self.message_handlers[message_type](message)
    
    async def send_request(self, to_agent: str, request_type: str, data: Dict[str, Any]):
        """Send a request to another agent."""
        return await self.broker.send_agent_message(
            self.agent_id, to_agent, request_type, data
        )
    
    async def send_response(self, to_agent: str, request_id: str, data: Dict[str, Any]):
        """Send a response to another agent."""
        return await self.broker.send_agent_message(
            self.agent_id, to_agent, 'response', 
            {'request_id': request_id, 'data': data}
        )