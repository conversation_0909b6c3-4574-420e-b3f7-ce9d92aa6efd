"""Input validation utilities for the CHaBot system."""

import re
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, validator

class MessageValidator(BaseModel):
    content: str
    user_id: str
    conversation_id: Optional[str] = None
    
    @validator('content')
    def validate_content(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Message content cannot be empty')
        if len(v) > 4000:
            raise ValueError('Message content too long')
        return v.strip()
    
    @validator('user_id')
    def validate_user_id(cls, v):
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Invalid user ID format')
        return v

class UserValidator(BaseModel):
    username: str
    email: str
    role: str
    
    @validator('username')
    def validate_username(cls, v):
        if not re.match(r'^[a-zA-Z0-9_]{3,20}$', v):
            raise ValueError('Username must be 3-20 characters, alphanumeric and underscore only')
        return v
    
    @validator('email')
    def validate_email(cls, v):
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        return v
    
    @validator('role')
    def validate_role(cls, v):
        valid_roles = ['employee', 'manager', 'hr_admin', 'admin']
        if v not in valid_roles:
            raise ValueError(f'Role must be one of: {valid_roles}')
        return v

def sanitize_input(text: str) -> str:
    """Sanitize user input."""
    # Remove potentially dangerous characters
    text = re.sub(r'[<>"\']', '', text)
    # Limit length
    return text[:1000].strip()

def validate_query_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """Validate query parameters."""
    validated = {}
    
    if 'limit' in params:
        limit = int(params['limit'])
        validated['limit'] = min(max(limit, 1), 100)
    
    if 'offset' in params:
        validated['offset'] = max(int(params['offset']), 0)
    
    if 'search' in params:
        validated['search'] = sanitize_input(params['search'])
    
    return validated