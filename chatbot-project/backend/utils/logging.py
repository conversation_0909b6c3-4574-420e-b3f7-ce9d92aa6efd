"""Logging utilities for the CHaBot system."""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

class StructuredLogger:
    def __init__(self, name: str, log_level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        file_handler = logging.FileHandler(log_dir / f"{name}.log")
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def log_structured(self, level: str, message: str, **kwargs):
        """Log structured data."""
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'message': message,
            **kwargs
        }
        getattr(self.logger, level.lower())(json.dumps(log_data))
    
    def log_agent_action(self, agent_id: str, action: str, details: Dict[str, Any]):
        """Log agent actions."""
        self.log_structured('info', 'Agent Action', 
                          agent_id=agent_id, action=action, details=details)
    
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """Log errors with context."""
        self.log_structured('error', str(error), 
                          error_type=type(error).__name__, context=context or {})
    
    def log_performance(self, operation: str, duration: float, **metrics):
        """Log performance metrics."""
        self.log_structured('info', 'Performance Metric',
                          operation=operation, duration=duration, **metrics)