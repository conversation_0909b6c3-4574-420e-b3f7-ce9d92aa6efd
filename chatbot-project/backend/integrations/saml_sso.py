"""
SAML SSO Integration for CHaBot Enterprise Authentication.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import xml.etree.ElementTree as ET
from urllib.parse import urlparse, parse_qs
import base64
import zlib
import secrets
import hashlib
from datetime import datetime, timedelta
import jwt
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.x509 import load_pem_x509_certificate
import requests
from fastapi import FastAPI, Request, HTTPException, Response
from fastapi.responses import RedirectResponse, HTMLResponse


class SAMLBinding(Enum):
    HTTP_POST = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    HTTP_REDIRECT = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"


class SAMLNameIDFormat(Enum):
    EMAIL = "urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"
    PERSISTENT = "urn:oasis:names:tc:SAML:2.0:nameid-format:persistent"
    TRANSIENT = "urn:oasis:names:tc:SAML:2.0:nameid-format:transient"


@dataclass
class SAMLUser:
    name_id: str
    email: str
    first_name: str
    last_name: str
    full_name: str
    department: str
    organization: str
    groups: List[str]
    roles: List[str]
    employee_id: Optional[str]
    manager: Optional[str]
    attributes: Dict[str, Any]
    session_index: str
    assertion_id: str
    issued_at: datetime
    expires_at: datetime


@dataclass
class SAMLConfig:
    # Service Provider (SP) Configuration
    sp_entity_id: str
    sp_acs_url: str  # Assertion Consumer Service URL
    sp_sls_url: str  # Single Logout Service URL
    sp_private_key: str
    sp_certificate: str
    
    # Identity Provider (IdP) Configuration
    idp_entity_id: str
    idp_sso_url: str  # Single Sign-On URL
    idp_sls_url: str  # Single Logout URL
    idp_certificate: str
    idp_metadata_url: Optional[str]
    
    # SAML Settings
    name_id_format: SAMLNameIDFormat
    binding: SAMLBinding
    sign_requests: bool
    encrypt_assertions: bool
    
    # Attribute Mappings
    attribute_mappings: Dict[str, str]


class SAMLSSOProvider:
    """SAML SSO provider for enterprise authentication."""
    
    def __init__(self, config: SAMLConfig):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Load certificates and keys
        self._load_certificates()
        
        # Active sessions
        self.active_sessions = {}
        
        # SAML request cache
        self.request_cache = {}
        
        # Initialize metadata
        self.sp_metadata = self._generate_sp_metadata()
    
    def _load_certificates(self):
        """Load SP and IdP certificates."""
        try:
            # Load SP private key
            self.sp_private_key = serialization.load_pem_private_key(
                self.config.sp_private_key.encode(),
                password=None
            )
            
            # Load SP certificate
            self.sp_certificate = load_pem_x509_certificate(
                self.config.sp_certificate.encode()
            )
            
            # Load IdP certificate
            self.idp_certificate = load_pem_x509_certificate(
                self.config.idp_certificate.encode()
            )
            
            self.logger.info("SAML certificates loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load SAML certificates: {e}")
            raise
    
    def _generate_sp_metadata(self) -> str:
        """Generate Service Provider metadata XML."""
        metadata_template = f"""<?xml version="1.0" encoding="UTF-8"?>
<md:EntityDescriptor xmlns:md="urn:oasis:names:tc:SAML:2.0:metadata"
                     entityID="{self.config.sp_entity_id}">
    <md:SPSSODescriptor AuthnRequestsSigned="{str(self.config.sign_requests).lower()}"
                        WantAssertionsSigned="true"
                        protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        
        <md:KeyDescriptor use="signing">
            <ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
                <ds:X509Data>
                    <ds:X509Certificate>{self._get_certificate_data(self.sp_certificate)}</ds:X509Certificate>
                </ds:X509Data>
            </ds:KeyInfo>
        </md:KeyDescriptor>
        
        <md:KeyDescriptor use="encryption">
            <ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
                <ds:X509Data>
                    <ds:X509Certificate>{self._get_certificate_data(self.sp_certificate)}</ds:X509Certificate>
                </ds:X509Data>
            </ds:KeyInfo>
        </md:KeyDescriptor>
        
        <md:NameIDFormat>{self.config.name_id_format.value}</md:NameIDFormat>
        
        <md:AssertionConsumerService Binding="{self.config.binding.value}"
                                   Location="{self.config.sp_acs_url}"
                                   index="1" isDefault="true"/>
        
        <md:SingleLogoutService Binding="{self.config.binding.value}"
                              Location="{self.config.sp_sls_url}"/>
    </md:SPSSODescriptor>
</md:EntityDescriptor>"""
        
        return metadata_template
    
    def _get_certificate_data(self, certificate) -> str:
        """Extract certificate data without headers."""
        cert_pem = certificate.public_bytes(serialization.Encoding.PEM).decode()
        cert_data = cert_pem.replace('-----BEGIN CERTIFICATE-----\n', '')
        cert_data = cert_data.replace('\n-----END CERTIFICATE-----', '')
        cert_data = cert_data.replace('\n', '')
        return cert_data
    
    async def initiate_sso(self, relay_state: Optional[str] = None) -> Tuple[str, str]:
        """Initiate SAML SSO authentication."""
        try:
            # Generate request ID
            request_id = f"_{secrets.token_hex(16)}"
            
            # Create SAML AuthnRequest
            authn_request = self._create_authn_request(request_id)
            
            # Store request in cache
            self.request_cache[request_id] = {
                'timestamp': datetime.now(),
                'relay_state': relay_state
            }
            
            # Encode and sign request if required
            if self.config.binding == SAMLBinding.HTTP_REDIRECT:
                encoded_request = self._encode_redirect_request(authn_request)
                sso_url = f"{self.config.idp_sso_url}?SAMLRequest={encoded_request}"
                if relay_state:
                    sso_url += f"&RelayState={relay_state}"
                
                return sso_url, request_id
            
            else:  # HTTP_POST
                encoded_request = base64.b64encode(authn_request.encode()).decode()
                return self._create_post_form(encoded_request, relay_state), request_id
                
        except Exception as e:
            self.logger.error(f"Failed to initiate SSO: {e}")
            raise
    
    def _create_authn_request(self, request_id: str) -> str:
        """Create SAML AuthnRequest XML."""
        issue_instant = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
        
        authn_request = f"""<?xml version="1.0" encoding="UTF-8"?>
<samlp:AuthnRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
                    xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
                    ID="{request_id}"
                    Version="2.0"
                    IssueInstant="{issue_instant}"
                    Destination="{self.config.idp_sso_url}"
                    AssertionConsumerServiceURL="{self.config.sp_acs_url}"
                    ProtocolBinding="{self.config.binding.value}">
    
    <saml:Issuer>{self.config.sp_entity_id}</saml:Issuer>
    
    <samlp:NameIDPolicy Format="{self.config.name_id_format.value}"
                        AllowCreate="true"/>
    
    <samlp:RequestedAuthnContext Comparison="exact">
        <saml:AuthnContextClassRef>urn:oasis:names:tc:SAML:2.0:ac:classes:PasswordProtectedTransport</saml:AuthnContextClassRef>
    </samlp:RequestedAuthnContext>
</samlp:AuthnRequest>"""
        
        if self.config.sign_requests:
            return self._sign_xml(authn_request)
        
        return authn_request
    
    def _encode_redirect_request(self, request: str) -> str:
        """Encode SAML request for HTTP-Redirect binding."""
        # Deflate and base64 encode
        compressed = zlib.compress(request.encode())[2:-4]  # Remove zlib headers
        encoded = base64.b64encode(compressed).decode()
        return encoded
    
    def _create_post_form(self, encoded_request: str, relay_state: Optional[str]) -> str:
        """Create HTML form for HTTP-POST binding."""
        relay_state_input = f'<input type="hidden" name="RelayState" value="{relay_state}"/>' if relay_state else ''
        
        form_html = f"""<!DOCTYPE html>
<html>
<head>
    <title>SAML SSO</title>
</head>
<body onload="document.forms[0].submit()">
    <form method="post" action="{self.config.idp_sso_url}">
        <input type="hidden" name="SAMLRequest" value="{encoded_request}"/>
        {relay_state_input}
        <noscript>
            <input type="submit" value="Continue"/>
        </noscript>
    </form>
</body>
</html>"""
        
        return form_html
    
    async def process_saml_response(self, saml_response: str, relay_state: Optional[str] = None) -> SAMLUser:
        """Process SAML response from IdP."""
        try:
            # Decode SAML response
            decoded_response = base64.b64decode(saml_response).decode()
            
            # Parse XML
            root = ET.fromstring(decoded_response)
            
            # Verify signature
            if not self._verify_response_signature(root):
                raise ValueError("Invalid SAML response signature")
            
            # Extract assertion
            assertion = self._extract_assertion(root)
            if not assertion:
                raise ValueError("No assertion found in SAML response")
            
            # Verify assertion
            if not self._verify_assertion(assertion):
                raise ValueError("Invalid assertion")
            
            # Extract user information
            user = self._extract_user_info(assertion)
            
            # Store session
            session_id = secrets.token_hex(32)
            self.active_sessions[session_id] = {
                'user': user,
                'created_at': datetime.now(),
                'last_activity': datetime.now()
            }
            
            user.session_index = session_id
            
            self.logger.info(f"SAML authentication successful for user: {user.email}")
            return user
            
        except Exception as e:
            self.logger.error(f"Failed to process SAML response: {e}")
            raise
    
    def _verify_response_signature(self, response_root) -> bool:
        """Verify SAML response signature."""
        try:
            # Find signature element
            signature_elem = response_root.find('.//{http://www.w3.org/2000/09/xmldsig#}Signature')
            if signature_elem is None:
                return not self.config.sign_requests  # If we don't require signing
            
            # Extract signature value
            signature_value = signature_elem.find('.//{http://www.w3.org/2000/09/xmldsig#}SignatureValue').text
            signature_bytes = base64.b64decode(signature_value)
            
            # Get signed info
            signed_info = signature_elem.find('.//{http://www.w3.org/2000/09/xmldsig#}SignedInfo')
            signed_info_str = ET.tostring(signed_info, encoding='unicode')
            
            # Verify signature using IdP certificate
            try:
                self.idp_certificate.public_key().verify(
                    signature_bytes,
                    signed_info_str.encode(),
                    padding.PKCS1v15(),
                    hashes.SHA256()
                )
                return True
            except Exception:
                return False
                
        except Exception as e:
            self.logger.error(f"Error verifying response signature: {e}")
            return False
    
    def _extract_assertion(self, response_root):
        """Extract assertion from SAML response."""
        return response_root.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}Assertion')
    
    def _verify_assertion(self, assertion) -> bool:
        """Verify SAML assertion."""
        try:
            # Check assertion validity period
            conditions = assertion.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}Conditions')
            if conditions is not None:
                not_before = conditions.get('NotBefore')
                not_on_or_after = conditions.get('NotOnOrAfter')
                
                now = datetime.utcnow()
                
                if not_before:
                    not_before_dt = datetime.fromisoformat(not_before.replace('Z', '+00:00'))
                    if now < not_before_dt.replace(tzinfo=None):
                        return False
                
                if not_on_or_after:
                    not_on_or_after_dt = datetime.fromisoformat(not_on_or_after.replace('Z', '+00:00'))
                    if now >= not_on_or_after_dt.replace(tzinfo=None):
                        return False
            
            # Check audience restriction
            audience_restriction = assertion.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}AudienceRestriction')
            if audience_restriction is not None:
                audience = audience_restriction.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}Audience')
                if audience is not None and audience.text != self.config.sp_entity_id:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error verifying assertion: {e}")
            return False
    
    def _extract_user_info(self, assertion) -> SAMLUser:
        """Extract user information from SAML assertion."""
        try:
            # Extract NameID
            name_id_elem = assertion.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}NameID')
            name_id = name_id_elem.text if name_id_elem is not None else ""
            
            # Extract attributes
            attributes = {}
            attribute_statements = assertion.findall('.//{urn:oasis:names:tc:SAML:2.0:assertion}AttributeStatement')
            
            for attr_statement in attribute_statements:
                for attr in attr_statement.findall('.//{urn:oasis:names:tc:SAML:2.0:assertion}Attribute'):
                    attr_name = attr.get('Name')
                    attr_values = []
                    
                    for attr_value in attr.findall('.//{urn:oasis:names:tc:SAML:2.0:assertion}AttributeValue'):
                        if attr_value.text:
                            attr_values.append(attr_value.text)
                    
                    if attr_values:
                        attributes[attr_name] = attr_values[0] if len(attr_values) == 1 else attr_values
            
            # Map attributes to user fields
            mappings = self.config.attribute_mappings
            
            # Extract assertion metadata
            assertion_id = assertion.get('ID', '')
            issue_instant = assertion.get('IssueInstant', '')
            
            # Parse issue instant
            issued_at = datetime.utcnow()
            if issue_instant:
                try:
                    issued_at = datetime.fromisoformat(issue_instant.replace('Z', '+00:00')).replace(tzinfo=None)
                except:
                    pass
            
            # Set expiration (default 1 hour)
            expires_at = issued_at + timedelta(hours=1)
            
            # Create user object
            user = SAMLUser(
                name_id=name_id,
                email=attributes.get(mappings.get('email', 'email'), name_id),
                first_name=attributes.get(mappings.get('first_name', 'first_name'), ''),
                last_name=attributes.get(mappings.get('last_name', 'last_name'), ''),
                full_name=attributes.get(mappings.get('full_name', 'full_name'), ''),
                department=attributes.get(mappings.get('department', 'department'), ''),
                organization=attributes.get(mappings.get('organization', 'organization'), ''),
                groups=self._extract_list_attribute(attributes, mappings.get('groups', 'groups')),
                roles=self._extract_list_attribute(attributes, mappings.get('roles', 'roles')),
                employee_id=attributes.get(mappings.get('employee_id', 'employee_id')),
                manager=attributes.get(mappings.get('manager', 'manager')),
                attributes=attributes,
                session_index='',  # Will be set later
                assertion_id=assertion_id,
                issued_at=issued_at,
                expires_at=expires_at
            )
            
            return user
            
        except Exception as e:
            self.logger.error(f"Error extracting user info: {e}")
            raise
    
    def _extract_list_attribute(self, attributes: Dict[str, Any], attr_name: str) -> List[str]:
        """Extract list attribute from SAML attributes."""
        attr_value = attributes.get(attr_name, [])
        if isinstance(attr_value, str):
            # Split by common delimiters
            return [item.strip() for item in attr_value.replace(';', ',').split(',') if item.strip()]
        elif isinstance(attr_value, list):
            return attr_value
        return []
    
    async def initiate_slo(self, session_id: str, relay_state: Optional[str] = None) -> str:
        """Initiate SAML Single Logout."""
        try:
            if session_id not in self.active_sessions:
                raise ValueError("Invalid session")
            
            user = self.active_sessions[session_id]['user']
            
            # Generate logout request
            request_id = f"_{secrets.token_hex(16)}"
            logout_request = self._create_logout_request(request_id, user)
            
            # Remove session
            del self.active_sessions[session_id]
            
            # Encode request
            if self.config.binding == SAMLBinding.HTTP_REDIRECT:
                encoded_request = self._encode_redirect_request(logout_request)
                slo_url = f"{self.config.idp_sls_url}?SAMLRequest={encoded_request}"
                if relay_state:
                    slo_url += f"&RelayState={relay_state}"
                return slo_url
            else:
                encoded_request = base64.b64encode(logout_request.encode()).decode()
                return self._create_logout_post_form(encoded_request, relay_state)
                
        except Exception as e:
            self.logger.error(f"Failed to initiate SLO: {e}")
            raise
    
    def _create_logout_request(self, request_id: str, user: SAMLUser) -> str:
        """Create SAML LogoutRequest XML."""
        issue_instant = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
        
        logout_request = f"""<?xml version="1.0" encoding="UTF-8"?>
<samlp:LogoutRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
                     xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
                     ID="{request_id}"
                     Version="2.0"
                     IssueInstant="{issue_instant}"
                     Destination="{self.config.idp_sls_url}">
    
    <saml:Issuer>{self.config.sp_entity_id}</saml:Issuer>
    
    <saml:NameID Format="{self.config.name_id_format.value}">{user.name_id}</saml:NameID>
    
    <samlp:SessionIndex>{user.session_index}</samlp:SessionIndex>
</samlp:LogoutRequest>"""
        
        if self.config.sign_requests:
            return self._sign_xml(logout_request)
        
        return logout_request
    
    def _create_logout_post_form(self, encoded_request: str, relay_state: Optional[str]) -> str:
        """Create HTML form for logout HTTP-POST binding."""
        relay_state_input = f'<input type="hidden" name="RelayState" value="{relay_state}"/>' if relay_state else ''
        
        form_html = f"""<!DOCTYPE html>
<html>
<head>
    <title>SAML SLO</title>
</head>
<body onload="document.forms[0].submit()">
    <form method="post" action="{self.config.idp_sls_url}">
        <input type="hidden" name="SAMLRequest" value="{encoded_request}"/>
        {relay_state_input}
        <noscript>
            <input type="submit" value="Continue"/>
        </noscript>
    </form>
</body>
</html>"""
        
        return form_html
    
    def _sign_xml(self, xml_content: str) -> str:
        """Sign XML content (simplified implementation)."""
        # This is a simplified implementation
        # In production, use a proper XML signing library like xmlsec
        return xml_content
    
    async def get_idp_metadata(self) -> Dict[str, Any]:
        """Fetch IdP metadata."""
        if not self.config.idp_metadata_url:
            return {}
        
        try:
            response = requests.get(self.config.idp_metadata_url, timeout=30)
            response.raise_for_status()
            
            # Parse metadata XML
            root = ET.fromstring(response.content)
            
            # Extract relevant information
            metadata = {
                'entity_id': root.get('entityID'),
                'sso_url': '',
                'sls_url': '',
                'certificate': ''
            }
            
            # Find SSO URL
            sso_service = root.find('.//{urn:oasis:names:tc:SAML:2.0:metadata}SingleSignOnService')
            if sso_service is not None:
                metadata['sso_url'] = sso_service.get('Location')
            
            # Find SLS URL
            sls_service = root.find('.//{urn:oasis:names:tc:SAML:2.0:metadata}SingleLogoutService')
            if sls_service is not None:
                metadata['sls_url'] = sls_service.get('Location')
            
            # Find certificate
            cert_elem = root.find('.//{http://www.w3.org/2000/09/xmldsig#}X509Certificate')
            if cert_elem is not None:
                metadata['certificate'] = cert_elem.text
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"Failed to fetch IdP metadata: {e}")
            return {}
    
    def get_sp_metadata(self) -> str:
        """Get Service Provider metadata."""
        return self.sp_metadata
    
    def validate_session(self, session_id: str) -> Optional[SAMLUser]:
        """Validate and return user session."""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        user = session['user']
        
        # Check if session is expired
        if datetime.now() > user.expires_at:
            del self.active_sessions[session_id]
            return None
        
        # Update last activity
        session['last_activity'] = datetime.now()
        
        return user
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions."""
        now = datetime.now()
        expired_sessions = [
            session_id for session_id, session in self.active_sessions.items()
            if now > session['user'].expires_at
        ]
        
        for session_id in expired_sessions:
            del self.active_sessions[session_id]
        
        if expired_sessions:
            self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")


# FastAPI integration
def create_saml_routes(saml_provider: SAMLSSOProvider) -> FastAPI:
    """Create FastAPI routes for SAML SSO."""
    app = FastAPI()
    
    @app.get("/saml/metadata")
    async def get_metadata():
        """Get SP metadata."""
        return Response(
            content=saml_provider.get_sp_metadata(),
            media_type="application/xml"
        )
    
    @app.get("/saml/login")
    async def initiate_login(relay_state: Optional[str] = None):
        """Initiate SAML login."""
        try:
            sso_url, request_id = await saml_provider.initiate_sso(relay_state)
            
            if saml_provider.config.binding == SAMLBinding.HTTP_REDIRECT:
                return RedirectResponse(url=sso_url)
            else:
                return HTMLResponse(content=sso_url)
                
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/saml/acs")
    async def assertion_consumer_service(request: Request):
        """SAML Assertion Consumer Service."""
        try:
            form_data = await request.form()
            saml_response = form_data.get("SAMLResponse")
            relay_state = form_data.get("RelayState")
            
            if not saml_response:
                raise HTTPException(status_code=400, detail="Missing SAMLResponse")
            
            user = await saml_provider.process_saml_response(saml_response, relay_state)
            
            # Create JWT token for the user
            token_payload = {
                'user_id': user.email,
                'name': user.full_name,
                'email': user.email,
                'department': user.department,
                'organization': user.organization,
                'groups': user.groups,
                'roles': user.roles,
                'session_id': user.session_index,
                'exp': user.expires_at.timestamp(),
                'iat': user.issued_at.timestamp()
            }
            
            # Sign JWT token (use your JWT secret)
            jwt_token = jwt.encode(token_payload, "your-jwt-secret", algorithm="HS256")
            
            # Redirect to application with token
            redirect_url = relay_state or "/"
            response = RedirectResponse(url=redirect_url)
            response.set_cookie("auth_token", jwt_token, httponly=True, secure=True)
            
            return response
            
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    @app.get("/saml/logout")
    async def initiate_logout(session_id: str, relay_state: Optional[str] = None):
        """Initiate SAML logout."""
        try:
            slo_url = await saml_provider.initiate_slo(session_id, relay_state)
            
            if saml_provider.config.binding == SAMLBinding.HTTP_REDIRECT:
                return RedirectResponse(url=slo_url)
            else:
                return HTMLResponse(content=slo_url)
                
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    return app


# Configuration example
SAML_CONFIG_EXAMPLE = SAMLConfig(
    # Service Provider Configuration
    sp_entity_id="https://chabot.company.com/saml/metadata",
    sp_acs_url="https://chabot.company.com/saml/acs",
    sp_sls_url="https://chabot.company.com/saml/sls",
    sp_private_key="""-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
-----END PRIVATE KEY-----""",
    sp_certificate="""-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/OvD/XqiMA0GCSqGSIb3DQEBCwUA...
-----END CERTIFICATE-----""",
    
    # Identity Provider Configuration
    idp_entity_id="https://idp.company.com/saml/metadata",
    idp_sso_url="https://idp.company.com/saml/sso",
    idp_sls_url="https://idp.company.com/saml/sls",
    idp_certificate="""-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/OvD/XqiMA0GCSqGSIb3DQEBCwUA...
-----END CERTIFICATE-----""",
    idp_metadata_url="https://idp.company.com/saml/metadata",
    
    # SAML Settings
    name_id_format=SAMLNameIDFormat.EMAIL,
    binding=SAMLBinding.HTTP_POST,
    sign_requests=True,
    encrypt_assertions=False,
    
    # Attribute Mappings
    attribute_mappings={
        'email': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
        'first_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
        'last_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname',
        'full_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name',
        'department': 'http://schemas.company.com/ws/2005/05/identity/claims/department',
        'organization': 'http://schemas.company.com/ws/2005/05/identity/claims/organization',
        'groups': 'http://schemas.company.com/ws/2005/05/identity/claims/groups',
        'roles': 'http://schemas.microsoft.com/ws/2008/06/identity/claims/role',
        'employee_id': 'http://schemas.company.com/ws/2005/05/identity/claims/employeeid',
        'manager': 'http://schemas.company.com/ws/2005/05/identity/claims/manager'
    }
)


if __name__ == "__main__":
    # Example usage
    saml_provider = SAMLSSOProvider(SAML_CONFIG_EXAMPLE)
    
    # Create FastAPI app with SAML routes
    app = create_saml_routes(saml_provider)
    
    # Run with: uvicorn saml_sso:app --host 0.0.0.0 --port 8000