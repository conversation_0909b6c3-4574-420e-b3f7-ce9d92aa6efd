"""
Real-time Communication Integration for CHaBot System.
Provides WebSocket, Server-Sent Events, and Push Notifications.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Set, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import uuid
import redis
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, HTTPException
from fastapi.responses import StreamingResponse
import websockets
from pydantic import BaseModel
import aioredis
from contextlib import asynccontextmanager
import ssl
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import requests
from twilio.rest import Client as TwilioClient


class MessageType(Enum):
    CHAT_MESSAGE = "chat_message"
    AGENT_STATUS = "agent_status"
    AGENT_COLLABORATION = "agent_collaboration"
    SYSTEM_ALERT = "system_alert"
    USER_TYPING = "user_typing"
    CONVERSATION_UPDATE = "conversation_update"
    REASONING_UPDATE = "reasoning_update"
    NOTIFICATION = "notification"


class NotificationType(Enum):
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    SLACK = "slack"
    WEBHOOK = "webhook"


@dataclass
class RealtimeMessage:
    id: str
    type: MessageType
    sender_id: str
    recipient_id: Optional[str]
    channel: str
    content: Dict[str, Any]
    timestamp: datetime
    metadata: Dict[str, Any]


@dataclass
class NotificationMessage:
    id: str
    type: NotificationType
    recipient: str
    subject: str
    content: str
    priority: str
    metadata: Dict[str, Any]
    scheduled_at: Optional[datetime]
    sent_at: Optional[datetime]


class ConnectionManager:
    """Manages WebSocket connections and message routing."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[str, Set[str]] = {}
        self.channel_subscriptions: Dict[str, Set[str]] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Redis for distributed messaging
        self.redis_client = None
        self.redis_subscriber = None
        
        # Message handlers
        self.message_handlers: Dict[MessageType, List[Callable]] = {}
        
        # Statistics
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'messages_sent': 0,
            'messages_received': 0
        }
    
    async def initialize_redis(self, redis_url: str = "redis://localhost:6379"):
        """Initialize Redis connection for distributed messaging."""
        try:
            self.redis_client = aioredis.from_url(redis_url)
            self.redis_subscriber = self.redis_client.pubsub()
            
            # Subscribe to broadcast channels
            await self.redis_subscriber.subscribe("chabot:broadcast")
            await self.redis_subscriber.subscribe("chabot:agents")
            await self.redis_subscriber.subscribe("chabot:system")
            
            # Start Redis message listener
            asyncio.create_task(self._redis_message_listener())
            
            self.logger.info("Redis connection initialized for real-time messaging")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Redis: {e}")
    
    async def connect(self, websocket: WebSocket, connection_id: str, user_id: str, metadata: Dict[str, Any] = None):
        """Accept new WebSocket connection."""
        await websocket.accept()
        
        self.active_connections[connection_id] = websocket
        
        # Track user connections
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(connection_id)
        
        # Store connection metadata
        self.connection_metadata[connection_id] = {
            'user_id': user_id,
            'connected_at': datetime.now(),
            'last_activity': datetime.now(),
            'metadata': metadata or {}
        }
        
        # Update statistics
        self.stats['total_connections'] += 1
        self.stats['active_connections'] = len(self.active_connections)
        
        self.logger.info(f"WebSocket connection established: {connection_id} for user {user_id}")
        
        # Send welcome message
        await self.send_to_connection(connection_id, {
            'type': 'connection_established',
            'connection_id': connection_id,
            'timestamp': datetime.now().isoformat()
        })
    
    async def disconnect(self, connection_id: str):
        """Handle WebSocket disconnection."""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            
            # Get user ID
            user_id = self.connection_metadata.get(connection_id, {}).get('user_id')
            
            # Remove from active connections
            del self.active_connections[connection_id]
            
            # Remove from user connections
            if user_id and user_id in self.user_connections:
                self.user_connections[user_id].discard(connection_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            
            # Remove from channel subscriptions
            for channel, subscribers in self.channel_subscriptions.items():
                subscribers.discard(connection_id)
            
            # Remove metadata
            if connection_id in self.connection_metadata:
                del self.connection_metadata[connection_id]
            
            # Update statistics
            self.stats['active_connections'] = len(self.active_connections)
            
            self.logger.info(f"WebSocket connection closed: {connection_id}")
    
    async def send_to_connection(self, connection_id: str, message: Dict[str, Any]):
        """Send message to specific connection."""
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(message, default=str))
                
                # Update last activity
                if connection_id in self.connection_metadata:
                    self.connection_metadata[connection_id]['last_activity'] = datetime.now()
                
                self.stats['messages_sent'] += 1
                
            except Exception as e:
                self.logger.error(f"Failed to send message to connection {connection_id}: {e}")
                await self.disconnect(connection_id)
    
    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """Send message to all connections of a user."""
        if user_id in self.user_connections:
            tasks = []
            for connection_id in self.user_connections[user_id].copy():
                tasks.append(self.send_to_connection(connection_id, message))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    async def send_to_channel(self, channel: str, message: Dict[str, Any], exclude_connection: str = None):
        """Send message to all subscribers of a channel."""
        if channel in self.channel_subscriptions:
            tasks = []
            for connection_id in self.channel_subscriptions[channel].copy():
                if connection_id != exclude_connection:
                    tasks.append(self.send_to_connection(connection_id, message))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    async def broadcast(self, message: Dict[str, Any], exclude_connection: str = None):
        """Broadcast message to all active connections."""
        tasks = []
        for connection_id in list(self.active_connections.keys()):
            if connection_id != exclude_connection:
                tasks.append(self.send_to_connection(connection_id, message))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        # Also broadcast via Redis for distributed systems
        if self.redis_client:
            await self.redis_client.publish("chabot:broadcast", json.dumps(message, default=str))
    
    async def subscribe_to_channel(self, connection_id: str, channel: str):
        """Subscribe connection to a channel."""
        if channel not in self.channel_subscriptions:
            self.channel_subscriptions[channel] = set()
        
        self.channel_subscriptions[channel].add(connection_id)
        
        await self.send_to_connection(connection_id, {
            'type': 'channel_subscribed',
            'channel': channel,
            'timestamp': datetime.now().isoformat()
        })
    
    async def unsubscribe_from_channel(self, connection_id: str, channel: str):
        """Unsubscribe connection from a channel."""
        if channel in self.channel_subscriptions:
            self.channel_subscriptions[channel].discard(connection_id)
            
            if not self.channel_subscriptions[channel]:
                del self.channel_subscriptions[channel]
        
        await self.send_to_connection(connection_id, {
            'type': 'channel_unsubscribed',
            'channel': channel,
            'timestamp': datetime.now().isoformat()
        })
    
    async def handle_message(self, connection_id: str, message: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        try:
            message_type = MessageType(message.get('type'))
            
            # Update statistics
            self.stats['messages_received'] += 1
            
            # Create realtime message
            realtime_message = RealtimeMessage(
                id=str(uuid.uuid4()),
                type=message_type,
                sender_id=self.connection_metadata.get(connection_id, {}).get('user_id', ''),
                recipient_id=message.get('recipient_id'),
                channel=message.get('channel', 'default'),
                content=message.get('content', {}),
                timestamp=datetime.now(),
                metadata=message.get('metadata', {})
            )
            
            # Call message handlers
            if message_type in self.message_handlers:
                for handler in self.message_handlers[message_type]:
                    try:
                        await handler(realtime_message)
                    except Exception as e:
                        self.logger.error(f"Message handler error: {e}")
            
            # Route message based on type
            await self._route_message(realtime_message)
            
        except Exception as e:
            self.logger.error(f"Error handling message from {connection_id}: {e}")
    
    async def _route_message(self, message: RealtimeMessage):
        """Route message to appropriate recipients."""
        if message.recipient_id:
            # Send to specific user
            await self.send_to_user(message.recipient_id, {
                'type': message.type.value,
                'sender_id': message.sender_id,
                'content': message.content,
                'timestamp': message.timestamp.isoformat(),
                'metadata': message.metadata
            })
        elif message.channel:
            # Send to channel subscribers
            await self.send_to_channel(message.channel, {
                'type': message.type.value,
                'sender_id': message.sender_id,
                'content': message.content,
                'timestamp': message.timestamp.isoformat(),
                'metadata': message.metadata
            })
    
    async def _redis_message_listener(self):
        """Listen for Redis pub/sub messages."""
        try:
            async for message in self.redis_subscriber.listen():
                if message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                        channel = message['channel'].decode()
                        
                        if channel == 'chabot:broadcast':
                            await self.broadcast(data)
                        elif channel == 'chabot:agents':
                            await self.send_to_channel('agents', data)
                        elif channel == 'chabot:system':
                            await self.send_to_channel('system', data)
                            
                    except Exception as e:
                        self.logger.error(f"Error processing Redis message: {e}")
                        
        except Exception as e:
            self.logger.error(f"Redis message listener error: {e}")
    
    def register_message_handler(self, message_type: MessageType, handler: Callable):
        """Register message handler for specific message type."""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        
        self.message_handlers[message_type].append(handler)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            **self.stats,
            'channels': len(self.channel_subscriptions),
            'users_online': len(self.user_connections)
        }
    
    async def cleanup_inactive_connections(self, timeout_minutes: int = 30):
        """Clean up inactive connections."""
        cutoff_time = datetime.now() - timedelta(minutes=timeout_minutes)
        inactive_connections = []
        
        for connection_id, metadata in self.connection_metadata.items():
            if metadata.get('last_activity', datetime.now()) < cutoff_time:
                inactive_connections.append(connection_id)
        
        for connection_id in inactive_connections:
            await self.disconnect(connection_id)
        
        if inactive_connections:
            self.logger.info(f"Cleaned up {len(inactive_connections)} inactive connections")


class ServerSentEventsManager:
    """Manages Server-Sent Events for real-time updates."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.active_streams: Dict[str, asyncio.Queue] = {}
        self.user_streams: Dict[str, Set[str]] = {}
    
    async def create_stream(self, stream_id: str, user_id: str) -> asyncio.Queue:
        """Create new SSE stream."""
        queue = asyncio.Queue()
        self.active_streams[stream_id] = queue
        
        if user_id not in self.user_streams:
            self.user_streams[user_id] = set()
        self.user_streams[user_id].add(stream_id)
        
        self.logger.info(f"SSE stream created: {stream_id} for user {user_id}")
        return queue
    
    async def close_stream(self, stream_id: str):
        """Close SSE stream."""
        if stream_id in self.active_streams:
            del self.active_streams[stream_id]
        
        # Remove from user streams
        for user_id, streams in self.user_streams.items():
            streams.discard(stream_id)
        
        self.logger.info(f"SSE stream closed: {stream_id}")
    
    async def send_to_stream(self, stream_id: str, data: Dict[str, Any]):
        """Send data to specific SSE stream."""
        if stream_id in self.active_streams:
            try:
                await self.active_streams[stream_id].put(data)
            except Exception as e:
                self.logger.error(f"Failed to send to SSE stream {stream_id}: {e}")
    
    async def send_to_user_streams(self, user_id: str, data: Dict[str, Any]):
        """Send data to all SSE streams of a user."""
        if user_id in self.user_streams:
            tasks = []
            for stream_id in self.user_streams[user_id].copy():
                tasks.append(self.send_to_stream(stream_id, data))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    async def generate_stream(self, stream_id: str) -> str:
        """Generate SSE stream data."""
        queue = self.active_streams.get(stream_id)
        if not queue:
            return
        
        try:
            while True:
                data = await queue.get()
                yield f"data: {json.dumps(data, default=str)}\n\n"
        except asyncio.CancelledError:
            await self.close_stream(stream_id)


class NotificationService:
    """Service for sending various types of notifications."""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Initialize notification providers
        self._init_email_client()
        self._init_sms_client()
        self._init_slack_client()
        
        # Notification queue
        self.notification_queue = asyncio.Queue()
        
        # Start notification processor
        asyncio.create_task(self._process_notifications())
    
    def _init_email_client(self):
        """Initialize email client."""
        email_config = self.config.get('email', {})
        self.smtp_server = email_config.get('smtp_server')
        self.smtp_port = email_config.get('smtp_port', 587)
        self.smtp_username = email_config.get('username')
        self.smtp_password = email_config.get('password')
        self.from_email = email_config.get('from_email')
    
    def _init_sms_client(self):
        """Initialize SMS client (Twilio)."""
        sms_config = self.config.get('sms', {})
        if sms_config.get('account_sid') and sms_config.get('auth_token'):
            self.twilio_client = TwilioClient(
                sms_config['account_sid'],
                sms_config['auth_token']
            )
            self.twilio_from_number = sms_config.get('from_number')
        else:
            self.twilio_client = None
    
    def _init_slack_client(self):
        """Initialize Slack client."""
        slack_config = self.config.get('slack', {})
        self.slack_webhook_url = slack_config.get('webhook_url')
        self.slack_token = slack_config.get('token')
    
    async def send_notification(self, notification: NotificationMessage):
        """Queue notification for sending."""
        await self.notification_queue.put(notification)
    
    async def _process_notifications(self):
        """Process notification queue."""
        while True:
            try:
                notification = await self.notification_queue.get()
                
                if notification.scheduled_at and notification.scheduled_at > datetime.now():
                    # Re-queue for later
                    await asyncio.sleep(1)
                    await self.notification_queue.put(notification)
                    continue
                
                success = await self._send_notification(notification)
                
                if success:
                    notification.sent_at = datetime.now()
                    self.logger.info(f"Notification sent: {notification.id} ({notification.type.value})")
                else:
                    self.logger.error(f"Failed to send notification: {notification.id}")
                
            except Exception as e:
                self.logger.error(f"Error processing notification: {e}")
    
    async def _send_notification(self, notification: NotificationMessage) -> bool:
        """Send notification based on type."""
        try:
            if notification.type == NotificationType.EMAIL:
                return await self._send_email(notification)
            elif notification.type == NotificationType.SMS:
                return await self._send_sms(notification)
            elif notification.type == NotificationType.SLACK:
                return await self._send_slack(notification)
            elif notification.type == NotificationType.WEBHOOK:
                return await self._send_webhook(notification)
            else:
                self.logger.warning(f"Unsupported notification type: {notification.type}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending {notification.type.value} notification: {e}")
            return False
    
    async def _send_email(self, notification: NotificationMessage) -> bool:
        """Send email notification."""
        if not all([self.smtp_server, self.smtp_username, self.smtp_password, self.from_email]):
            self.logger.error("Email configuration incomplete")
            return False
        
        try:
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = notification.recipient
            msg['Subject'] = notification.subject
            
            msg.attach(MIMEText(notification.content, 'html'))
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send email: {e}")
            return False
    
    async def _send_sms(self, notification: NotificationMessage) -> bool:
        """Send SMS notification."""
        if not self.twilio_client:
            self.logger.error("SMS client not configured")
            return False
        
        try:
            message = self.twilio_client.messages.create(
                body=notification.content,
                from_=self.twilio_from_number,
                to=notification.recipient
            )
            
            return message.sid is not None
            
        except Exception as e:
            self.logger.error(f"Failed to send SMS: {e}")
            return False
    
    async def _send_slack(self, notification: NotificationMessage) -> bool:
        """Send Slack notification."""
        if not self.slack_webhook_url:
            self.logger.error("Slack webhook URL not configured")
            return False
        
        try:
            payload = {
                'text': notification.subject,
                'attachments': [{
                    'color': 'good' if notification.priority == 'low' else 'warning' if notification.priority == 'medium' else 'danger',
                    'text': notification.content,
                    'ts': int(datetime.now().timestamp())
                }]
            }
            
            response = requests.post(self.slack_webhook_url, json=payload, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            self.logger.error(f"Failed to send Slack notification: {e}")
            return False
    
    async def _send_webhook(self, notification: NotificationMessage) -> bool:
        """Send webhook notification."""
        webhook_url = notification.metadata.get('webhook_url')
        if not webhook_url:
            self.logger.error("Webhook URL not provided")
            return False
        
        try:
            payload = {
                'id': notification.id,
                'type': notification.type.value,
                'recipient': notification.recipient,
                'subject': notification.subject,
                'content': notification.content,
                'priority': notification.priority,
                'timestamp': datetime.now().isoformat(),
                'metadata': notification.metadata
            }
            
            response = requests.post(webhook_url, json=payload, timeout=10)
            return response.status_code in [200, 201, 202]
            
        except Exception as e:
            self.logger.error(f"Failed to send webhook: {e}")
            return False


class RealtimeCommunicationService:
    """Main service for real-time communication."""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Initialize managers
        self.connection_manager = ConnectionManager()
        self.sse_manager = ServerSentEventsManager()
        self.notification_service = NotificationService(config.get('notifications', {}))
        
        # Initialize Redis
        asyncio.create_task(self.connection_manager.initialize_redis(
            config.get('redis_url', 'redis://localhost:6379')
        ))
        
        # Register default message handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default message handlers."""
        
        async def handle_chat_message(message: RealtimeMessage):
            """Handle chat message."""
            # Broadcast to conversation participants
            await self.connection_manager.send_to_channel(
                f"conversation:{message.content.get('conversation_id')}",
                {
                    'type': 'chat_message',
                    'message': message.content,
                    'sender_id': message.sender_id,
                    'timestamp': message.timestamp.isoformat()
                }
            )
        
        async def handle_agent_status(message: RealtimeMessage):
            """Handle agent status update."""
            # Broadcast to agents channel
            await self.connection_manager.send_to_channel('agents', {
                'type': 'agent_status',
                'agent_id': message.content.get('agent_id'),
                'status': message.content.get('status'),
                'timestamp': message.timestamp.isoformat()
            })
        
        async def handle_system_alert(message: RealtimeMessage):
            """Handle system alert."""
            # Send to system administrators
            await self.connection_manager.send_to_channel('system_alerts', {
                'type': 'system_alert',
                'alert': message.content,
                'timestamp': message.timestamp.isoformat()
            })
            
            # Send notification if critical
            if message.content.get('severity') == 'critical':
                notification = NotificationMessage(
                    id=str(uuid.uuid4()),
                    type=NotificationType.EMAIL,
                    recipient=self.config.get('admin_email', '<EMAIL>'),
                    subject=f"Critical System Alert: {message.content.get('title')}",
                    content=message.content.get('description', ''),
                    priority='high',
                    metadata={},
                    scheduled_at=None,
                    sent_at=None
                )
                await self.notification_service.send_notification(notification)
        
        # Register handlers
        self.connection_manager.register_message_handler(MessageType.CHAT_MESSAGE, handle_chat_message)
        self.connection_manager.register_message_handler(MessageType.AGENT_STATUS, handle_agent_status)
        self.connection_manager.register_message_handler(MessageType.SYSTEM_ALERT, handle_system_alert)
    
    async def broadcast_agent_collaboration(self, collaboration_data: Dict[str, Any]):
        """Broadcast agent collaboration update."""
        await self.connection_manager.send_to_channel('agents', {
            'type': 'agent_collaboration',
            'data': collaboration_data,
            'timestamp': datetime.now().isoformat()
        })
    
    async def broadcast_reasoning_update(self, reasoning_data: Dict[str, Any]):
        """Broadcast reasoning process update."""
        await self.connection_manager.send_to_channel('reasoning', {
            'type': 'reasoning_update',
            'data': reasoning_data,
            'timestamp': datetime.now().isoformat()
        })
    
    async def send_user_notification(self, user_id: str, notification_type: NotificationType, 
                                   subject: str, content: str, recipient: str, priority: str = 'medium'):
        """Send notification to user."""
        notification = NotificationMessage(
            id=str(uuid.uuid4()),
            type=notification_type,
            recipient=recipient,
            subject=subject,
            content=content,
            priority=priority,
            metadata={'user_id': user_id},
            scheduled_at=None,
            sent_at=None
        )
        
        await self.notification_service.send_notification(notification)
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        return {
            'websocket': self.connection_manager.get_connection_stats(),
            'sse_streams': len(self.sse_manager.active_streams),
            'notification_queue_size': self.notification_service.notification_queue.qsize()
        }


# FastAPI integration
def create_realtime_routes(rt_service: RealtimeCommunicationService) -> FastAPI:
    """Create FastAPI routes for real-time communication."""
    app = FastAPI()
    
    @app.websocket("/ws/{user_id}")
    async def websocket_endpoint(websocket: WebSocket, user_id: str):
        """WebSocket endpoint."""
        connection_id = str(uuid.uuid4())
        
        await rt_service.connection_manager.connect(websocket, connection_id, user_id)
        
        try:
            while True:
                data = await websocket.receive_text()
                message = json.loads(data)
                await rt_service.connection_manager.handle_message(connection_id, message)
                
        except WebSocketDisconnect:
            await rt_service.connection_manager.disconnect(connection_id)
        except Exception as e:
            rt_service.logger.error(f"WebSocket error: {e}")
            await rt_service.connection_manager.disconnect(connection_id)
    
    @app.get("/sse/{user_id}")
    async def sse_endpoint(request: Request, user_id: str):
        """Server-Sent Events endpoint."""
        stream_id = str(uuid.uuid4())
        queue = await rt_service.sse_manager.create_stream(stream_id, user_id)
        
        async def event_generator():
            try:
                while True:
                    if await request.is_disconnected():
                        break
                    
                    try:
                        data = await asyncio.wait_for(queue.get(), timeout=30.0)
                        yield f"data: {json.dumps(data, default=str)}\n\n"
                    except asyncio.TimeoutError:
                        yield "data: {\"type\": \"heartbeat\"}\n\n"
                        
            except asyncio.CancelledError:
                pass
            finally:
                await rt_service.sse_manager.close_stream(stream_id)
        
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
    
    @app.get("/stats")
    async def get_stats():
        """Get real-time service statistics."""
        return rt_service.get_service_stats()
    
    return app


# Configuration example
REALTIME_CONFIG = {
    'redis_url': 'redis://localhost:6379',
    'admin_email': '<EMAIL>',
    'notifications': {
        'email': {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': '<EMAIL>',
            'password': 'your-app-password',
            'from_email': '<EMAIL>'
        },
        'sms': {
            'account_sid': 'your-twilio-account-sid',
            'auth_token': 'your-twilio-auth-token',
            'from_number': '+**********'
        },
        'slack': {
            'webhook_url': 'https://hooks.slack.com/services/...',
            'token': 'xoxb-your-slack-token'
        }
    }
}


if __name__ == "__main__":
    # Example usage
    rt_service = RealtimeCommunicationService(REALTIME_CONFIG)
    app = create_realtime_routes(rt_service)
    
    # Run with: uvicorn realtime_communication:app --host 0.0.0.0 --port 8006