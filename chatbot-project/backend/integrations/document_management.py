"""
Document Management System Integration for CHaBot.
Supports SharePoint, Google Drive, OneDrive, and other document systems.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Union, BinaryIO
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import uuid
import mimetypes
import hashlib
from urllib.parse import urlencode, urlparse
import aiohttp
import aiofiles
from fastapi import UploadFile
import requests
from office365.runtime.auth.authentication_context import AuthenticationContext
from office365.sharepoint.client_context import ClientContext
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
import boto3
from azure.storage.blob import BlobServiceClient
from azure.identity import DefaultAzureCredential


class DocumentProvider(Enum):
    SHAREPOINT = "sharepoint"
    GOOGLE_DRIVE = "google_drive"
    ONEDRIVE = "onedrive"
    AWS_S3 = "aws_s3"
    AZURE_BLOB = "azure_blob"
    LOCAL_STORAGE = "local_storage"
    DROPBOX = "dropbox"
    BOX = "box"


class DocumentType(Enum):
    POLICY = "policy"
    PROCEDURE = "procedure"
    MANUAL = "manual"
    FORM = "form"
    TEMPLATE = "template"
    REPORT = "report"
    PRESENTATION = "presentation"
    SPREADSHEET = "spreadsheet"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    OTHER = "other"


@dataclass
class DocumentMetadata:
    id: str
    name: str
    path: str
    provider: DocumentProvider
    document_type: DocumentType
    size: int
    mime_type: str
    created_at: datetime
    modified_at: datetime
    created_by: str
    modified_by: str
    version: str
    tags: List[str]
    permissions: Dict[str, List[str]]
    checksum: str
    url: Optional[str]
    download_url: Optional[str]
    preview_url: Optional[str]
    thumbnail_url: Optional[str]
    metadata: Dict[str, Any]


@dataclass
class DocumentContent:
    metadata: DocumentMetadata
    content: Optional[bytes]
    text_content: Optional[str]
    extracted_metadata: Dict[str, Any]


class SharePointConnector:
    """SharePoint document management connector."""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        self.site_url = config['site_url']
        self.username = config['username']
        self.password = config['password']
        self.client_id = config.get('client_id')
        self.client_secret = config.get('client_secret')
        
        self.context = None
        self._initialize_context()
    
    def _initialize_context(self):
        """Initialize SharePoint context."""
        try:
            if self.client_id and self.client_secret:
                # App-only authentication
                auth_context = AuthenticationContext(self.site_url)
                auth_context.acquire_token_for_app(self.client_id, self.client_secret)
                self.context = ClientContext(self.site_url, auth_context)
            else:
                # User authentication
                auth_context = AuthenticationContext(self.site_url)
                auth_context.acquire_token_for_user(self.username, self.password)
                self.context = ClientContext(self.site_url, auth_context)
            
            self.logger.info("SharePoint context initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize SharePoint context: {e}")
            raise
    
    async def list_documents(self, folder_path: str = "", recursive: bool = True) -> List[DocumentMetadata]:
        """List documents in SharePoint."""
        try:
            documents = []
            
            # Get document library
            if folder_path:
                folder = self.context.web.get_folder_by_server_relative_url(folder_path)
            else:
                folder = self.context.web.default_document_library().root_folder
            
            # Load folder and files
            self.context.load(folder)
            self.context.execute_query()
            
            # Get files in folder
            files = folder.files
            self.context.load(files)
            self.context.execute_query()
            
            for file in files:
                metadata = await self._extract_sharepoint_metadata(file)
                documents.append(metadata)
            
            # Recursively get files from subfolders
            if recursive:
                folders = folder.folders
                self.context.load(folders)
                self.context.execute_query()
                
                for subfolder in folders:
                    if not subfolder.name.startswith('.'):  # Skip system folders
                        subfolder_docs = await self.list_documents(
                            subfolder.server_relative_url, 
                            recursive=True
                        )
                        documents.extend(subfolder_docs)
            
            return documents
            
        except Exception as e:
            self.logger.error(f"Failed to list SharePoint documents: {e}")
            return []
    
    async def get_document(self, document_id: str) -> Optional[DocumentContent]:
        """Get document content from SharePoint."""
        try:
            # Get file by ID
            file = self.context.web.get_file_by_id(document_id)
            self.context.load(file)
            self.context.execute_query()
            
            # Download file content
            response = file.download()
            self.context.execute_query()
            
            # Extract metadata
            metadata = await self._extract_sharepoint_metadata(file)
            
            # Extract text content if possible
            text_content = await self._extract_text_content(response.content, metadata.mime_type)
            
            return DocumentContent(
                metadata=metadata,
                content=response.content,
                text_content=text_content,
                extracted_metadata={}
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get SharePoint document {document_id}: {e}")
            return None
    
    async def upload_document(self, file_path: str, content: bytes, 
                            metadata: Dict[str, Any] = None) -> Optional[DocumentMetadata]:
        """Upload document to SharePoint."""
        try:
            # Get target folder
            folder = self.context.web.default_document_library().root_folder
            
            # Upload file
            uploaded_file = folder.upload_file(file_path, content)
            self.context.execute_query()
            
            # Set metadata if provided
            if metadata:
                list_item = uploaded_file.listItemAllFields
                for key, value in metadata.items():
                    list_item.set_property(key, value)
                list_item.update()
                self.context.execute_query()
            
            # Return metadata
            return await self._extract_sharepoint_metadata(uploaded_file)
            
        except Exception as e:
            self.logger.error(f"Failed to upload document to SharePoint: {e}")
            return None
    
    async def _extract_sharepoint_metadata(self, file) -> DocumentMetadata:
        """Extract metadata from SharePoint file."""
        # Load file properties
        self.context.load(file)
        self.context.load(file.listItemAllFields)
        self.context.execute_query()
        
        # Determine document type
        doc_type = self._determine_document_type(file.name, file.properties.get('ContentType', ''))
        
        return DocumentMetadata(
            id=file.unique_id,
            name=file.name,
            path=file.server_relative_url,
            provider=DocumentProvider.SHAREPOINT,
            document_type=doc_type,
            size=file.length,
            mime_type=file.properties.get('ContentType', 'application/octet-stream'),
            created_at=datetime.fromisoformat(file.time_created.replace('Z', '+00:00')).replace(tzinfo=None),
            modified_at=datetime.fromisoformat(file.time_last_modified.replace('Z', '+00:00')).replace(tzinfo=None),
            created_by=file.author.title if file.author else '',
            modified_by=file.modified_by.title if file.modified_by else '',
            version=file.ui_version_label,
            tags=[],
            permissions={},
            checksum='',
            url=f"{self.site_url}{file.server_relative_url}",
            download_url=f"{self.site_url}/_api/web/getfilebyserverrelativeurl('{file.server_relative_url}')/$value",
            preview_url=None,
            thumbnail_url=None,
            metadata=file.listItemAllFields.properties if file.listItemAllFields else {}
        )
    
    async def _extract_text_content(self, content: bytes, mime_type: str) -> Optional[str]:
        """Extract text content from document."""
        try:
            if mime_type.startswith('text/'):
                return content.decode('utf-8')
            elif mime_type == 'application/pdf':
                # Would use PyPDF2 or similar
                return None
            elif mime_type.startswith('application/vnd.openxmlformats'):
                # Would use python-docx or similar
                return None
            else:
                return None
        except Exception:
            return None
    
    def _determine_document_type(self, filename: str, content_type: str) -> DocumentType:
        """Determine document type from filename and content type."""
        filename_lower = filename.lower()
        
        if 'policy' in filename_lower:
            return DocumentType.POLICY
        elif 'procedure' in filename_lower or 'process' in filename_lower:
            return DocumentType.PROCEDURE
        elif 'manual' in filename_lower or 'guide' in filename_lower:
            return DocumentType.MANUAL
        elif 'form' in filename_lower:
            return DocumentType.FORM
        elif 'template' in filename_lower:
            return DocumentType.TEMPLATE
        elif 'report' in filename_lower:
            return DocumentType.REPORT
        elif content_type.startswith('image/'):
            return DocumentType.IMAGE
        elif content_type.startswith('video/'):
            return DocumentType.VIDEO
        elif content_type.startswith('audio/'):
            return DocumentType.AUDIO
        else:
            return DocumentType.OTHER


class GoogleDriveConnector:
    """Google Drive document management connector."""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        self.credentials_file = config['credentials_file']
        self.token_file = config.get('token_file', 'token.json')
        self.scopes = config.get('scopes', ['https://www.googleapis.com/auth/drive.readonly'])
        
        self.service = None
        self._initialize_service()
    
    def _initialize_service(self):
        """Initialize Google Drive service."""
        try:
            creds = None
            
            # Load existing token
            if os.path.exists(self.token_file):
                creds = Credentials.from_authorized_user_file(self.token_file, self.scopes)
            
            # If no valid credentials, get new ones
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    flow = Flow.from_client_secrets_file(self.credentials_file, self.scopes)
                    flow.redirect_uri = 'urn:ietf:wg:oauth:2.0:oob'
                    
                    auth_url, _ = flow.authorization_url(prompt='consent')
                    self.logger.info(f"Please visit: {auth_url}")
                    
                    code = input('Enter authorization code: ')
                    flow.fetch_token(code=code)
                    creds = flow.credentials
                
                # Save credentials
                with open(self.token_file, 'w') as token:
                    token.write(creds.to_json())
            
            self.service = build('drive', 'v3', credentials=creds)
            self.logger.info("Google Drive service initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Google Drive service: {e}")
            raise
    
    async def list_documents(self, folder_id: str = None, recursive: bool = True) -> List[DocumentMetadata]:
        """List documents in Google Drive."""
        try:
            documents = []
            
            # Build query
            query = "trashed=false"
            if folder_id:
                query += f" and '{folder_id}' in parents"
            
            # Get files
            results = self.service.files().list(
                q=query,
                fields="nextPageToken, files(id, name, mimeType, size, createdTime, modifiedTime, owners, parents, webViewLink, webContentLink, thumbnailLink)"
            ).execute()
            
            files = results.get('files', [])
            
            for file in files:
                metadata = await self._extract_drive_metadata(file)
                documents.append(metadata)
                
                # Recursively get files from folders
                if recursive and file['mimeType'] == 'application/vnd.google-apps.folder':
                    subfolder_docs = await self.list_documents(file['id'], recursive=True)
                    documents.extend(subfolder_docs)
            
            return documents
            
        except Exception as e:
            self.logger.error(f"Failed to list Google Drive documents: {e}")
            return []
    
    async def get_document(self, document_id: str) -> Optional[DocumentContent]:
        """Get document content from Google Drive."""
        try:
            # Get file metadata
            file = self.service.files().get(
                fileId=document_id,
                fields="id, name, mimeType, size, createdTime, modifiedTime, owners, webViewLink, webContentLink"
            ).execute()
            
            # Download file content
            if file['mimeType'].startswith('application/vnd.google-apps'):
                # Google Workspace document - export as PDF
                content = self.service.files().export(
                    fileId=document_id,
                    mimeType='application/pdf'
                ).execute()
            else:
                # Regular file - download directly
                content = self.service.files().get_media(fileId=document_id).execute()
            
            # Extract metadata
            metadata = await self._extract_drive_metadata(file)
            
            # Extract text content
            text_content = await self._extract_text_content(content, metadata.mime_type)
            
            return DocumentContent(
                metadata=metadata,
                content=content,
                text_content=text_content,
                extracted_metadata={}
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get Google Drive document {document_id}: {e}")
            return None
    
    async def upload_document(self, filename: str, content: bytes, 
                            folder_id: str = None, metadata: Dict[str, Any] = None) -> Optional[DocumentMetadata]:
        """Upload document to Google Drive."""
        try:
            # Prepare file metadata
            file_metadata = {'name': filename}
            if folder_id:
                file_metadata['parents'] = [folder_id]
            
            # Upload file
            media = MediaIoBaseUpload(
                io.BytesIO(content),
                mimetype=mimetypes.guess_type(filename)[0] or 'application/octet-stream'
            )
            
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id, name, mimeType, size, createdTime, modifiedTime, owners, webViewLink'
            ).execute()
            
            return await self._extract_drive_metadata(file)
            
        except Exception as e:
            self.logger.error(f"Failed to upload document to Google Drive: {e}")
            return None
    
    async def _extract_drive_metadata(self, file: Dict[str, Any]) -> DocumentMetadata:
        """Extract metadata from Google Drive file."""
        # Determine document type
        doc_type = self._determine_document_type(file['name'], file['mimeType'])
        
        # Parse dates
        created_at = datetime.fromisoformat(file['createdTime'].replace('Z', '+00:00')).replace(tzinfo=None)
        modified_at = datetime.fromisoformat(file['modifiedTime'].replace('Z', '+00:00')).replace(tzinfo=None)
        
        # Get owner info
        owners = file.get('owners', [])
        created_by = owners[0]['displayName'] if owners else ''
        
        return DocumentMetadata(
            id=file['id'],
            name=file['name'],
            path=f"/drive/{file['id']}",
            provider=DocumentProvider.GOOGLE_DRIVE,
            document_type=doc_type,
            size=int(file.get('size', 0)),
            mime_type=file['mimeType'],
            created_at=created_at,
            modified_at=modified_at,
            created_by=created_by,
            modified_by=created_by,  # Google Drive doesn't track separate modifier
            version='1',  # Google Drive handles versioning internally
            tags=[],
            permissions={},
            checksum='',
            url=file.get('webViewLink', ''),
            download_url=file.get('webContentLink', ''),
            preview_url=file.get('webViewLink', ''),
            thumbnail_url=file.get('thumbnailLink', ''),
            metadata=file
        )


class DocumentManagementService:
    """Unified document management service."""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Initialize connectors
        self.connectors: Dict[DocumentProvider, Any] = {}
        self._initialize_connectors()
        
        # Document cache
        self.document_cache = {}
        self.cache_ttl = config.get('cache_ttl', 3600)  # 1 hour
        
        # Search index
        self.search_index = {}
    
    def _initialize_connectors(self):
        """Initialize document management connectors."""
        try:
            # SharePoint
            if 'sharepoint' in self.config:
                self.connectors[DocumentProvider.SHAREPOINT] = SharePointConnector(
                    self.config['sharepoint']
                )
            
            # Google Drive
            if 'google_drive' in self.config:
                self.connectors[DocumentProvider.GOOGLE_DRIVE] = GoogleDriveConnector(
                    self.config['google_drive']
                )
            
            # AWS S3
            if 'aws_s3' in self.config:
                self.connectors[DocumentProvider.AWS_S3] = self._create_s3_connector(
                    self.config['aws_s3']
                )
            
            # Azure Blob Storage
            if 'azure_blob' in self.config:
                self.connectors[DocumentProvider.AZURE_BLOB] = self._create_azure_connector(
                    self.config['azure_blob']
                )
            
            self.logger.info(f"Initialized {len(self.connectors)} document connectors")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize document connectors: {e}")
    
    def _create_s3_connector(self, config: Dict[str, Any]):
        """Create AWS S3 connector."""
        return boto3.client(
            's3',
            aws_access_key_id=config['access_key_id'],
            aws_secret_access_key=config['secret_access_key'],
            region_name=config.get('region', 'us-east-1')
        )
    
    def _create_azure_connector(self, config: Dict[str, Any]):
        """Create Azure Blob Storage connector."""
        if 'connection_string' in config:
            return BlobServiceClient.from_connection_string(config['connection_string'])
        else:
            return BlobServiceClient(
                account_url=config['account_url'],
                credential=DefaultAzureCredential()
            )
    
    async def list_all_documents(self, providers: List[DocumentProvider] = None) -> List[DocumentMetadata]:
        """List documents from all or specified providers."""
        if providers is None:
            providers = list(self.connectors.keys())
        
        all_documents = []
        
        for provider in providers:
            if provider in self.connectors:
                try:
                    documents = await self.connectors[provider].list_documents()
                    all_documents.extend(documents)
                except Exception as e:
                    self.logger.error(f"Failed to list documents from {provider.value}: {e}")
        
        return all_documents
    
    async def search_documents(self, query: str, providers: List[DocumentProvider] = None,
                             document_types: List[DocumentType] = None) -> List[DocumentMetadata]:
        """Search documents across providers."""
        all_documents = await self.list_all_documents(providers)
        
        # Filter by document types
        if document_types:
            all_documents = [doc for doc in all_documents if doc.document_type in document_types]
        
        # Simple text search
        query_lower = query.lower()
        matching_documents = []
        
        for doc in all_documents:
            if (query_lower in doc.name.lower() or
                query_lower in doc.path.lower() or
                any(query_lower in tag.lower() for tag in doc.tags)):
                matching_documents.append(doc)
        
        return matching_documents
    
    async def get_document_content(self, document_id: str, provider: DocumentProvider) -> Optional[DocumentContent]:
        """Get document content from specific provider."""
        # Check cache first
        cache_key = f"{provider.value}:{document_id}"
        if cache_key in self.document_cache:
            cached_data, timestamp = self.document_cache[cache_key]
            if datetime.now() - timestamp < timedelta(seconds=self.cache_ttl):
                return cached_data
        
        # Get from provider
        if provider in self.connectors:
            try:
                content = await self.connectors[provider].get_document(document_id)
                
                # Cache the result
                if content:
                    self.document_cache[cache_key] = (content, datetime.now())
                
                return content
                
            except Exception as e:
                self.logger.error(f"Failed to get document {document_id} from {provider.value}: {e}")
        
        return None
    
    async def upload_document(self, filename: str, content: bytes, provider: DocumentProvider,
                            metadata: Dict[str, Any] = None) -> Optional[DocumentMetadata]:
        """Upload document to specific provider."""
        if provider in self.connectors:
            try:
                return await self.connectors[provider].upload_document(filename, content, metadata)
            except Exception as e:
                self.logger.error(f"Failed to upload document to {provider.value}: {e}")
        
        return None
    
    async def sync_documents(self, providers: List[DocumentProvider] = None) -> Dict[str, Any]:
        """Sync documents from all providers and update search index."""
        if providers is None:
            providers = list(self.connectors.keys())
        
        sync_results = {}
        
        for provider in providers:
            try:
                documents = await self.connectors[provider].list_documents()
                
                # Update search index
                for doc in documents:
                    self.search_index[f"{provider.value}:{doc.id}"] = {
                        'name': doc.name,
                        'path': doc.path,
                        'type': doc.document_type.value,
                        'tags': doc.tags,
                        'content': '',  # Would extract text content for full-text search
                        'provider': provider.value,
                        'last_updated': datetime.now()
                    }
                
                sync_results[provider.value] = {
                    'status': 'success',
                    'document_count': len(documents),
                    'last_sync': datetime.now().isoformat()
                }
                
            except Exception as e:
                sync_results[provider.value] = {
                    'status': 'error',
                    'error': str(e),
                    'last_sync': datetime.now().isoformat()
                }
        
        return sync_results
    
    def get_document_stats(self) -> Dict[str, Any]:
        """Get document management statistics."""
        stats = {
            'providers': len(self.connectors),
            'cached_documents': len(self.document_cache),
            'search_index_size': len(self.search_index),
            'provider_status': {}
        }
        
        for provider in self.connectors.keys():
            stats['provider_status'][provider.value] = 'connected'
        
        return stats


# Configuration example
DOCUMENT_CONFIG_EXAMPLE = {
    'sharepoint': {
        'site_url': 'https://company.sharepoint.com/sites/documents',
        'username': '<EMAIL>',
        'password': 'service_password',
        'client_id': 'your-client-id',
        'client_secret': 'your-client-secret'
    },
    'google_drive': {
        'credentials_file': 'credentials.json',
        'token_file': 'token.json',
        'scopes': ['https://www.googleapis.com/auth/drive.readonly']
    },
    'aws_s3': {
        'access_key_id': 'your-access-key',
        'secret_access_key': 'your-secret-key',
        'region': 'us-east-1',
        'bucket_name': 'company-documents'
    },
    'azure_blob': {
        'connection_string': 'DefaultEndpointsProtocol=https;AccountName=...',
        'container_name': 'documents'
    },
    'cache_ttl': 3600
}


if __name__ == "__main__":
    # Example usage
    doc_service = DocumentManagementService(DOCUMENT_CONFIG_EXAMPLE)
    
    # List all documents
    # documents = await doc_service.list_all_documents()
    
    # Search documents
    # results = await doc_service.search_documents("policy")
    
    # Get document content
    # content = await doc_service.get_document_content("doc_id", DocumentProvider.SHAREPOINT)