"""
LDAP/Active Directory Integration for CHaBot Authentication System.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import ssl
import ldap3
from ldap3 import Server, Connection, ALL, NTLM, SIMPLE, SUBTREE
from ldap3.core.exceptions import LDAPException
import json
from datetime import datetime, timedelta
import hashlib
import secrets


class AuthenticationMethod(Enum):
    SIMPLE = "simple"
    NTLM = "ntlm"
    KERBEROS = "kerberos"


@dataclass
class LDAPUser:
    username: str
    email: str
    full_name: str
    department: str
    organization: str
    groups: List[str]
    manager: Optional[str]
    employee_id: Optional[str]
    phone: Optional[str]
    title: Optional[str]
    distinguished_name: str
    last_login: Optional[datetime]
    account_enabled: bool


@dataclass
class LDAPGroup:
    name: str
    distinguished_name: str
    description: str
    members: List[str]
    group_type: str


class LDAPConnector:
    """LDAP/Active Directory connector for enterprise authentication."""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger(__name__)
        self.config = config
        self.server = None
        self.connection = None
        self.connection_pool = []
        self.max_pool_size = config.get('max_pool_size', 10)
        
        # LDAP Configuration
        self.server_uri = config['server_uri']
        self.base_dn = config['base_dn']
        self.bind_dn = config.get('bind_dn')
        self.bind_password = config.get('bind_password')
        self.auth_method = AuthenticationMethod(config.get('auth_method', 'simple'))
        self.use_ssl = config.get('use_ssl', True)
        self.use_tls = config.get('use_tls', False)
        
        # Search configurations
        self.user_search_base = config.get('user_search_base', self.base_dn)
        self.group_search_base = config.get('group_search_base', self.base_dn)
        self.user_filter = config.get('user_filter', '(objectClass=person)')
        self.group_filter = config.get('group_filter', '(objectClass=group)')
        
        # Attribute mappings
        self.user_attributes = config.get('user_attributes', {
            'username': 'sAMAccountName',
            'email': 'mail',
            'full_name': 'displayName',
            'department': 'department',
            'organization': 'company',
            'manager': 'manager',
            'employee_id': 'employeeID',
            'phone': 'telephoneNumber',
            'title': 'title'
        })
        
        # Cache settings
        self.cache_ttl = config.get('cache_ttl', 300)  # 5 minutes
        self.user_cache = {}
        self.group_cache = {}
        
        # Initialize connection
        self._initialize_server()
    
    def _initialize_server(self):
        """Initialize LDAP server connection."""
        try:
            # Configure SSL/TLS
            tls_config = None
            if self.use_ssl or self.use_tls:
                tls_config = ldap3.Tls(
                    validate=ssl.CERT_REQUIRED if self.config.get('verify_ssl', True) else ssl.CERT_NONE,
                    version=ssl.PROTOCOL_TLSv1_2,
                    ca_certs_file=self.config.get('ca_cert_file'),
                    private_key_file=self.config.get('private_key_file'),
                    private_key_password=self.config.get('private_key_password')
                )
            
            # Create server
            port = self.config.get('port', 636 if self.use_ssl else 389)
            self.server = Server(
                self.server_uri,
                port=port,
                use_ssl=self.use_ssl,
                tls=tls_config,
                get_info=ALL
            )
            
            self.logger.info(f"LDAP server initialized: {self.server_uri}:{port}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LDAP server: {e}")
            raise
    
    async def get_connection(self) -> Connection:
        """Get LDAP connection from pool or create new one."""
        # Try to get connection from pool
        if self.connection_pool:
            connection = self.connection_pool.pop()
            if connection.bound:
                return connection
        
        # Create new connection
        try:
            if self.auth_method == AuthenticationMethod.NTLM:
                authentication = NTLM
            else:
                authentication = SIMPLE
            
            connection = Connection(
                self.server,
                user=self.bind_dn,
                password=self.bind_password,
                authentication=authentication,
                auto_bind=True,
                auto_range=True,
                raise_exceptions=True
            )
            
            return connection
            
        except LDAPException as e:
            self.logger.error(f"Failed to create LDAP connection: {e}")
            raise
    
    def return_connection(self, connection: Connection):
        """Return connection to pool."""
        if len(self.connection_pool) < self.max_pool_size and connection.bound:
            self.connection_pool.append(connection)
        else:
            connection.unbind()
    
    async def authenticate_user(self, username: str, password: str) -> Tuple[bool, Optional[LDAPUser]]:
        """Authenticate user against LDAP/AD."""
        try:
            # Get user DN
            user_dn = await self.get_user_dn(username)
            if not user_dn:
                self.logger.warning(f"User not found in LDAP: {username}")
                return False, None
            
            # Try to bind with user credentials
            try:
                auth_connection = Connection(
                    self.server,
                    user=user_dn,
                    password=password,
                    authentication=SIMPLE,
                    auto_bind=True,
                    raise_exceptions=True
                )
                
                # Authentication successful
                auth_connection.unbind()
                
                # Get user details
                user = await self.get_user_details(username)
                if user:
                    user.last_login = datetime.now()
                    self.logger.info(f"User authenticated successfully: {username}")
                    return True, user
                else:
                    return False, None
                    
            except LDAPException as e:
                self.logger.warning(f"Authentication failed for user {username}: {e}")
                return False, None
                
        except Exception as e:
            self.logger.error(f"Error during authentication for user {username}: {e}")
            return False, None
    
    async def get_user_dn(self, username: str) -> Optional[str]:
        """Get user distinguished name."""
        connection = await self.get_connection()
        try:
            search_filter = f"(&{self.user_filter}({self.user_attributes['username']}={username}))"
            
            success = connection.search(
                search_base=self.user_search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName']
            )
            
            if success and connection.entries:
                return str(connection.entries[0].distinguishedName)
            
            return None
            
        finally:
            self.return_connection(connection)
    
    async def get_user_details(self, username: str) -> Optional[LDAPUser]:
        """Get detailed user information from LDAP."""
        # Check cache first
        cache_key = f"user:{username}"
        if cache_key in self.user_cache:
            cached_data, timestamp = self.user_cache[cache_key]
            if datetime.now() - timestamp < timedelta(seconds=self.cache_ttl):
                return cached_data
        
        connection = await self.get_connection()
        try:
            # Build search filter
            search_filter = f"(&{self.user_filter}({self.user_attributes['username']}={username}))"
            
            # Get all user attributes
            attributes = list(self.user_attributes.values()) + [
                'distinguishedName', 'memberOf', 'userAccountControl'
            ]
            
            success = connection.search(
                search_base=self.user_search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=attributes
            )
            
            if not success or not connection.entries:
                return None
            
            entry = connection.entries[0]
            
            # Extract user information
            user = LDAPUser(
                username=self._get_attribute_value(entry, self.user_attributes['username']),
                email=self._get_attribute_value(entry, self.user_attributes['email']),
                full_name=self._get_attribute_value(entry, self.user_attributes['full_name']),
                department=self._get_attribute_value(entry, self.user_attributes['department']),
                organization=self._get_attribute_value(entry, self.user_attributes['organization']),
                manager=self._get_attribute_value(entry, self.user_attributes['manager']),
                employee_id=self._get_attribute_value(entry, self.user_attributes['employee_id']),
                phone=self._get_attribute_value(entry, self.user_attributes['phone']),
                title=self._get_attribute_value(entry, self.user_attributes['title']),
                distinguished_name=str(entry.distinguishedName),
                groups=self._extract_groups(entry.memberOf.values if hasattr(entry, 'memberOf') else []),
                account_enabled=self._is_account_enabled(entry),
                last_login=None
            )
            
            # Cache the result
            self.user_cache[cache_key] = (user, datetime.now())
            
            return user
            
        except Exception as e:
            self.logger.error(f"Error getting user details for {username}: {e}")
            return None
            
        finally:
            self.return_connection(connection)
    
    async def get_user_groups(self, username: str) -> List[str]:
        """Get user's group memberships."""
        user = await self.get_user_details(username)
        return user.groups if user else []
    
    async def get_group_members(self, group_name: str) -> List[str]:
        """Get members of a specific group."""
        # Check cache first
        cache_key = f"group:{group_name}"
        if cache_key in self.group_cache:
            cached_data, timestamp = self.group_cache[cache_key]
            if datetime.now() - timestamp < timedelta(seconds=self.cache_ttl):
                return cached_data
        
        connection = await self.get_connection()
        try:
            search_filter = f"(&{self.group_filter}(cn={group_name}))"
            
            success = connection.search(
                search_base=self.group_search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['member']
            )
            
            if not success or not connection.entries:
                return []
            
            entry = connection.entries[0]
            members = []
            
            if hasattr(entry, 'member'):
                for member_dn in entry.member.values:
                    # Extract username from DN
                    username = await self._get_username_from_dn(member_dn)
                    if username:
                        members.append(username)
            
            # Cache the result
            self.group_cache[cache_key] = (members, datetime.now())
            
            return members
            
        except Exception as e:
            self.logger.error(f"Error getting group members for {group_name}: {e}")
            return []
            
        finally:
            self.return_connection(connection)
    
    async def search_users(self, search_term: str, limit: int = 50) -> List[LDAPUser]:
        """Search for users by name, email, or username."""
        connection = await self.get_connection()
        try:
            # Build search filter for multiple attributes
            search_filters = [
                f"({self.user_attributes['username']}=*{search_term}*)",
                f"({self.user_attributes['email']}=*{search_term}*)",
                f"({self.user_attributes['full_name']}=*{search_term}*)"
            ]
            
            search_filter = f"(&{self.user_filter}(|{''.join(search_filters)}))"
            
            attributes = list(self.user_attributes.values()) + [
                'distinguishedName', 'memberOf', 'userAccountControl'
            ]
            
            success = connection.search(
                search_base=self.user_search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=attributes,
                size_limit=limit
            )
            
            if not success:
                return []
            
            users = []
            for entry in connection.entries:
                user = LDAPUser(
                    username=self._get_attribute_value(entry, self.user_attributes['username']),
                    email=self._get_attribute_value(entry, self.user_attributes['email']),
                    full_name=self._get_attribute_value(entry, self.user_attributes['full_name']),
                    department=self._get_attribute_value(entry, self.user_attributes['department']),
                    organization=self._get_attribute_value(entry, self.user_attributes['organization']),
                    manager=self._get_attribute_value(entry, self.user_attributes['manager']),
                    employee_id=self._get_attribute_value(entry, self.user_attributes['employee_id']),
                    phone=self._get_attribute_value(entry, self.user_attributes['phone']),
                    title=self._get_attribute_value(entry, self.user_attributes['title']),
                    distinguished_name=str(entry.distinguishedName),
                    groups=self._extract_groups(entry.memberOf.values if hasattr(entry, 'memberOf') else []),
                    account_enabled=self._is_account_enabled(entry),
                    last_login=None
                )
                users.append(user)
            
            return users
            
        except Exception as e:
            self.logger.error(f"Error searching users with term '{search_term}': {e}")
            return []
            
        finally:
            self.return_connection(connection)
    
    async def get_organizational_structure(self) -> Dict[str, List[str]]:
        """Get organizational structure from LDAP."""
        connection = await self.get_connection()
        try:
            # Get all users with department and manager information
            attributes = [
                self.user_attributes['username'],
                self.user_attributes['department'],
                self.user_attributes['manager'],
                self.user_attributes['full_name']
            ]
            
            success = connection.search(
                search_base=self.user_search_base,
                search_filter=self.user_filter,
                search_scope=SUBTREE,
                attributes=attributes
            )
            
            if not success:
                return {}
            
            # Build organizational structure
            structure = {}
            for entry in connection.entries:
                department = self._get_attribute_value(entry, self.user_attributes['department'])
                username = self._get_attribute_value(entry, self.user_attributes['username'])
                
                if department and username:
                    if department not in structure:
                        structure[department] = []
                    structure[department].append(username)
            
            return structure
            
        except Exception as e:
            self.logger.error(f"Error getting organizational structure: {e}")
            return {}
            
        finally:
            self.return_connection(connection)
    
    async def sync_users_to_database(self, database_connector) -> Dict[str, int]:
        """Sync LDAP users to local database."""
        connection = await self.get_connection()
        stats = {'created': 0, 'updated': 0, 'errors': 0}
        
        try:
            # Get all users from LDAP
            attributes = list(self.user_attributes.values()) + [
                'distinguishedName', 'memberOf', 'userAccountControl'
            ]
            
            success = connection.search(
                search_base=self.user_search_base,
                search_filter=self.user_filter,
                search_scope=SUBTREE,
                attributes=attributes
            )
            
            if not success:
                return stats
            
            for entry in connection.entries:
                try:
                    user = LDAPUser(
                        username=self._get_attribute_value(entry, self.user_attributes['username']),
                        email=self._get_attribute_value(entry, self.user_attributes['email']),
                        full_name=self._get_attribute_value(entry, self.user_attributes['full_name']),
                        department=self._get_attribute_value(entry, self.user_attributes['department']),
                        organization=self._get_attribute_value(entry, self.user_attributes['organization']),
                        manager=self._get_attribute_value(entry, self.user_attributes['manager']),
                        employee_id=self._get_attribute_value(entry, self.user_attributes['employee_id']),
                        phone=self._get_attribute_value(entry, self.user_attributes['phone']),
                        title=self._get_attribute_value(entry, self.user_attributes['title']),
                        distinguished_name=str(entry.distinguishedName),
                        groups=self._extract_groups(entry.memberOf.values if hasattr(entry, 'memberOf') else []),
                        account_enabled=self._is_account_enabled(entry),
                        last_login=None
                    )
                    
                    # Sync to database
                    if await database_connector.user_exists(user.username):
                        await database_connector.update_user(user)
                        stats['updated'] += 1
                    else:
                        await database_connector.create_user(user)
                        stats['created'] += 1
                        
                except Exception as e:
                    self.logger.error(f"Error syncing user {entry}: {e}")
                    stats['errors'] += 1
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error during user sync: {e}")
            return stats
            
        finally:
            self.return_connection(connection)
    
    def _get_attribute_value(self, entry, attribute_name: str) -> Optional[str]:
        """Get attribute value from LDAP entry."""
        try:
            if hasattr(entry, attribute_name):
                attr = getattr(entry, attribute_name)
                if attr and attr.value:
                    return str(attr.value)
            return None
        except Exception:
            return None
    
    def _extract_groups(self, member_of_values: List[str]) -> List[str]:
        """Extract group names from memberOf attribute values."""
        groups = []
        for dn in member_of_values:
            # Extract CN from DN (e.g., "CN=GroupName,OU=Groups,DC=domain,DC=com")
            try:
                parts = dn.split(',')
                for part in parts:
                    if part.strip().startswith('CN='):
                        group_name = part.strip()[3:]  # Remove "CN="
                        groups.append(group_name)
                        break
            except Exception:
                continue
        return groups
    
    def _is_account_enabled(self, entry) -> bool:
        """Check if user account is enabled."""
        try:
            if hasattr(entry, 'userAccountControl'):
                uac = int(entry.userAccountControl.value)
                # Check if ACCOUNTDISABLE flag (0x2) is not set
                return not (uac & 0x2)
            return True
        except Exception:
            return True
    
    async def _get_username_from_dn(self, dn: str) -> Optional[str]:
        """Extract username from distinguished name."""
        connection = await self.get_connection()
        try:
            success = connection.search(
                search_base=dn,
                search_filter='(objectClass=*)',
                search_scope='BASE',
                attributes=[self.user_attributes['username']]
            )
            
            if success and connection.entries:
                return self._get_attribute_value(connection.entries[0], self.user_attributes['username'])
            
            return None
            
        except Exception:
            return None
            
        finally:
            self.return_connection(connection)
    
    async def test_connection(self) -> Tuple[bool, str]:
        """Test LDAP connection."""
        try:
            connection = await self.get_connection()
            
            # Try a simple search
            success = connection.search(
                search_base=self.base_dn,
                search_filter='(objectClass=*)',
                search_scope='BASE',
                attributes=['objectClass']
            )
            
            self.return_connection(connection)
            
            if success:
                return True, "LDAP connection successful"
            else:
                return False, "LDAP search failed"
                
        except Exception as e:
            return False, f"LDAP connection failed: {str(e)}"
    
    def clear_cache(self):
        """Clear user and group caches."""
        self.user_cache.clear()
        self.group_cache.clear()
        self.logger.info("LDAP cache cleared")
    
    async def close_connections(self):
        """Close all LDAP connections."""
        for connection in self.connection_pool:
            try:
                connection.unbind()
            except Exception:
                pass
        self.connection_pool.clear()
        
        if self.connection and self.connection.bound:
            self.connection.unbind()


# Configuration examples
LDAP_CONFIG_EXAMPLES = {
    "active_directory": {
        "server_uri": "ldap://dc.company.com",
        "port": 389,
        "base_dn": "DC=company,DC=com",
        "bind_dn": "CN=service_account,OU=Service Accounts,DC=company,DC=com",
        "bind_password": "service_password",
        "auth_method": "simple",
        "use_ssl": False,
        "use_tls": True,
        "user_search_base": "OU=Users,DC=company,DC=com",
        "group_search_base": "OU=Groups,DC=company,DC=com",
        "user_filter": "(&(objectClass=user)(objectCategory=person))",
        "group_filter": "(objectClass=group)",
        "user_attributes": {
            "username": "sAMAccountName",
            "email": "mail",
            "full_name": "displayName",
            "department": "department",
            "organization": "company",
            "manager": "manager",
            "employee_id": "employeeID",
            "phone": "telephoneNumber",
            "title": "title"
        }
    },
    "openldap": {
        "server_uri": "ldap://ldap.company.com",
        "port": 389,
        "base_dn": "dc=company,dc=com",
        "bind_dn": "cn=admin,dc=company,dc=com",
        "bind_password": "admin_password",
        "auth_method": "simple",
        "use_ssl": False,
        "use_tls": True,
        "user_search_base": "ou=people,dc=company,dc=com",
        "group_search_base": "ou=groups,dc=company,dc=com",
        "user_filter": "(objectClass=inetOrgPerson)",
        "group_filter": "(objectClass=groupOfNames)",
        "user_attributes": {
            "username": "uid",
            "email": "mail",
            "full_name": "cn",
            "department": "departmentNumber",
            "organization": "o",
            "manager": "manager",
            "employee_id": "employeeNumber",
            "phone": "telephoneNumber",
            "title": "title"
        }
    }
}


# Factory function for creating LDAP connector
def create_ldap_connector(config: Dict[str, Any]) -> LDAPConnector:
    """Factory function to create LDAP connector with configuration."""
    return LDAPConnector(config)


# Example usage
if __name__ == "__main__":
    import asyncio
    
    async def test_ldap():
        # Use Active Directory configuration
        config = LDAP_CONFIG_EXAMPLES["active_directory"]
        
        ldap_connector = create_ldap_connector(config)
        
        # Test connection
        success, message = await ldap_connector.test_connection()
        print(f"Connection test: {success} - {message}")
        
        if success:
            # Test authentication
            auth_success, user = await ldap_connector.authenticate_user("testuser", "testpass")
            print(f"Authentication: {auth_success}")
            
            if user:
                print(f"User: {user.full_name} ({user.email})")
                print(f"Department: {user.department}")
                print(f"Groups: {user.groups}")
        
        await ldap_connector.close_connections()
    
    # Run test
    asyncio.run(test_ldap())