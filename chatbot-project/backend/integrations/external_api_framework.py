"""
External API Integration Framework for CHaBot System.
Provides unified interface for integrating with external APIs and services.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import uuid
import hashlib
import hmac
import base64
from urllib.parse import urlencode, urlparse
import aiohttp
import aioredis
from fastapi import HTTPException
import jwt
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
import xml.etree.ElementTree as ET
import yaml


class AuthType(Enum):
    NONE = "none"
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer_token"
    BASIC_AUTH = "basic_auth"
    OAUTH2 = "oauth2"
    JWT = "jwt"
    HMAC = "hmac"
    CUSTOM = "custom"


class RateLimitStrategy(Enum):
    FIXED_WINDOW = "fixed_window"
    SLIDING_WINDOW = "sliding_window"
    TOKEN_BUCKET = "token_bucket"
    EXPONENTIAL_BACKOFF = "exponential_backoff"


@dataclass
class APIEndpoint:
    name: str
    url: str
    method: str
    auth_type: AuthType
    auth_config: Dict[str, Any]
    headers: Dict[str, str]
    timeout: int
    retry_config: Dict[str, Any]
    rate_limit: Dict[str, Any]
    cache_config: Dict[str, Any]
    validation_schema: Optional[Dict[str, Any]]
    transformation_rules: List[Dict[str, Any]]


@dataclass
class APIRequest:
    endpoint_name: str
    parameters: Dict[str, Any]
    headers: Dict[str, str]
    body: Optional[Dict[str, Any]]
    metadata: Dict[str, Any]
    request_id: str
    timestamp: datetime


@dataclass
class APIResponse:
    request_id: str
    status_code: int
    headers: Dict[str, str]
    body: Any
    response_time: float
    cached: bool
    timestamp: datetime
    metadata: Dict[str, Any]


class RateLimiter:
    """Rate limiter for API requests."""
    
    def __init__(self, redis_client, strategy: RateLimitStrategy):
        self.redis_client = redis_client
        self.strategy = strategy
        self.logger = logging.getLogger(__name__)
    
    async def check_rate_limit(self, key: str, limit: int, window: int) -> Tuple[bool, Dict[str, Any]]:
        """Check if request is within rate limit."""
        try:
            if self.strategy == RateLimitStrategy.FIXED_WINDOW:
                return await self._fixed_window_check(key, limit, window)
            elif self.strategy == RateLimitStrategy.SLIDING_WINDOW:
                return await self._sliding_window_check(key, limit, window)
            elif self.strategy == RateLimitStrategy.TOKEN_BUCKET:
                return await self._token_bucket_check(key, limit, window)
            else:
                return True, {}
                
        except Exception as e:
            self.logger.error(f"Rate limit check error: {e}")
            return True, {}  # Allow request on error
    
    async def _fixed_window_check(self, key: str, limit: int, window: int) -> Tuple[bool, Dict[str, Any]]:
        """Fixed window rate limiting."""
        current_window = int(datetime.now().timestamp()) // window
        redis_key = f"rate_limit:{key}:{current_window}"
        
        current_count = await self.redis_client.get(redis_key)
        current_count = int(current_count) if current_count else 0
        
        if current_count >= limit:
            return False, {
                'limit': limit,
                'remaining': 0,
                'reset_time': (current_window + 1) * window
            }
        
        # Increment counter
        pipe = self.redis_client.pipeline()
        pipe.incr(redis_key)
        pipe.expire(redis_key, window)
        await pipe.execute()
        
        return True, {
            'limit': limit,
            'remaining': limit - current_count - 1,
            'reset_time': (current_window + 1) * window
        }
    
    async def _sliding_window_check(self, key: str, limit: int, window: int) -> Tuple[bool, Dict[str, Any]]:
        """Sliding window rate limiting."""
        now = datetime.now().timestamp()
        redis_key = f"rate_limit:sliding:{key}"
        
        # Remove old entries
        await self.redis_client.zremrangebyscore(redis_key, 0, now - window)
        
        # Count current requests
        current_count = await self.redis_client.zcard(redis_key)
        
        if current_count >= limit:
            return False, {
                'limit': limit,
                'remaining': 0,
                'reset_time': now + window
            }
        
        # Add current request
        await self.redis_client.zadd(redis_key, {str(uuid.uuid4()): now})
        await self.redis_client.expire(redis_key, window)
        
        return True, {
            'limit': limit,
            'remaining': limit - current_count - 1,
            'reset_time': now + window
        }
    
    async def _token_bucket_check(self, key: str, limit: int, refill_rate: int) -> Tuple[bool, Dict[str, Any]]:
        """Token bucket rate limiting."""
        redis_key = f"rate_limit:bucket:{key}"
        now = datetime.now().timestamp()
        
        # Get current bucket state
        bucket_data = await self.redis_client.hgetall(redis_key)
        
        if bucket_data:
            tokens = float(bucket_data.get('tokens', limit))
            last_refill = float(bucket_data.get('last_refill', now))
        else:
            tokens = limit
            last_refill = now
        
        # Refill tokens
        time_passed = now - last_refill
        tokens_to_add = time_passed * refill_rate
        tokens = min(limit, tokens + tokens_to_add)
        
        if tokens < 1:
            return False, {
                'limit': limit,
                'remaining': int(tokens),
                'reset_time': now + (1 - tokens) / refill_rate
            }
        
        # Consume token
        tokens -= 1
        
        # Update bucket state
        await self.redis_client.hset(redis_key, mapping={
            'tokens': tokens,
            'last_refill': now
        })
        await self.redis_client.expire(redis_key, 3600)  # 1 hour TTL
        
        return True, {
            'limit': limit,
            'remaining': int(tokens),
            'reset_time': now + (limit - tokens) / refill_rate
        }


class APICache:
    """Cache for API responses."""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
    
    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cached response."""
        try:
            cached_data = await self.redis_client.get(f"api_cache:{key}")
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            self.logger.error(f"Cache get error: {e}")
            return None
    
    async def set(self, key: str, data: Dict[str, Any], ttl: int):
        """Cache response."""
        try:
            await self.redis_client.setex(
                f"api_cache:{key}",
                ttl,
                json.dumps(data, default=str)
            )
        except Exception as e:
            self.logger.error(f"Cache set error: {e}")
    
    def generate_cache_key(self, endpoint_name: str, parameters: Dict[str, Any]) -> str:
        """Generate cache key for request."""
        # Create deterministic key from endpoint and parameters
        param_str = json.dumps(parameters, sort_keys=True)
        key_hash = hashlib.md5(f"{endpoint_name}:{param_str}".encode()).hexdigest()
        return key_hash


class AuthenticationManager:
    """Manages authentication for external APIs."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.token_cache = {}
    
    async def authenticate(self, auth_type: AuthType, auth_config: Dict[str, Any], 
                         request_data: Dict[str, Any]) -> Dict[str, str]:
        """Generate authentication headers."""
        try:
            if auth_type == AuthType.API_KEY:
                return await self._api_key_auth(auth_config)
            elif auth_type == AuthType.BEARER_TOKEN:
                return await self._bearer_token_auth(auth_config)
            elif auth_type == AuthType.BASIC_AUTH:
                return await self._basic_auth(auth_config)
            elif auth_type == AuthType.OAUTH2:
                return await self._oauth2_auth(auth_config)
            elif auth_type == AuthType.JWT:
                return await self._jwt_auth(auth_config)
            elif auth_type == AuthType.HMAC:
                return await self._hmac_auth(auth_config, request_data)
            else:
                return {}
                
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {}
    
    async def _api_key_auth(self, config: Dict[str, Any]) -> Dict[str, str]:
        """API key authentication."""
        api_key = config.get('api_key')
        header_name = config.get('header_name', 'X-API-Key')
        
        if api_key:
            return {header_name: api_key}
        return {}
    
    async def _bearer_token_auth(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Bearer token authentication."""
        token = config.get('token')
        
        if token:
            return {'Authorization': f'Bearer {token}'}
        return {}
    
    async def _basic_auth(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Basic authentication."""
        username = config.get('username')
        password = config.get('password')
        
        if username and password:
            credentials = base64.b64encode(f"{username}:{password}".encode()).decode()
            return {'Authorization': f'Basic {credentials}'}
        return {}
    
    async def _oauth2_auth(self, config: Dict[str, Any]) -> Dict[str, str]:
        """OAuth2 authentication."""
        # Check if we have a cached token
        cache_key = f"oauth2:{config.get('client_id')}"
        
        if cache_key in self.token_cache:
            token_data = self.token_cache[cache_key]
            if datetime.now() < token_data['expires_at']:
                return {'Authorization': f'Bearer {token_data["access_token"]}'}
        
        # Get new token
        token_url = config.get('token_url')
        client_id = config.get('client_id')
        client_secret = config.get('client_secret')
        scope = config.get('scope', '')
        
        if not all([token_url, client_id, client_secret]):
            return {}
        
        try:
            async with aiohttp.ClientSession() as session:
                data = {
                    'grant_type': 'client_credentials',
                    'client_id': client_id,
                    'client_secret': client_secret,
                    'scope': scope
                }
                
                async with session.post(token_url, data=data) as response:
                    if response.status == 200:
                        token_data = await response.json()
                        access_token = token_data.get('access_token')
                        expires_in = token_data.get('expires_in', 3600)
                        
                        # Cache token
                        self.token_cache[cache_key] = {
                            'access_token': access_token,
                            'expires_at': datetime.now() + timedelta(seconds=expires_in - 60)
                        }
                        
                        return {'Authorization': f'Bearer {access_token}'}
        except Exception as e:
            self.logger.error(f"OAuth2 token request failed: {e}")
        
        return {}
    
    async def _jwt_auth(self, config: Dict[str, Any]) -> Dict[str, str]:
        """JWT authentication."""
        secret = config.get('secret')
        algorithm = config.get('algorithm', 'HS256')
        payload = config.get('payload', {})
        
        if secret:
            # Add standard claims
            now = datetime.utcnow()
            payload.update({
                'iat': now,
                'exp': now + timedelta(hours=1)
            })
            
            token = jwt.encode(payload, secret, algorithm=algorithm)
            return {'Authorization': f'Bearer {token}'}
        
        return {}
    
    async def _hmac_auth(self, config: Dict[str, Any], request_data: Dict[str, Any]) -> Dict[str, str]:
        """HMAC authentication."""
        secret = config.get('secret')
        algorithm = config.get('algorithm', 'sha256')
        header_name = config.get('header_name', 'X-Signature')
        
        if not secret:
            return {}
        
        # Create signature
        method = request_data.get('method', 'GET')
        url = request_data.get('url', '')
        body = request_data.get('body', '')
        timestamp = str(int(datetime.now().timestamp()))
        
        # Create string to sign
        string_to_sign = f"{method}\n{url}\n{body}\n{timestamp}"
        
        # Generate HMAC
        signature = hmac.new(
            secret.encode(),
            string_to_sign.encode(),
            getattr(hashlib, algorithm)
        ).hexdigest()
        
        return {
            header_name: signature,
            'X-Timestamp': timestamp
        }


class ExternalAPIClient:
    """Client for making external API requests."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.logger = logging.getLogger(__name__)
        self.endpoints: Dict[str, APIEndpoint] = {}
        
        # Initialize components
        self.redis_client = None
        self.rate_limiter = None
        self.cache = None
        self.auth_manager = AuthenticationManager()
        
        # Request tracking
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'cached_responses': 0
        }
        
        # Initialize Redis connection
        asyncio.create_task(self._initialize_redis(redis_url))
    
    async def _initialize_redis(self, redis_url: str):
        """Initialize Redis connection."""
        try:
            self.redis_client = aioredis.from_url(redis_url)
            self.rate_limiter = RateLimiter(self.redis_client, RateLimitStrategy.SLIDING_WINDOW)
            self.cache = APICache(self.redis_client)
            self.logger.info("Redis connection initialized for external API client")
        except Exception as e:
            self.logger.error(f"Failed to initialize Redis: {e}")
    
    def register_endpoint(self, endpoint: APIEndpoint):
        """Register API endpoint."""
        self.endpoints[endpoint.name] = endpoint
        self.logger.info(f"Registered API endpoint: {endpoint.name}")
    
    def load_endpoints_from_config(self, config_path: str):
        """Load endpoints from configuration file."""
        try:
            with open(config_path, 'r') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    config = yaml.safe_load(f)
                else:
                    config = json.load(f)
            
            for endpoint_config in config.get('endpoints', []):
                endpoint = APIEndpoint(
                    name=endpoint_config['name'],
                    url=endpoint_config['url'],
                    method=endpoint_config.get('method', 'GET'),
                    auth_type=AuthType(endpoint_config.get('auth_type', 'none')),
                    auth_config=endpoint_config.get('auth_config', {}),
                    headers=endpoint_config.get('headers', {}),
                    timeout=endpoint_config.get('timeout', 30),
                    retry_config=endpoint_config.get('retry_config', {}),
                    rate_limit=endpoint_config.get('rate_limit', {}),
                    cache_config=endpoint_config.get('cache_config', {}),
                    validation_schema=endpoint_config.get('validation_schema'),
                    transformation_rules=endpoint_config.get('transformation_rules', [])
                )
                self.register_endpoint(endpoint)
                
        except Exception as e:
            self.logger.error(f"Failed to load endpoints from config: {e}")
    
    async def make_request(self, endpoint_name: str, parameters: Dict[str, Any] = None,
                         headers: Dict[str, str] = None, body: Dict[str, Any] = None,
                         metadata: Dict[str, Any] = None) -> APIResponse:
        """Make API request."""
        if endpoint_name not in self.endpoints:
            raise ValueError(f"Unknown endpoint: {endpoint_name}")
        
        endpoint = self.endpoints[endpoint_name]
        request_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        # Create request object
        api_request = APIRequest(
            endpoint_name=endpoint_name,
            parameters=parameters or {},
            headers=headers or {},
            body=body,
            metadata=metadata or {},
            request_id=request_id,
            timestamp=start_time
        )
        
        try:
            # Check rate limit
            if endpoint.rate_limit:
                rate_limit_key = f"{endpoint_name}:{metadata.get('user_id', 'anonymous')}"
                allowed, rate_info = await self.rate_limiter.check_rate_limit(
                    rate_limit_key,
                    endpoint.rate_limit.get('limit', 100),
                    endpoint.rate_limit.get('window', 3600)
                )
                
                if not allowed:
                    raise HTTPException(
                        status_code=429,
                        detail=f"Rate limit exceeded. Reset at: {rate_info.get('reset_time')}"
                    )
            
            # Check cache
            cached_response = None
            if endpoint.cache_config.get('enabled', False):
                cache_key = self.cache.generate_cache_key(endpoint_name, parameters or {})
                cached_response = await self.cache.get(cache_key)
                
                if cached_response:
                    self.request_stats['cached_responses'] += 1
                    return APIResponse(
                        request_id=request_id,
                        status_code=cached_response['status_code'],
                        headers=cached_response['headers'],
                        body=cached_response['body'],
                        response_time=0.0,
                        cached=True,
                        timestamp=datetime.now(),
                        metadata=cached_response.get('metadata', {})
                    )
            
            # Make HTTP request
            response = await self._execute_request(endpoint, api_request)
            
            # Cache response if configured
            if endpoint.cache_config.get('enabled', False) and response.status_code == 200:
                cache_key = self.cache.generate_cache_key(endpoint_name, parameters or {})
                cache_data = {
                    'status_code': response.status_code,
                    'headers': response.headers,
                    'body': response.body,
                    'metadata': response.metadata
                }
                await self.cache.set(
                    cache_key,
                    cache_data,
                    endpoint.cache_config.get('ttl', 300)
                )
            
            # Update statistics
            self.request_stats['total_requests'] += 1
            if response.status_code < 400:
                self.request_stats['successful_requests'] += 1
            else:
                self.request_stats['failed_requests'] += 1
            
            return response
            
        except Exception as e:
            self.request_stats['total_requests'] += 1
            self.request_stats['failed_requests'] += 1
            self.logger.error(f"API request failed: {e}")
            raise
    
    async def _execute_request(self, endpoint: APIEndpoint, request: APIRequest) -> APIResponse:
        """Execute HTTP request."""
        start_time = datetime.now()
        
        # Prepare URL
        url = endpoint.url
        if request.parameters and endpoint.method.upper() == 'GET':
            url += '?' + urlencode(request.parameters)
        
        # Prepare headers
        headers = {**endpoint.headers, **request.headers}
        
        # Add authentication headers
        auth_headers = await self.auth_manager.authenticate(
            endpoint.auth_type,
            endpoint.auth_config,
            {
                'method': endpoint.method,
                'url': url,
                'body': json.dumps(request.body) if request.body else ''
            }
        )
        headers.update(auth_headers)
        
        # Prepare request data
        request_kwargs = {
            'method': endpoint.method.upper(),
            'url': url,
            'headers': headers,
            'timeout': aiohttp.ClientTimeout(total=endpoint.timeout)
        }
        
        if request.body and endpoint.method.upper() in ['POST', 'PUT', 'PATCH']:
            if headers.get('Content-Type', '').startswith('application/json'):
                request_kwargs['json'] = request.body
            else:
                request_kwargs['data'] = request.body
        
        # Execute request with retry logic
        retry_config = endpoint.retry_config
        max_retries = retry_config.get('max_retries', 3)
        retry_delay = retry_config.get('delay', 1)
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.request(**request_kwargs) as response:
                        response_time = (datetime.now() - start_time).total_seconds()
                        
                        # Read response body
                        content_type = response.headers.get('Content-Type', '')
                        if 'application/json' in content_type:
                            body = await response.json()
                        elif 'application/xml' in content_type or 'text/xml' in content_type:
                            text = await response.text()
                            body = self._parse_xml(text)
                        else:
                            body = await response.text()
                        
                        # Apply transformations
                        if endpoint.transformation_rules:
                            body = self._apply_transformations(body, endpoint.transformation_rules)
                        
                        # Validate response
                        if endpoint.validation_schema:
                            self._validate_response(body, endpoint.validation_schema)
                        
                        return APIResponse(
                            request_id=request.request_id,
                            status_code=response.status,
                            headers=dict(response.headers),
                            body=body,
                            response_time=response_time,
                            cached=False,
                            timestamp=datetime.now(),
                            metadata={}
                        )
                        
            except Exception as e:
                last_exception = e
                
                if attempt < max_retries:
                    # Exponential backoff
                    delay = retry_delay * (2 ** attempt)
                    await asyncio.sleep(delay)
                    self.logger.warning(f"Request failed, retrying in {delay}s: {e}")
                else:
                    self.logger.error(f"Request failed after {max_retries} retries: {e}")
        
        # If we get here, all retries failed
        raise last_exception
    
    def _parse_xml(self, xml_text: str) -> Dict[str, Any]:
        """Parse XML response to dictionary."""
        try:
            root = ET.fromstring(xml_text)
            return self._xml_to_dict(root)
        except Exception as e:
            self.logger.error(f"XML parsing error: {e}")
            return {'raw_xml': xml_text}
    
    def _xml_to_dict(self, element) -> Dict[str, Any]:
        """Convert XML element to dictionary."""
        result = {}
        
        # Add attributes
        if element.attrib:
            result['@attributes'] = element.attrib
        
        # Add text content
        if element.text and element.text.strip():
            if len(element) == 0:
                return element.text.strip()
            result['#text'] = element.text.strip()
        
        # Add child elements
        for child in element:
            child_data = self._xml_to_dict(child)
            
            if child.tag in result:
                # Convert to list if multiple elements with same tag
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data
        
        return result
    
    def _apply_transformations(self, data: Any, rules: List[Dict[str, Any]]) -> Any:
        """Apply transformation rules to response data."""
        for rule in rules:
            rule_type = rule.get('type')
            
            if rule_type == 'extract':
                # Extract specific fields
                path = rule.get('path', '')
                data = self._extract_by_path(data, path)
            
            elif rule_type == 'rename':
                # Rename fields
                mappings = rule.get('mappings', {})
                data = self._rename_fields(data, mappings)
            
            elif rule_type == 'filter':
                # Filter data
                condition = rule.get('condition', {})
                data = self._filter_data(data, condition)
            
            elif rule_type == 'transform':
                # Custom transformation
                function = rule.get('function')
                if function:
                    data = self._apply_custom_transform(data, function)
        
        return data
    
    def _extract_by_path(self, data: Any, path: str) -> Any:
        """Extract data by path (e.g., 'response.data.items')."""
        if not path:
            return data
        
        parts = path.split('.')
        current = data
        
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            elif isinstance(current, list) and part.isdigit():
                index = int(part)
                if 0 <= index < len(current):
                    current = current[index]
                else:
                    return None
            else:
                return None
        
        return current
    
    def _rename_fields(self, data: Any, mappings: Dict[str, str]) -> Any:
        """Rename fields in data."""
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                new_key = mappings.get(key, key)
                result[new_key] = self._rename_fields(value, mappings)
            return result
        elif isinstance(data, list):
            return [self._rename_fields(item, mappings) for item in data]
        else:
            return data
    
    def _filter_data(self, data: Any, condition: Dict[str, Any]) -> Any:
        """Filter data based on condition."""
        # Simplified filtering - can be extended
        if isinstance(data, list):
            field = condition.get('field')
            value = condition.get('value')
            operator = condition.get('operator', 'eq')
            
            if field and value is not None:
                filtered = []
                for item in data:
                    if isinstance(item, dict) and field in item:
                        item_value = item[field]
                        
                        if operator == 'eq' and item_value == value:
                            filtered.append(item)
                        elif operator == 'ne' and item_value != value:
                            filtered.append(item)
                        elif operator == 'gt' and item_value > value:
                            filtered.append(item)
                        elif operator == 'lt' and item_value < value:
                            filtered.append(item)
                
                return filtered
        
        return data
    
    def _apply_custom_transform(self, data: Any, function: str) -> Any:
        """Apply custom transformation function."""
        # This would typically load and execute custom transformation functions
        # For security, this should be implemented with proper sandboxing
        self.logger.warning(f"Custom transformation not implemented: {function}")
        return data
    
    def _validate_response(self, data: Any, schema: Dict[str, Any]):
        """Validate response against schema."""
        # Simplified validation - can be extended with jsonschema
        required_fields = schema.get('required', [])
        
        if isinstance(data, dict):
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"Required field missing: {field}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get API client statistics."""
        return {
            **self.request_stats,
            'registered_endpoints': len(self.endpoints),
            'endpoint_names': list(self.endpoints.keys())
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on registered endpoints."""
        results = {}
        
        for endpoint_name, endpoint in self.endpoints.items():
            try:
                # Simple health check request
                response = await self.make_request(endpoint_name, {})
                results[endpoint_name] = {
                    'status': 'healthy' if response.status_code < 400 else 'unhealthy',
                    'response_time': response.response_time,
                    'status_code': response.status_code
                }
            except Exception as e:
                results[endpoint_name] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        return results


# Example configuration
EXAMPLE_CONFIG = {
    "endpoints": [
        {
            "name": "weather_api",
            "url": "https://api.openweathermap.org/data/2.5/weather",
            "method": "GET",
            "auth_type": "api_key",
            "auth_config": {
                "api_key": "your-api-key",
                "header_name": "X-API-Key"
            },
            "headers": {
                "Content-Type": "application/json"
            },
            "timeout": 30,
            "retry_config": {
                "max_retries": 3,
                "delay": 1
            },
            "rate_limit": {
                "limit": 100,
                "window": 3600
            },
            "cache_config": {
                "enabled": True,
                "ttl": 300
            },
            "transformation_rules": [
                {
                    "type": "extract",
                    "path": "main"
                }
            ]
        }
    ]
}


if __name__ == "__main__":
    # Example usage
    client = ExternalAPIClient()
    
    # Load configuration
    # client.load_endpoints_from_config("api_config.yaml")
    
    # Or register endpoints programmatically
    endpoint = APIEndpoint(
        name="test_api",
        url="https://httpbin.org/get",
        method="GET",
        auth_type=AuthType.NONE,
        auth_config={},
        headers={},
        timeout=30,
        retry_config={},
        rate_limit={},
        cache_config={},
        validation_schema=None,
        transformation_rules=[]
    )
    
    client.register_endpoint(endpoint)
    
    # Make request
    # response = await client.make_request("test_api", {"param": "value"})