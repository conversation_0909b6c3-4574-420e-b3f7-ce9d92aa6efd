"""Feedback processing and learning system."""

from typing import Dict, Any, List
from datetime import datetime
import json

class FeedbackProcessor:
    def __init__(self):
        self.feedback_data = []
        self.learning_patterns = {}
    
    def process_feedback(self, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """Process user feedback."""
        processed_feedback = {
            "id": len(self.feedback_data) + 1,
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": feedback.get("user_id"),
            "query": feedback.get("query"),
            "response": feedback.get("response"),
            "rating": feedback.get("rating", 0),
            "helpful": feedback.get("helpful", False),
            "issues": feedback.get("issues", []),
            "suggestions": feedback.get("suggestions", "")
        }
        
        self.feedback_data.append(processed_feedback)
        self._update_learning_patterns(processed_feedback)
        
        return {"success": True, "feedback_id": processed_feedback["id"]}
    
    def _update_learning_patterns(self, feedback: Dict[str, Any]):
        """Update learning patterns based on feedback."""
        query_type = self._classify_query(feedback["query"])
        
        if query_type not in self.learning_patterns:
            self.learning_patterns[query_type] = {
                "total_feedback": 0,
                "avg_rating": 0,
                "common_issues": {},
                "improvement_suggestions": []
            }
        
        pattern = self.learning_patterns[query_type]
        pattern["total_feedback"] += 1
        
        # Update average rating
        current_avg = pattern["avg_rating"]
        new_rating = feedback["rating"]
        pattern["avg_rating"] = (current_avg + new_rating) / 2
        
        # Track common issues
        for issue in feedback["issues"]:
            if issue in pattern["common_issues"]:
                pattern["common_issues"][issue] += 1
            else:
                pattern["common_issues"][issue] = 1
    
    def _classify_query(self, query: str) -> str:
        """Classify query type for pattern analysis."""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["policy", "rule", "procedure"]):
            return "policy_query"
        elif any(word in query_lower for word in ["calculate", "compute", "math"]):
            return "calculation_query"
        elif any(word in query_lower for word in ["what", "how", "why", "explain"]):
            return "information_query"
        else:
            return "general_query"
    
    def get_improvement_recommendations(self) -> List[Dict[str, Any]]:
        """Generate improvement recommendations."""
        recommendations = []
        
        for query_type, pattern in self.learning_patterns.items():
            if pattern["avg_rating"] < 3.0 and pattern["total_feedback"] > 5:
                recommendations.append({
                    "query_type": query_type,
                    "priority": "high" if pattern["avg_rating"] < 2.0 else "medium",
                    "avg_rating": pattern["avg_rating"],
                    "recommendation": f"Improve {query_type} handling"
                })
        
        return recommendations

# Global instances
feedback_processor = FeedbackProcessor()