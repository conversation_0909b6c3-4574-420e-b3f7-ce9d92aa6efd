"""
Blue-Green Deployment Manager for CHaBot Production System.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import kubernetes
from kubernetes import client, config
import httpx
import yaml


class DeploymentStatus(Enum):
    PENDING = "pending"
    DEPLOYING = "deploying"
    TESTING = "testing"
    READY = "ready"
    ACTIVE = "active"
    FAILED = "failed"
    ROLLING_BACK = "rolling_back"


@dataclass
class Environment:
    name: str
    namespace: str
    status: DeploymentStatus
    version: str
    replicas: int
    health_check_url: str
    last_updated: datetime
    traffic_percentage: int = 0


class BlueGreenDeploymentManager:
    """Manages blue-green deployments for CHaBot system."""
    
    def __init__(self, config_file: str = None):
        self.logger = logging.getLogger(__name__)
        self.k8s_client = None
        self.apps_v1 = None
        self.core_v1 = None
        self.networking_v1 = None
        
        # Environment configurations
        self.blue_env = Environment(
            name="blue",
            namespace="chabot-blue",
            status=DeploymentStatus.PENDING,
            version="",
            replicas=3,
            health_check_url="http://chabot-blue-service:8000/health",
            last_updated=datetime.now()
        )
        
        self.green_env = Environment(
            name="green",
            namespace="chabot-green",
            status=DeploymentStatus.PENDING,
            version="",
            replicas=3,
            health_check_url="http://chabot-green-service:8000/health",
            last_updated=datetime.now()
        )
        
        self.current_active = None
        self.deployment_config = self.load_deployment_config(config_file)
        
        # Initialize Kubernetes client
        self.init_kubernetes_client()
    
    def load_deployment_config(self, config_file: str) -> Dict[str, Any]:
        """Load deployment configuration."""
        default_config = {
            "health_check_timeout": 300,  # 5 minutes
            "health_check_interval": 10,  # 10 seconds
            "traffic_switch_delay": 30,   # 30 seconds
            "rollback_timeout": 600,      # 10 minutes
            "min_healthy_replicas": 2,
            "services": {
                "api_gateway": {"port": 8000, "replicas": 3},
                "auth_service": {"port": 8001, "replicas": 2},
                "chat_service": {"port": 8002, "replicas": 3},
                "agent_service": {"port": 8003, "replicas": 4},
                "admin_service": {"port": 8004, "replicas": 2}
            },
            "databases": {
                "postgres": {"enabled": True, "backup_before_deploy": True},
                "neo4j": {"enabled": True, "backup_before_deploy": True},
                "milvus": {"enabled": True, "backup_before_deploy": False},
                "redis": {"enabled": True, "backup_before_deploy": False}
            }
        }
        
        if config_file:
            try:
                with open(config_file, 'r') as f:
                    user_config = yaml.safe_load(f)
                    default_config.update(user_config)
            except Exception as e:
                self.logger.warning(f"Could not load config file {config_file}: {e}")
        
        return default_config
    
    def init_kubernetes_client(self):
        """Initialize Kubernetes client."""
        try:
            # Try to load in-cluster config first
            config.load_incluster_config()
        except:
            try:
                # Fall back to local kubeconfig
                config.load_kube_config()
            except Exception as e:
                self.logger.error(f"Failed to load Kubernetes config: {e}")
                return
        
        self.k8s_client = client.ApiClient()
        self.apps_v1 = client.AppsV1Api()
        self.core_v1 = client.CoreV1Api()
        self.networking_v1 = client.NetworkingV1Api()
    
    async def deploy_new_version(self, version: str, image_tag: str) -> bool:
        """Deploy new version using blue-green strategy."""
        self.logger.info(f"Starting blue-green deployment for version {version}")
        
        try:
            # Determine target environment
            target_env = self.get_inactive_environment()
            if not target_env:
                self.logger.error("No inactive environment available")
                return False
            
            self.logger.info(f"Deploying to {target_env.name} environment")
            
            # Step 1: Backup databases if configured
            if not await self.backup_databases():
                self.logger.error("Database backup failed")
                return False
            
            # Step 2: Deploy to target environment
            target_env.status = DeploymentStatus.DEPLOYING
            target_env.version = version
            target_env.last_updated = datetime.now()
            
            if not await self.deploy_to_environment(target_env, image_tag):
                target_env.status = DeploymentStatus.FAILED
                return False
            
            # Step 3: Wait for deployment to be ready
            if not await self.wait_for_deployment_ready(target_env):
                target_env.status = DeploymentStatus.FAILED
                return False
            
            # Step 4: Run health checks
            target_env.status = DeploymentStatus.TESTING
            if not await self.run_health_checks(target_env):
                target_env.status = DeploymentStatus.FAILED
                return False
            
            # Step 5: Run smoke tests
            if not await self.run_smoke_tests(target_env):
                target_env.status = DeploymentStatus.FAILED
                return False
            
            target_env.status = DeploymentStatus.READY
            
            # Step 6: Switch traffic
            if not await self.switch_traffic(target_env):
                self.logger.error("Traffic switch failed")
                await self.rollback_deployment(target_env)
                return False
            
            target_env.status = DeploymentStatus.ACTIVE
            target_env.traffic_percentage = 100
            
            # Step 7: Update current active environment
            if self.current_active:
                self.current_active.status = DeploymentStatus.PENDING
                self.current_active.traffic_percentage = 0
            
            self.current_active = target_env
            
            self.logger.info(f"Successfully deployed version {version} to {target_env.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Deployment failed: {e}")
            if target_env:
                await self.rollback_deployment(target_env)
            return False
    
    def get_inactive_environment(self) -> Optional[Environment]:
        """Get the inactive environment for deployment."""
        if self.current_active is None:
            return self.blue_env
        
        if self.current_active.name == "blue":
            return self.green_env
        else:
            return self.blue_env
    
    async def backup_databases(self) -> bool:
        """Backup databases before deployment."""
        self.logger.info("Starting database backups")
        
        backup_tasks = []
        
        for db_name, db_config in self.deployment_config["databases"].items():
            if db_config.get("enabled") and db_config.get("backup_before_deploy"):
                backup_tasks.append(self.backup_database(db_name))
        
        if backup_tasks:
            results = await asyncio.gather(*backup_tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"Database backup failed: {result}")
                    return False
        
        self.logger.info("Database backups completed successfully")
        return True
    
    async def backup_database(self, db_name: str) -> bool:
        """Backup a specific database."""
        self.logger.info(f"Backing up {db_name} database")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{db_name}_backup_{timestamp}"
        
        try:
            if db_name == "postgres":
                return await self.backup_postgres(backup_name)
            elif db_name == "neo4j":
                return await self.backup_neo4j(backup_name)
            elif db_name == "milvus":
                return await self.backup_milvus(backup_name)
            elif db_name == "redis":
                return await self.backup_redis(backup_name)
            else:
                self.logger.warning(f"Unknown database type: {db_name}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to backup {db_name}: {e}")
            return False
    
    async def backup_postgres(self, backup_name: str) -> bool:
        """Backup PostgreSQL database."""
        # Implementation would use pg_dump via kubectl exec
        self.logger.info(f"PostgreSQL backup: {backup_name}")
        # Simulate backup
        await asyncio.sleep(2)
        return True
    
    async def backup_neo4j(self, backup_name: str) -> bool:
        """Backup Neo4j database."""
        # Implementation would use neo4j-admin backup
        self.logger.info(f"Neo4j backup: {backup_name}")
        # Simulate backup
        await asyncio.sleep(2)
        return True
    
    async def backup_milvus(self, backup_name: str) -> bool:
        """Backup Milvus database."""
        # Implementation would use Milvus backup utilities
        self.logger.info(f"Milvus backup: {backup_name}")
        # Simulate backup
        await asyncio.sleep(1)
        return True
    
    async def backup_redis(self, backup_name: str) -> bool:
        """Backup Redis database."""
        # Implementation would use Redis BGSAVE
        self.logger.info(f"Redis backup: {backup_name}")
        # Simulate backup
        await asyncio.sleep(1)
        return True
    
    async def deploy_to_environment(self, env: Environment, image_tag: str) -> bool:
        """Deploy services to target environment."""
        self.logger.info(f"Deploying services to {env.name} environment")
        
        try:
            # Create namespace if it doesn't exist
            await self.ensure_namespace(env.namespace)
            
            # Deploy each service
            for service_name, service_config in self.deployment_config["services"].items():
                if not await self.deploy_service(env, service_name, service_config, image_tag):
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to deploy to {env.name}: {e}")
            return False
    
    async def ensure_namespace(self, namespace: str):
        """Ensure namespace exists."""
        try:
            self.core_v1.read_namespace(namespace)
        except client.exceptions.ApiException as e:
            if e.status == 404:
                # Create namespace
                namespace_manifest = client.V1Namespace(
                    metadata=client.V1ObjectMeta(name=namespace)
                )
                self.core_v1.create_namespace(namespace_manifest)
                self.logger.info(f"Created namespace: {namespace}")
    
    async def deploy_service(self, env: Environment, service_name: str, 
                           service_config: Dict[str, Any], image_tag: str) -> bool:
        """Deploy a specific service."""
        self.logger.info(f"Deploying {service_name} to {env.namespace}")
        
        try:
            # Create deployment manifest
            deployment = self.create_deployment_manifest(
                env, service_name, service_config, image_tag
            )
            
            # Create or update deployment
            try:
                self.apps_v1.read_namespaced_deployment(
                    name=f"chabot-{service_name}",
                    namespace=env.namespace
                )
                # Update existing deployment
                self.apps_v1.patch_namespaced_deployment(
                    name=f"chabot-{service_name}",
                    namespace=env.namespace,
                    body=deployment
                )
            except client.exceptions.ApiException as e:
                if e.status == 404:
                    # Create new deployment
                    self.apps_v1.create_namespaced_deployment(
                        namespace=env.namespace,
                        body=deployment
                    )
            
            # Create service if it doesn't exist
            await self.ensure_service(env, service_name, service_config)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to deploy {service_name}: {e}")
            return False
    
    def create_deployment_manifest(self, env: Environment, service_name: str,
                                 service_config: Dict[str, Any], image_tag: str) -> client.V1Deployment:
        """Create Kubernetes deployment manifest."""
        labels = {
            "app": f"chabot-{service_name}",
            "environment": env.name,
            "version": env.version
        }
        
        container = client.V1Container(
            name=f"chabot-{service_name}",
            image=f"chabot/{service_name}:{image_tag}",
            ports=[client.V1ContainerPort(container_port=service_config["port"])],
            env=[
                client.V1EnvVar(name="ENVIRONMENT", value=env.name),
                client.V1EnvVar(name="VERSION", value=env.version)
            ],
            liveness_probe=client.V1Probe(
                http_get=client.V1HTTPGetAction(
                    path="/health",
                    port=service_config["port"]
                ),
                initial_delay_seconds=30,
                period_seconds=10
            ),
            readiness_probe=client.V1Probe(
                http_get=client.V1HTTPGetAction(
                    path="/ready",
                    port=service_config["port"]
                ),
                initial_delay_seconds=5,
                period_seconds=5
            ),
            resources=client.V1ResourceRequirements(
                requests={"cpu": "100m", "memory": "256Mi"},
                limits={"cpu": "500m", "memory": "512Mi"}
            )
        )
        
        pod_spec = client.V1PodSpec(containers=[container])
        
        pod_template = client.V1PodTemplateSpec(
            metadata=client.V1ObjectMeta(labels=labels),
            spec=pod_spec
        )
        
        deployment_spec = client.V1DeploymentSpec(
            replicas=service_config["replicas"],
            selector=client.V1LabelSelector(match_labels=labels),
            template=pod_template
        )
        
        deployment = client.V1Deployment(
            api_version="apps/v1",
            kind="Deployment",
            metadata=client.V1ObjectMeta(
                name=f"chabot-{service_name}",
                namespace=env.namespace,
                labels=labels
            ),
            spec=deployment_spec
        )
        
        return deployment
    
    async def ensure_service(self, env: Environment, service_name: str, 
                           service_config: Dict[str, Any]):
        """Ensure Kubernetes service exists."""
        service_manifest = client.V1Service(
            api_version="v1",
            kind="Service",
            metadata=client.V1ObjectMeta(
                name=f"chabot-{service_name}-service",
                namespace=env.namespace
            ),
            spec=client.V1ServiceSpec(
                selector={"app": f"chabot-{service_name}"},
                ports=[client.V1ServicePort(
                    port=service_config["port"],
                    target_port=service_config["port"]
                )]
            )
        )
        
        try:
            self.core_v1.read_namespaced_service(
                name=f"chabot-{service_name}-service",
                namespace=env.namespace
            )
        except client.exceptions.ApiException as e:
            if e.status == 404:
                self.core_v1.create_namespaced_service(
                    namespace=env.namespace,
                    body=service_manifest
                )
    
    async def wait_for_deployment_ready(self, env: Environment) -> bool:
        """Wait for all deployments to be ready."""
        self.logger.info(f"Waiting for {env.name} deployments to be ready")
        
        timeout = self.deployment_config["health_check_timeout"]
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            all_ready = True
            
            for service_name in self.deployment_config["services"].keys():
                deployment_name = f"chabot-{service_name}"
                
                try:
                    deployment = self.apps_v1.read_namespaced_deployment(
                        name=deployment_name,
                        namespace=env.namespace
                    )
                    
                    if not self.is_deployment_ready(deployment):
                        all_ready = False
                        break
                        
                except Exception as e:
                    self.logger.error(f"Error checking deployment {deployment_name}: {e}")
                    all_ready = False
                    break
            
            if all_ready:
                self.logger.info(f"All deployments in {env.name} are ready")
                return True
            
            await asyncio.sleep(self.deployment_config["health_check_interval"])
        
        self.logger.error(f"Timeout waiting for {env.name} deployments to be ready")
        return False
    
    def is_deployment_ready(self, deployment: client.V1Deployment) -> bool:
        """Check if deployment is ready."""
        status = deployment.status
        
        if not status:
            return False
        
        desired_replicas = deployment.spec.replicas
        ready_replicas = status.ready_replicas or 0
        
        return ready_replicas >= desired_replicas
    
    async def run_health_checks(self, env: Environment) -> bool:
        """Run health checks on deployed services."""
        self.logger.info(f"Running health checks for {env.name}")
        
        health_check_tasks = []
        
        for service_name, service_config in self.deployment_config["services"].items():
            health_url = f"http://chabot-{service_name}-service.{env.namespace}:{service_config['port']}/health"
            health_check_tasks.append(self.check_service_health(service_name, health_url))
        
        results = await asyncio.gather(*health_check_tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            service_name = list(self.deployment_config["services"].keys())[i]
            if isinstance(result, Exception) or not result:
                self.logger.error(f"Health check failed for {service_name}: {result}")
                return False
        
        self.logger.info(f"All health checks passed for {env.name}")
        return True
    
    async def check_service_health(self, service_name: str, health_url: str) -> bool:
        """Check health of a specific service."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(health_url)
                
                if response.status_code == 200:
                    health_data = response.json()
                    return health_data.get("status") == "healthy"
                else:
                    self.logger.warning(f"Health check for {service_name} returned {response.status_code}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Health check failed for {service_name}: {e}")
            return False
    
    async def run_smoke_tests(self, env: Environment) -> bool:
        """Run smoke tests on deployed environment."""
        self.logger.info(f"Running smoke tests for {env.name}")
        
        # Basic smoke tests
        smoke_tests = [
            self.test_api_gateway(env),
            self.test_authentication(env),
            self.test_chat_functionality(env),
            self.test_agent_coordination(env)
        ]
        
        results = await asyncio.gather(*smoke_tests, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception) or not result:
                self.logger.error(f"Smoke test {i} failed: {result}")
                return False
        
        self.logger.info(f"All smoke tests passed for {env.name}")
        return True
    
    async def test_api_gateway(self, env: Environment) -> bool:
        """Test API gateway functionality."""
        try:
            gateway_url = f"http://chabot-api-gateway-service.{env.namespace}:8000"
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{gateway_url}/health")
                return response.status_code == 200
                
        except Exception as e:
            self.logger.error(f"API gateway test failed: {e}")
            return False
    
    async def test_authentication(self, env: Environment) -> bool:
        """Test authentication service."""
        try:
            auth_url = f"http://chabot-auth-service-service.{env.namespace}:8001"
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{auth_url}/health")
                return response.status_code == 200
                
        except Exception as e:
            self.logger.error(f"Authentication test failed: {e}")
            return False
    
    async def test_chat_functionality(self, env: Environment) -> bool:
        """Test basic chat functionality."""
        try:
            chat_url = f"http://chabot-chat-service-service.{env.namespace}:8002"
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{chat_url}/health")
                return response.status_code == 200
                
        except Exception as e:
            self.logger.error(f"Chat functionality test failed: {e}")
            return False
    
    async def test_agent_coordination(self, env: Environment) -> bool:
        """Test agent coordination."""
        try:
            agent_url = f"http://chabot-agent-service-service.{env.namespace}:8003"
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{agent_url}/health")
                return response.status_code == 200
                
        except Exception as e:
            self.logger.error(f"Agent coordination test failed: {e}")
            return False
    
    async def switch_traffic(self, target_env: Environment) -> bool:
        """Switch traffic to target environment."""
        self.logger.info(f"Switching traffic to {target_env.name}")
        
        try:
            # Update ingress or load balancer configuration
            # This would typically involve updating Kubernetes Ingress resources
            # or external load balancer configurations
            
            # Simulate traffic switch
            await asyncio.sleep(self.deployment_config["traffic_switch_delay"])
            
            self.logger.info(f"Traffic switched to {target_env.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to switch traffic: {e}")
            return False
    
    async def rollback_deployment(self, failed_env: Environment) -> bool:
        """Rollback failed deployment."""
        self.logger.info(f"Rolling back deployment in {failed_env.name}")
        
        failed_env.status = DeploymentStatus.ROLLING_BACK
        
        try:
            # If there's an active environment, ensure traffic is routed there
            if self.current_active and self.current_active != failed_env:
                await self.switch_traffic(self.current_active)
            
            # Clean up failed deployment
            await self.cleanup_environment(failed_env)
            
            failed_env.status = DeploymentStatus.FAILED
            self.logger.info(f"Rollback completed for {failed_env.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Rollback failed: {e}")
            return False
    
    async def cleanup_environment(self, env: Environment):
        """Clean up environment resources."""
        self.logger.info(f"Cleaning up {env.name} environment")
        
        try:
            # Delete deployments
            for service_name in self.deployment_config["services"].keys():
                try:
                    self.apps_v1.delete_namespaced_deployment(
                        name=f"chabot-{service_name}",
                        namespace=env.namespace
                    )
                except client.exceptions.ApiException as e:
                    if e.status != 404:  # Ignore not found errors
                        self.logger.warning(f"Error deleting deployment {service_name}: {e}")
            
            # Services can be left for next deployment
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def get_deployment_status(self) -> Dict[str, Any]:
        """Get current deployment status."""
        return {
            "blue_environment": {
                "status": self.blue_env.status.value,
                "version": self.blue_env.version,
                "traffic_percentage": self.blue_env.traffic_percentage,
                "last_updated": self.blue_env.last_updated.isoformat()
            },
            "green_environment": {
                "status": self.green_env.status.value,
                "version": self.green_env.version,
                "traffic_percentage": self.green_env.traffic_percentage,
                "last_updated": self.green_env.last_updated.isoformat()
            },
            "current_active": self.current_active.name if self.current_active else None
        }


# CLI interface for deployment management
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="CHaBot Blue-Green Deployment Manager")
    parser.add_argument("action", choices=["deploy", "status", "rollback"], help="Action to perform")
    parser.add_argument("--version", help="Version to deploy")
    parser.add_argument("--image-tag", help="Docker image tag")
    parser.add_argument("--config", help="Configuration file path")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    async def main():
        manager = BlueGreenDeploymentManager(args.config)
        
        if args.action == "deploy":
            if not args.version or not args.image_tag:
                print("Version and image-tag are required for deployment")
                return
            
            success = await manager.deploy_new_version(args.version, args.image_tag)
            if success:
                print(f"Deployment of version {args.version} completed successfully")
            else:
                print(f"Deployment of version {args.version} failed")
        
        elif args.action == "status":
            status = manager.get_deployment_status()
            print(json.dumps(status, indent=2))
        
        elif args.action == "rollback":
            # Implementation for rollback command
            print("Rollback functionality would be implemented here")
    
    asyncio.run(main())