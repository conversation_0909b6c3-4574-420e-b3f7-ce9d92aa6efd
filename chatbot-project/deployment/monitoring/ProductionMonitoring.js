class ProductionMonitoring {
    constructor() {
        this.alerts = new Map();
        this.dashboards = new Map();
        this.anomalyDetectors = new Map();
        this.metrics = new Map();
        this.alertHistory = [];
        this.initializeMonitoring();
    }

    // Initialize monitoring system
    initializeMonitoring() {
        this.setupRealTimeAlerting();
        this.createPerformanceDashboards();
        this.buildAnomalyDetection();
    }

    // Set up real-time alerting
    setupRealTimeAlerting() {
        const alertConfigs = {
            high_cpu: {
                name: 'High CPU Usage',
                metric: 'cpu_usage',
                threshold: 80,
                operator: '>',
                duration: 300, // 5 minutes
                severity: 'warning',
                channels: ['email', 'slack', 'pagerduty']
            },
            high_memory: {
                name: 'High Memory Usage',
                metric: 'memory_usage',
                threshold: 85,
                operator: '>',
                duration: 300,
                severity: 'warning',
                channels: ['email', 'slack']
            },
            error_rate: {
                name: 'High Error Rate',
                metric: 'error_rate',
                threshold: 5,
                operator: '>',
                duration: 60, // 1 minute
                severity: 'critical',
                channels: ['email', 'slack', 'pagerduty', 'sms']
            },
            response_time: {
                name: 'High Response Time',
                metric: 'response_time',
                threshold: 2000, // 2 seconds
                operator: '>',
                duration: 180, // 3 minutes
                severity: 'warning',
                channels: ['email', 'slack']
            },
            service_down: {
                name: 'Service Down',
                metric: 'health_check',
                threshold: 0,
                operator: '==',
                duration: 30, // 30 seconds
                severity: 'critical',
                channels: ['email', 'slack', 'pagerduty', 'sms']
            },
            database_connection: {
                name: 'Database Connection Issues',
                metric: 'db_connection_errors',
                threshold: 10,
                operator: '>',
                duration: 120, // 2 minutes
                severity: 'critical',
                channels: ['email', 'slack', 'pagerduty']
            }
        };

        Object.entries(alertConfigs).forEach(([id, config]) => {
            this.alerts.set(id, {
                ...config,
                id,
                enabled: true,
                lastTriggered: null,
                triggerCount: 0
            });
        });
    }

    // Create performance dashboards
    createPerformanceDashboards() {
        const dashboards = {
            system_overview: {
                name: 'System Overview',
                widgets: [
                    { type: 'metric', title: 'CPU Usage', metric: 'cpu_usage', format: 'percentage' },
                    { type: 'metric', title: 'Memory Usage', metric: 'memory_usage', format: 'percentage' },
                    { type: 'metric', title: 'Active Users', metric: 'active_users', format: 'number' },
                    { type: 'chart', title: 'Request Rate', metric: 'request_rate', timeRange: '1h' },
                    { type: 'chart', title: 'Response Time', metric: 'response_time', timeRange: '1h' },
                    { type: 'chart', title: 'Error Rate', metric: 'error_rate', timeRange: '1h' }
                ],
                refreshInterval: 30
            },
            application_performance: {
                name: 'Application Performance',
                widgets: [
                    { type: 'chart', title: 'Throughput', metric: 'requests_per_second', timeRange: '4h' },
                    { type: 'chart', title: 'Latency Distribution', metric: 'response_time_percentiles', timeRange: '1h' },
                    { type: 'chart', title: 'Agent Performance', metric: 'agent_response_time', timeRange: '1h' },
                    { type: 'table', title: 'Top Errors', metric: 'error_breakdown', limit: 10 },
                    { type: 'heatmap', title: 'Response Time Heatmap', metric: 'response_time_heatmap' }
                ],
                refreshInterval: 60
            },
            business_metrics: {
                name: 'Business Metrics',
                widgets: [
                    { type: 'metric', title: 'Total Queries', metric: 'total_queries', format: 'number' },
                    { type: 'metric', title: 'User Satisfaction', metric: 'user_satisfaction', format: 'percentage' },
                    { type: 'chart', title: 'Query Volume', metric: 'query_volume', timeRange: '24h' },
                    { type: 'chart', title: 'Success Rate', metric: 'success_rate', timeRange: '24h' },
                    { type: 'pie', title: 'Query Categories', metric: 'query_categories' }
                ],
                refreshInterval: 300
            },
            infrastructure: {
                name: 'Infrastructure',
                widgets: [
                    { type: 'metric', title: 'Active Instances', metric: 'active_instances', format: 'number' },
                    { type: 'chart', title: 'Network I/O', metric: 'network_io', timeRange: '1h' },
                    { type: 'chart', title: 'Disk Usage', metric: 'disk_usage', timeRange: '4h' },
                    { type: 'table', title: 'Instance Health', metric: 'instance_health' },
                    { type: 'map', title: 'Geographic Distribution', metric: 'user_locations' }
                ],
                refreshInterval: 120
            }
        };

        Object.entries(dashboards).forEach(([id, dashboard]) => {
            this.dashboards.set(id, {
                ...dashboard,
                id,
                created: new Date().toISOString(),
                lastUpdated: new Date().toISOString()
            });
        });
    }

    // Build anomaly detection
    buildAnomalyDetection() {
        const detectors = {
            response_time_anomaly: {
                name: 'Response Time Anomaly Detection',
                metric: 'response_time',
                algorithm: 'statistical',
                sensitivity: 'medium',
                baseline_period: '7d',
                detection_window: '5m',
                threshold_multiplier: 2.5
            },
            error_rate_spike: {
                name: 'Error Rate Spike Detection',
                metric: 'error_rate',
                algorithm: 'threshold',
                sensitivity: 'high',
                baseline_period: '1d',
                detection_window: '1m',
                threshold_multiplier: 3.0
            },
            traffic_anomaly: {
                name: 'Traffic Anomaly Detection',
                metric: 'request_rate',
                algorithm: 'seasonal',
                sensitivity: 'medium',
                baseline_period: '14d',
                detection_window: '10m',
                threshold_multiplier: 2.0
            },
            user_behavior_anomaly: {
                name: 'User Behavior Anomaly',
                metric: 'user_session_patterns',
                algorithm: 'machine_learning',
                sensitivity: 'low',
                baseline_period: '30d',
                detection_window: '15m',
                threshold_multiplier: 1.8
            }
        };

        Object.entries(detectors).forEach(([id, detector]) => {
            this.anomalyDetectors.set(id, {
                ...detector,
                id,
                enabled: true,
                lastRun: null,
                anomaliesDetected: 0
            });
        });
    }

    // Process real-time metrics
    async processMetrics(metricData) {
        const timestamp = new Date().toISOString();
        
        // Store metrics
        Object.entries(metricData).forEach(([metric, value]) => {
            if (!this.metrics.has(metric)) {
                this.metrics.set(metric, []);
            }
            
            const metricHistory = this.metrics.get(metric);
            metricHistory.push({ timestamp, value });
            
            // Keep only last 1000 data points
            if (metricHistory.length > 1000) {
                metricHistory.shift();
            }
        });

        // Check alerts
        await this.checkAlerts(metricData);
        
        // Run anomaly detection
        await this.runAnomalyDetection(metricData);

        return {
            processed: Object.keys(metricData).length,
            timestamp
        };
    }

    // Check alerts
    async checkAlerts(metricData) {
        const triggeredAlerts = [];

        for (const [alertId, alert] of this.alerts) {
            if (!alert.enabled) continue;

            const metricValue = metricData[alert.metric];
            if (metricValue === undefined) continue;

            const shouldTrigger = this.evaluateAlertCondition(metricValue, alert);
            
            if (shouldTrigger) {
                const alertEvent = await this.triggerAlert(alertId, alert, metricValue);
                triggeredAlerts.push(alertEvent);
            }
        }

        return triggeredAlerts;
    }

    // Evaluate alert condition
    evaluateAlertCondition(value, alert) {
        switch (alert.operator) {
            case '>':
                return value > alert.threshold;
            case '<':
                return value < alert.threshold;
            case '==':
                return value === alert.threshold;
            case '>=':
                return value >= alert.threshold;
            case '<=':
                return value <= alert.threshold;
            default:
                return false;
        }
    }

    // Trigger alert
    async triggerAlert(alertId, alert, value) {
        const alertEvent = {
            id: Date.now(),
            alertId,
            name: alert.name,
            severity: alert.severity,
            metric: alert.metric,
            value,
            threshold: alert.threshold,
            timestamp: new Date().toISOString(),
            channels: alert.channels
        };

        // Update alert statistics
        alert.lastTriggered = alertEvent.timestamp;
        alert.triggerCount++;

        // Send notifications
        await this.sendAlertNotifications(alertEvent);

        // Store in history
        this.alertHistory.push(alertEvent);

        return alertEvent;
    }

    // Send alert notifications
    async sendAlertNotifications(alertEvent) {
        const notifications = [];

        for (const channel of alertEvent.channels) {
            const notification = await this.sendNotification(channel, alertEvent);
            notifications.push(notification);
        }

        return notifications;
    }

    // Send notification to specific channel
    async sendNotification(channel, alertEvent) {
        // Simulate notification sending
        await new Promise(resolve => setTimeout(resolve, 100));

        const message = `🚨 ${alertEvent.name}: ${alertEvent.metric} = ${alertEvent.value} (threshold: ${alertEvent.threshold})`;

        return {
            channel,
            message,
            sent: true,
            timestamp: new Date().toISOString()
        };
    }

    // Run anomaly detection
    async runAnomalyDetection(metricData) {
        const anomalies = [];

        for (const [detectorId, detector] of this.anomalyDetectors) {
            if (!detector.enabled) continue;

            const metricValue = metricData[detector.metric];
            if (metricValue === undefined) continue;

            const anomaly = await this.detectAnomaly(detectorId, detector, metricValue);
            if (anomaly) {
                anomalies.push(anomaly);
                detector.anomaliesDetected++;
            }

            detector.lastRun = new Date().toISOString();
        }

        return anomalies;
    }

    // Detect anomaly using specified algorithm
    async detectAnomaly(detectorId, detector, value) {
        const metricHistory = this.metrics.get(detector.metric) || [];
        
        if (metricHistory.length < 10) {
            return null; // Not enough data
        }

        // Simple statistical anomaly detection
        const recentValues = metricHistory.slice(-20).map(m => m.value);
        const mean = recentValues.reduce((a, b) => a + b, 0) / recentValues.length;
        const stdDev = Math.sqrt(recentValues.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / recentValues.length);
        
        const threshold = mean + (stdDev * detector.threshold_multiplier);
        
        if (value > threshold) {
            return {
                id: Date.now(),
                detectorId,
                name: detector.name,
                metric: detector.metric,
                value,
                expected: mean,
                threshold,
                deviation: value - mean,
                timestamp: new Date().toISOString()
            };
        }

        return null;
    }

    // Get dashboard data
    getDashboardData(dashboardId) {
        const dashboard = this.dashboards.get(dashboardId);
        if (!dashboard) return null;

        const widgetData = dashboard.widgets.map(widget => {
            const metricHistory = this.metrics.get(widget.metric) || [];
            
            return {
                ...widget,
                data: this.formatWidgetData(widget, metricHistory)
            };
        });

        return {
            ...dashboard,
            widgets: widgetData,
            lastUpdated: new Date().toISOString()
        };
    }

    // Format widget data based on type
    formatWidgetData(widget, metricHistory) {
        const recentData = metricHistory.slice(-100); // Last 100 data points

        switch (widget.type) {
            case 'metric':
                return recentData.length > 0 ? recentData[recentData.length - 1].value : 0;
            case 'chart':
                return recentData.map(d => ({ x: d.timestamp, y: d.value }));
            case 'table':
                return recentData.slice(-10).map((d, i) => ({ id: i, timestamp: d.timestamp, value: d.value }));
            default:
                return recentData;
        }
    }

    // Get monitoring summary
    getMonitoringSummary() {
        return {
            alerts: {
                total: this.alerts.size,
                enabled: Array.from(this.alerts.values()).filter(a => a.enabled).length,
                recentTriggers: this.alertHistory.slice(-10)
            },
            dashboards: {
                total: this.dashboards.size,
                available: Array.from(this.dashboards.keys())
            },
            anomalyDetection: {
                detectors: this.anomalyDetectors.size,
                enabled: Array.from(this.anomalyDetectors.values()).filter(d => d.enabled).length,
                totalAnomalies: Array.from(this.anomalyDetectors.values()).reduce((sum, d) => sum + d.anomaliesDetected, 0)
            },
            metrics: {
                tracked: this.metrics.size,
                dataPoints: Array.from(this.metrics.values()).reduce((sum, history) => sum + history.length, 0)
            }
        };
    }

    // Get alert configuration
    getAlertConfiguration(alertId) {
        return this.alerts.get(alertId);
    }

    // Update alert configuration
    updateAlertConfiguration(alertId, updates) {
        const alert = this.alerts.get(alertId);
        if (!alert) return null;

        Object.assign(alert, updates);
        return alert;
    }

    // Clear old data
    clearOldData(retentionDays = 7) {
        const cutoffTime = new Date(Date.now() - (retentionDays * 24 * 60 * 60 * 1000));
        
        // Clear old metrics
        for (const [metric, history] of this.metrics) {
            const filteredHistory = history.filter(d => new Date(d.timestamp) > cutoffTime);
            this.metrics.set(metric, filteredHistory);
        }

        // Clear old alert history
        this.alertHistory = this.alertHistory.filter(alert => new Date(alert.timestamp) > cutoffTime);

        return {
            cleared: true,
            retentionDays,
            timestamp: new Date().toISOString()
        };
    }
}

export default ProductionMonitoring;