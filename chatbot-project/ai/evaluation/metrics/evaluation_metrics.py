"""Evaluation metrics for the CHaBot system."""

from typing import Dict, List, Any, Tuple
import numpy as np
from datetime import datetime

class EvaluationMetrics:
    def __init__(self):
        self.metrics_history = []
    
    def calculate_response_quality(self, response: str, reference: str = None, 
                                 user_feedback: Dict[str, Any] = None) -> Dict[str, float]:
        """Calculate response quality metrics."""
        metrics = {}
        
        # Basic metrics
        metrics["length_score"] = self._calculate_length_score(response)
        metrics["coherence_score"] = self._calculate_coherence_score(response)
        metrics["completeness_score"] = self._calculate_completeness_score(response)
        
        # Reference-based metrics (if reference available)
        if reference:
            metrics["similarity_score"] = self._calculate_similarity(response, reference)
            metrics["coverage_score"] = self._calculate_coverage(response, reference)
        
        # User feedback metrics
        if user_feedback:
            metrics["user_satisfaction"] = user_feedback.get("rating", 0.0) / 5.0
            metrics["helpfulness"] = user_feedback.get("helpful", False)
        
        # Overall quality score
        metrics["overall_quality"] = np.mean(list(metrics.values()))
        
        return metrics
    
    def calculate_agent_performance(self, agent_id: str, interactions: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate agent performance metrics."""
        if not interactions:
            return {"performance_score": 0.0}
        
        metrics = {}
        
        # Response time metrics
        response_times = [i.get("response_time", 0) for i in interactions]
        metrics["avg_response_time"] = np.mean(response_times)
        metrics["response_time_consistency"] = 1.0 - (np.std(response_times) / np.mean(response_times)) if np.mean(response_times) > 0 else 0.0
        
        # Success rate
        successful_interactions = [i for i in interactions if i.get("success", False)]
        metrics["success_rate"] = len(successful_interactions) / len(interactions)
        
        # User satisfaction
        ratings = [i.get("user_rating", 3) for i in interactions if i.get("user_rating")]
        metrics["avg_user_rating"] = np.mean(ratings) / 5.0 if ratings else 0.6
        
        # Task completion rate
        completed_tasks = [i for i in interactions if i.get("task_completed", False)]
        metrics["task_completion_rate"] = len(completed_tasks) / len(interactions)
        
        # Overall performance
        metrics["performance_score"] = np.mean([
            metrics["success_rate"],
            metrics["avg_user_rating"],
            metrics["task_completion_rate"],
            min(1.0, 1.0 / (metrics["avg_response_time"] / 30.0)) if metrics["avg_response_time"] > 0 else 0.0
        ])
        
        return metrics
    
    def calculate_reasoning_quality(self, reasoning_chain: Dict[str, Any]) -> Dict[str, float]:
        """Calculate reasoning quality metrics."""
        metrics = {}
        
        reasoning_steps = reasoning_chain.get("reasoning", [])
        
        if not reasoning_steps:
            return {"reasoning_quality": 0.0}
        
        # Logical consistency
        metrics["logical_consistency"] = self._check_logical_consistency(reasoning_steps)
        
        # Evidence support
        metrics["evidence_support"] = self._calculate_evidence_support(reasoning_steps)
        
        # Reasoning depth
        metrics["reasoning_depth"] = min(1.0, len(reasoning_steps) / 5.0)
        
        # Confidence calibration
        confidences = [step.get("confidence", 0.5) for step in reasoning_steps]
        overall_confidence = reasoning_chain.get("confidence", 0.5)
        metrics["confidence_calibration"] = 1.0 - abs(overall_confidence - np.mean(confidences))
        
        # Completeness
        metrics["completeness"] = self._calculate_reasoning_completeness(reasoning_steps)
        
        # Overall reasoning quality
        metrics["reasoning_quality"] = np.mean(list(metrics.values()))
        
        return metrics
    
    def calculate_system_performance(self, system_metrics: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall system performance metrics."""
        metrics = {}
        
        # Throughput metrics
        metrics["requests_per_second"] = system_metrics.get("requests_per_second", 0.0)
        metrics["avg_response_time"] = system_metrics.get("avg_response_time", 0.0)
        
        # Resource utilization
        metrics["cpu_utilization"] = system_metrics.get("cpu_usage", 0.0) / 100.0
        metrics["memory_utilization"] = system_metrics.get("memory_usage", 0.0) / 100.0
        
        # Error rates
        metrics["error_rate"] = system_metrics.get("error_rate", 0.0)
        metrics["availability"] = 1.0 - metrics["error_rate"]
        
        # Agent coordination efficiency
        metrics["agent_coordination_efficiency"] = system_metrics.get("coordination_efficiency", 0.8)
        
        # Overall system health
        metrics["system_health"] = np.mean([
            metrics["availability"],
            1.0 - metrics["cpu_utilization"],
            1.0 - metrics["memory_utilization"],
            metrics["agent_coordination_efficiency"]
        ])
        
        return metrics
    
    def _calculate_length_score(self, response: str) -> float:
        """Calculate response length appropriateness score."""
        length = len(response.split())
        
        if 10 <= length <= 200:
            return 1.0
        elif length < 10:
            return length / 10.0
        else:
            return max(0.5, 200.0 / length)
    
    def _calculate_coherence_score(self, response: str) -> float:
        """Calculate response coherence score."""
        sentences = response.split('.')
        
        if len(sentences) < 2:
            return 0.8
        
        # Simple coherence check based on sentence transitions
        coherence_indicators = ['therefore', 'however', 'moreover', 'furthermore', 'additionally']
        coherence_count = sum(1 for sentence in sentences for indicator in coherence_indicators if indicator in sentence.lower())
        
        return min(1.0, 0.6 + (coherence_count / len(sentences)) * 0.4)
    
    def _calculate_completeness_score(self, response: str) -> float:
        """Calculate response completeness score."""
        # Check for conclusion indicators
        conclusion_indicators = ['in conclusion', 'therefore', 'thus', 'finally', 'to summarize']
        has_conclusion = any(indicator in response.lower() for indicator in conclusion_indicators)
        
        # Check for explanation elements
        explanation_indicators = ['because', 'since', 'due to', 'as a result']
        has_explanation = any(indicator in response.lower() for indicator in explanation_indicators)
        
        completeness = 0.5
        if has_conclusion:
            completeness += 0.25
        if has_explanation:
            completeness += 0.25
        
        return completeness
    
    def _calculate_similarity(self, response: str, reference: str) -> float:
        """Calculate similarity between response and reference."""
        # Simple word overlap similarity
        response_words = set(response.lower().split())
        reference_words = set(reference.lower().split())
        
        if not reference_words:
            return 0.0
        
        overlap = len(response_words.intersection(reference_words))
        return overlap / len(reference_words)
    
    def _calculate_coverage(self, response: str, reference: str) -> float:
        """Calculate how well response covers reference content."""
        reference_sentences = reference.split('.')
        response_lower = response.lower()
        
        covered_sentences = 0
        for sentence in reference_sentences:
            if sentence.strip() and any(word in response_lower for word in sentence.lower().split() if len(word) > 3):
                covered_sentences += 1
        
        return covered_sentences / len(reference_sentences) if reference_sentences else 0.0
    
    def _check_logical_consistency(self, reasoning_steps: List[Dict[str, Any]]) -> float:
        """Check logical consistency of reasoning steps."""
        if len(reasoning_steps) < 2:
            return 1.0
        
        consistency_score = 1.0
        
        for i in range(1, len(reasoning_steps)):
            current_step = reasoning_steps[i].get("step", "")
            previous_step = reasoning_steps[i-1].get("step", "")
            
            # Simple contradiction detection
            if "not" in current_step.lower() and any(word in previous_step.lower() for word in current_step.lower().split() if word != "not"):
                consistency_score -= 0.2
        
        return max(0.0, consistency_score)
    
    def _calculate_evidence_support(self, reasoning_steps: List[Dict[str, Any]]) -> float:
        """Calculate evidence support score."""
        total_evidence = sum(len(step.get("evidence", [])) for step in reasoning_steps)
        
        if not reasoning_steps:
            return 0.0
        
        avg_evidence_per_step = total_evidence / len(reasoning_steps)
        return min(1.0, avg_evidence_per_step / 2.0)  # Normalize to 2 pieces of evidence per step
    
    def _calculate_reasoning_completeness(self, reasoning_steps: List[Dict[str, Any]]) -> float:
        """Calculate reasoning completeness."""
        step_types = [step.get("type", "") for step in reasoning_steps]
        
        # Check for different types of reasoning steps
        has_analysis = any("analy" in step_type.lower() for step_type in step_types)
        has_inference = any("infer" in step_type.lower() for step_type in step_types)
        has_conclusion = any("conclu" in step_type.lower() for step_type in step_types)
        
        completeness = 0.4  # Base score
        if has_analysis:
            completeness += 0.2
        if has_inference:
            completeness += 0.2
        if has_conclusion:
            completeness += 0.2
        
        return completeness
    
    def generate_evaluation_report(self, evaluation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive evaluation report."""
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "summary": {},
            "detailed_metrics": {},
            "recommendations": []
        }
        
        # Calculate summary metrics
        if "responses" in evaluation_data:
            response_metrics = [self.calculate_response_quality(r) for r in evaluation_data["responses"]]
            report["summary"]["avg_response_quality"] = np.mean([m["overall_quality"] for m in response_metrics])
        
        if "agent_interactions" in evaluation_data:
            for agent_id, interactions in evaluation_data["agent_interactions"].items():
                agent_metrics = self.calculate_agent_performance(agent_id, interactions)
                report["detailed_metrics"][f"agent_{agent_id}"] = agent_metrics
        
        if "reasoning_chains" in evaluation_data:
            reasoning_metrics = [self.calculate_reasoning_quality(r) for r in evaluation_data["reasoning_chains"]]
            report["summary"]["avg_reasoning_quality"] = np.mean([m["reasoning_quality"] for m in reasoning_metrics])
        
        # Generate recommendations
        report["recommendations"] = self._generate_recommendations(report)
        
        return report
    
    def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Generate improvement recommendations based on metrics."""
        recommendations = []
        
        summary = report.get("summary", {})
        
        if summary.get("avg_response_quality", 1.0) < 0.7:
            recommendations.append("Improve response quality by enhancing content generation")
        
        if summary.get("avg_reasoning_quality", 1.0) < 0.6:
            recommendations.append("Strengthen reasoning capabilities with better evidence support")
        
        # Agent-specific recommendations
        for agent_id, metrics in report.get("detailed_metrics", {}).items():
            if metrics.get("performance_score", 1.0) < 0.6:
                recommendations.append(f"Optimize {agent_id} performance and response times")
        
        return recommendations