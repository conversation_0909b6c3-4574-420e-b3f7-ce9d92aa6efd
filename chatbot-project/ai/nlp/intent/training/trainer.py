from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
import torch
from torch.utils.data import Dataset
from sklearn.preprocessing import LabelEncoder
from .training_data import training_data
import numpy as np

class IntentDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        encoding = self.tokenizer(
            text, truncation=True, padding='max_length',
            max_length=self.max_length, return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(self.labels[idx], dtype=torch.long)
        }

class IntentTrainer:
    def __init__(self):
        self.tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
        self.label_encoder = LabelEncoder()
        
    def prepare_data(self):
        texts = [item[0] for item in training_data]
        labels = [item[1] for item in training_data]
        
        encoded_labels = self.label_encoder.fit_transform(labels)
        
        return texts, encoded_labels
    
    def train_model(self):
        texts, labels = self.prepare_data()
        
        model = AutoModelForSequenceClassification.from_pretrained(
            "bert-base-uncased", 
            num_labels=len(self.label_encoder.classes_)
        )
        
        dataset = IntentDataset(texts, labels, self.tokenizer)
        
        training_args = TrainingArguments(
            output_dir='./intent_model',
            num_train_epochs=3,
            per_device_train_batch_size=8,
            warmup_steps=100,
            weight_decay=0.01,
            logging_dir='./logs',
        )
        
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset,
            tokenizer=self.tokenizer,
        )
        
        trainer.train()
        trainer.save_model('./intent_model')
        
        return model, self.label_encoder