training_data = [
    # HR Policy queries - NUVO AI specific
    ("What is NUVO AI's privilege leave policy?", "hr_policy"),
    ("How many PL days do I get at NUVO AI Pvt Ltd?", "hr_policy"),
    ("What is the sick leave policy at NUVO AI?", "hr_policy"),
    ("Tell me about maternity leave at NUVO AI", "hr_policy"),
    ("Can I encash my privilege leave at NUVO AI?", "hr_policy"),
    ("What is the casual leave policy at NUVO AI?", "hr_policy"),
    ("How does PL encashment work at NUVO AI?", "hr_policy"),
    
    # HR Policy queries - Meril Group specific
    ("What is the leave policy at Meril Life Sciences?", "hr_policy"),
    ("How many days of privilege leave at Meril Life Sciences India?", "hr_policy"),
    ("What is Meril's maternity leave policy?", "hr_policy"),
    ("Tell me about sick leave at Meril Group?", "hr_policy"),
    ("What is the leave encashment policy at Meril Life Sciences?", "hr_policy"),
    ("How does CL work at Meril companies?", "hr_policy"),
    
    # Vacation/Leave requests
    ("I want to request privilege leave", "vacation_request"),
    ("How do I apply for casual leave?", "vacation_request"),
    ("Can I take time off next week?", "vacation_request"),
    ("I need to book holidays", "vacation_request"),
    ("How do I request maternity leave?", "vacation_request"),
    ("I want to apply for sick leave", "vacation_request"),
    ("Can I get advance PL for marriage?", "vacation_request"),
    
    # Finance/Benefits queries - NUVO AI
    ("What is my LTA entitlement at NUVO AI?", "finance_query"),
    ("How does the mediclaim policy work at NUVO AI?", "finance_query"),
    ("What is covered under group insurance at NUVO AI?", "finance_query"),
    ("Tell me about mobile phone reimbursement at NUVO AI", "finance_query"),
    
    # Finance/Benefits queries - Meril Group
    ("What is the LTA policy at Meril Life Sciences?", "finance_query"),
    ("How does term life insurance work at Meril?", "finance_query"),
    ("What is the mobile phone reimbursement at Meril?", "finance_query"),
    ("Tell me about insurance coverage at Meril Group", "finance_query"),
    
    # Travel and expense queries - NUVO AI
    ("What is NUVO AI's domestic travel policy?", "travel_policy"),
    ("What are the hotel stay limits for NUVO AI travel?", "travel_policy"),
    ("How do I claim travel expenses at NUVO AI?", "travel_policy"),
    ("What are the daily allowance rates at NUVO AI?", "travel_policy"),
    
    # Travel and expense queries - Meril Group
    ("How do I book travel through Meril travel portal?", "travel_policy"),
    ("What is the international travel policy at Meril?", "travel_policy"),
    ("What are Meril's travel expense limits?", "travel_policy"),
    ("How does Meril travel desk work?", "travel_policy"),
    
    # Technical support
    ("My laptop is not working", "technical_support"),
    ("I need software installation", "technical_support"),
    ("VPN connection issues", "technical_support"),
    ("Password reset required", "technical_support"),
    ("IT support needed", "technical_support"),
    
    # Compliance and ethics queries - NUVO AI
    ("What is NUVO AI's ethical code of conduct?", "compliance_question"),
    ("How do I report sexual harassment at NUVO AI?", "compliance_question"),
    ("What are the anti-fraud policies at NUVO AI?", "compliance_question"),
    
    # Compliance and ethics queries - Meril Group
    ("Who is on Meril's sexual harassment committee?", "compliance_question"),
    ("What is Meril's ethical code of conduct?", "compliance_question"),
    ("How do I report harassment at Meril Life Sciences?", "compliance_question"),
    ("What is the whistleblower policy at Meril?", "compliance_question"),
    ("Tell me about conflict of interest policy at Meril", "compliance_question"),
    
    # General company information - NUVO AI
    ("What is NUVO AI's address?", "general_info"),
    ("Who drafted NUVO AI's HR policy?", "general_info"),
    ("What is NUVO AI Pvt Ltd's policy number?", "general_info"),
    ("Who approved NUVO AI policies?", "general_info"),
    
    # General company information - Meril Group
    ("Where is Meril Life Sciences located?", "general_info"),
    ("What companies are part of Meril Group?", "general_info"),
    ("What is Meril's contact number?", "general_info"),
    ("What is Meril's website?", "general_info"),
    ("Tell me about Meril Diagnostics", "general_info"),
    ("What is Meril Healthcare?", "general_info"),
    ("Tell me about Meril Endo-Surgery", "general_info"),
    
    # Cross-department/organization queries
    ("How does HR coordinate with Finance for payroll?", "cross_department"),
    ("What is the approval process between NUVO AI and Meril?", "cross_department"),
    ("Cross-team project collaboration between departments", "cross_department"),
    ("How do different Meril companies work together?", "cross_department"),
    
    # Emergency and safety
    ("What are the emergency procedures?", "safety_emergency"),
    ("Fire safety protocols", "safety_emergency"),
    ("Pregnancy safety measures at Meril", "safety_emergency"),
    ("Health and safety guidelines", "safety_emergency"),
    ("Emergency evacuation procedures", "safety_emergency"),
    
    # Administrative queries
    ("How do I update my personal information?", "administrative"),
    ("Where do I submit expense reports?", "administrative"),
    ("How do I access the Meril travel portal?", "administrative"),
    ("Who approves my leave requests?", "administrative"),
    ("How do I contact HR department?", "administrative"),
    
    # Specific committee and contact queries
    ("Who is Anita Nagar at Meril?", "contact_info"),
    ("Contact details for sexual harassment committee", "contact_info"),
    ("Who is Twisha Hathi at Meril Diagnostics?", "contact_info"),
    ("How to contact Ami Rughani at Meril Endo-Surgery?", "contact_info"),
    ("Who is Pallabi Sarkar at Meril Healthcare?", "contact_info")
]

intent_taxonomy = {
    "hr_policy": {
        "keywords": ["leave", "policy", "vacation", "privilege", "sick", "maternity", "casual", "encash", "working hours", "PL", "CL", "SL", "240 working days", "January 7th", "26 weeks", "182 days"],
        "organizations": ["NUVO AI", "NUVO AI Pvt Ltd", "Meril", "Meril Life Sciences", "Meril Life Sciences India", "Meril Group"],
        "departments": ["HR", "Human Resources"]
    },
    "vacation_request": {
        "keywords": ["request", "apply", "book", "take time off", "leave application", "holiday", "vacation", "time off", "advance PL", "marriage", "emergency"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["HR"]
    },
    "finance_query": {
        "keywords": ["LTA", "mediclaim", "insurance", "reimbursement", "bonus", "salary", "allowance", "benefits", "coverage", "term life", "CTC", "Rs. 20 Lacs", "basic salary"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["Finance", "HR"]
    },
    "travel_policy": {
        "keywords": ["travel", "hotel", "domestic", "international", "portal", "booking", "allowance", "expense", "tour", "travel desk", "outstation", "metro", "mini-metro", "non-metro"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["HR", "Travel Desk"]
    },
    "technical_support": {
        "keywords": ["laptop", "software", "VPN", "password", "IT", "technical", "system", "computer"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["IT", "Technical"]
    },
    "compliance_question": {
        "keywords": ["ethics", "conduct", "harassment", "fraud", "compliance", "committee", "report", "whistleblower", "conflict", "sexual harassment", "Supreme Court"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["HR", "Compliance", "Legal"]
    },
    "general_info": {
        "keywords": ["address", "contact", "location", "phone", "email", "departments", "companies", "group", "Bilakhia House", "Vapi", "Gujarat", "website", "CIN"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["All"]
    },
    "cross_department": {
        "keywords": ["coordinate", "collaboration", "approval", "between", "cross-team", "multi-department"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["Multiple"]
    },
    "safety_emergency": {
        "keywords": ["emergency", "fire", "safety", "pregnancy", "evacuation", "health", "protocols", "pregnant women", "drill"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["Safety", "HR"]
    },
    "administrative": {
        "keywords": ["update", "submit", "access", "portal", "approve", "forms", "documents", "HOD", "expense report"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["HR", "Admin"]
    },
    "contact_info": {
        "keywords": ["contact", "phone", "email", "committee", "presiding officer", "member", "DGM", "manager", "assistant manager"],
        "organizations": ["NUVO AI", "Meril"],
        "departments": ["HR", "All"]
    }
}