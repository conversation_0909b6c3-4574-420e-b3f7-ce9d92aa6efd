from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch
from typing import Dict, List, Tuple
import os

class IntentClassifier:
    def __init__(self, model_path: str = None):
        self.intent_taxonomy = {
            "hr_policy": ["leave", "policy", "vacation", "privilege", "sick", "maternity", "casual", "encash", "working hours", "PL", "CL", "SL", "240 working days", "January 7th", "26 weeks", "182 days"],
            "vacation_request": ["request", "apply", "book", "take time off", "leave application", "holiday", "vacation", "time off", "advance PL", "marriage", "emergency"],
            "finance_query": ["LTA", "mediclaim", "insurance", "reimbursement", "bonus", "salary", "allowance", "benefits", "coverage", "term life", "CTC", "Rs. 20 Lacs", "basic salary"],
            "travel_policy": ["travel", "hotel", "domestic", "international", "portal", "booking", "allowance", "expense", "tour", "travel desk", "outstation", "metro", "mini-metro", "non-metro"],
            "technical_support": ["laptop", "software", "VPN", "password", "IT", "technical", "system", "computer"],
            "compliance_question": ["ethics", "conduct", "harassment", "fraud", "compliance", "committee", "report", "whistleblower", "conflict", "sexual harassment", "Supreme Court"],
            "general_info": ["address", "contact", "location", "phone", "email", "departments", "companies", "group", "Bilakhia House", "Vapi", "Gujarat", "website", "CIN"],
            "cross_department": ["coordinate", "collaboration", "approval", "between", "cross-team", "multi-department"],
            "safety_emergency": ["emergency", "fire", "safety", "pregnancy", "evacuation", "health", "protocols", "pregnant women", "drill"],
            "administrative": ["update", "submit", "access", "portal", "approve", "forms", "documents", "HOD", "expense report"],
            "contact_info": ["contact", "phone", "email", "committee", "presiding officer", "member", "DGM", "manager", "assistant manager"]
        }
        
        # Accurate organization names from documents
        self.organizations = [
            "NUVO AI", "NUVO AI Pvt Ltd", "Nuvo AI", 
            "Meril", "Meril Life Sciences", "Meril Life Sciences Pvt. Ltd.",
            "Meril Life Sciences India", "Meril Life Sciences India Pvt. Ltd.",
            "Meril Group", "Meril Group of Companies",
            "Meril Healthcare", "Meril Healthcare Pvt. Ltd.",
            "Meril Diagnostics", "Meril Diagnostics Pvt. Ltd.",
            "Meril Endo-Surgery", "Meril Endo-Surgery Pvt. Ltd.",
            "MLS", "MerilLife"
        ]
        
        # Departments and roles from documents
        self.departments = ["HR", "Human Resources", "Finance", "IT", "Technical", "AI", "Product", "Safety", "Admin", "Travel Desk"]
        
        # Key personnel from documents
        self.personnel = [
            "Anita Nagar", "Gayathri Nair", "Ankita Patel", "Neha Desai",
            "Twisha Hathi", "Chintal N Patel", "Tejalkumari A.Patel",
            "Ami Rughani", "Hemaxi Patel", "Ankita Rana",
            "Pallabi Sarkar", "Anushree Uniyal", "Punita Purav Pate",
            "Ankita Desai", "Dhaval Pancholi", "Dr. Harshadkumar Panjkar",
            "Hetvi Desai"
        ]
        
        if model_path and os.path.exists(model_path):
            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            self.model = AutoModelForSequenceClassification.from_pretrained(model_path)
        else:
            self.tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
            self.model = AutoModelForSequenceClassification.from_pretrained(
                "bert-base-uncased", 
                num_labels=len(self.get_intent_labels())
            )
        self.intent_labels = self.get_intent_labels()
        
    def get_intent_labels(self) -> List[str]:
        return [
            "hr_policy", "vacation_request", "finance_query", "travel_policy",
            "technical_support", "compliance_question", "general_info", 
            "cross_department", "safety_emergency", "administrative", "contact_info"
        ]
    
    def predict_intent(self, query: str) -> Tuple[str, float]:
        query_lower = query.lower()
        
        # Enhanced rule-based classification with weighted scoring
        intent_scores = {}
        for intent, keywords in self.intent_taxonomy.items():
            score = 0
            for keyword in keywords:
                if keyword.lower() in query_lower:
                    # Weight longer keywords more heavily
                    weight = len(keyword.split()) * 1.5 if len(keyword.split()) > 1 else 1
                    score += weight
            
            if score > 0:
                intent_scores[intent] = score
        
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            # Calculate confidence based on score and query length
            max_score = intent_scores[best_intent]
            confidence = min(0.95, 0.7 + (max_score * 0.03))
            return best_intent, confidence
        
        # Fallback to BERT model
        inputs = self.tokenizer(query, return_tensors="pt", truncation=True, padding=True)
        
        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
            
        predicted_class_id = predictions.argmax().item()
        confidence = predictions[0][predicted_class_id].item()
        
        return self.intent_labels[predicted_class_id], confidence
    
    def extract_entities(self, query: str) -> Dict[str, List[str]]:
        query_lower = query.lower()
        
        found_orgs = [org for org in self.organizations if org.lower() in query_lower]
        found_depts = [dept for dept in self.departments if dept.lower() in query_lower]
        found_personnel = [person for person in self.personnel if person.lower() in query_lower]
        
        return {
            "organizations": found_orgs,
            "departments": found_depts,
            "personnel": found_personnel
        }
    
    def analyze_query(self, query: str) -> Dict:
        intent, confidence = self.predict_intent(query)
        entities = self.extract_entities(query)
        
        return {
            "original_query": query,
            "primary_intent": intent,
            "confidence": confidence,
            "complexity": self._assess_complexity(query),
            "keywords": self._extract_keywords(query, intent),
            "entities": entities,
            "requires_decomposition": self._needs_decomposition(query)
        }
    
    def _assess_complexity(self, query: str) -> str:
        word_count = len(query.split())
        conjunctions = ["and", "also", "plus", "additionally", "furthermore", "moreover"]
        
        if word_count > 30 or sum(1 for conj in conjunctions if conj in query.lower()) > 2:
            return "high"
        elif word_count > 20 or any(conj in query.lower() for conj in conjunctions):
            return "medium"
        return "low"
    
    def _extract_keywords(self, query: str, intent: str) -> List[str]:
        query_lower = query.lower()
        intent_keywords = self.intent_taxonomy.get(intent, [])
        
        return [kw for kw in intent_keywords if kw.lower() in query_lower]
    
    def _needs_decomposition(self, query: str) -> bool:
        conjunctions = ["and", "also", "plus", "additionally", "furthermore", "moreover", "as well as"]
        return (any(conj in query.lower() for conj in conjunctions) or 
                len(query.split()) > 30 or
                query.count("?") > 1)