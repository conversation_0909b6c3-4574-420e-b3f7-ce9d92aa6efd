from typing import Dict, List, Optional
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

class EntityLinker:
    def __init__(self):
        self.knowledge_graph = self._build_knowledge_graph()
        
    def _build_knowledge_graph(self) -> Dict:
        return {
            "organizations": {
                "NUVO AI Pvt Ltd": {
                    "id": "org_001",
                    "type": "organization",
                    "aliases": ["NUVO AI", "Nuvo AI", "NAIPL"],
                    "parent": None,
                    "subsidiaries": [],
                    "location": "India",
                    "policy_number": "NAIPL001",
                    "personnel": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]
                },
                "Meril Life Sciences": {
                    "id": "org_002",
                    "type": "organization",
                    "aliases": ["Meril Life Sciences Pvt. Ltd.", "MLS"],
                    "parent": "Meril Group",
                    "subsidiaries": ["Meril Healthcare", "Meril Diagnostics", "Meril Endo-Surgery"],
                    "location": "Vapi, Gujarat",
                    "cin": "U24239GJ2007PTC051137",
                    "personnel": ["Anita Nagar", "<PERSON>athri <PERSON>", "Ankita Patel"]
                },
                "Meril Healthcare": {
                    "id": "org_003",
                    "type": "organization",
                    "parent": "Meril Life Sciences",
                    "personnel": ["Pallabi Sarkar", "Anushree Uniyal", "Punita Purav Pate"]
                },
                "Meril Diagnostics": {
                    "id": "org_004",
                    "type": "organization",
                    "parent": "Meril Life Sciences",
                    "personnel": ["Twisha Hathi", "Chintal N Patel", "Tejalkumari A.Patel"]
                },
                "Meril Endo-Surgery": {
                    "id": "org_005",
                    "type": "organization",
                    "parent": "Meril Life Sciences",
                    "personnel": ["Ami Rughani", "Hemaxi Patel", "Ankita Rana"]
                }
            },
            "personnel": {
                "Anita Nagar": {
                    "id": "per_001",
                    "type": "person",
                    "role": "Presiding Officer",
                    "organization": "Meril Life Sciences",
                    "committee": "Sexual Harassment Prevention",
                    "contact": {"mobile": "9714035588", "email": "<EMAIL>"}
                },
                "Twisha Hathi": {
                    "id": "per_002",
                    "type": "person",
                    "role": "DGM",
                    "organization": "Meril Diagnostics",
                    "committee": "Sexual Harassment Prevention",
                    "contact": {"mobile": "9924146740", "email": "<EMAIL>"}
                },
                "Ami Rughani": {
                    "id": "per_003",
                    "type": "person",
                    "role": "DGM",
                    "organization": "Meril Endo-Surgery",
                    "committee": "Sexual Harassment Prevention",
                    "contact": {"mobile": "**********", "email": "<EMAIL>"}
                },
                "Pallabi Sarkar": {
                    "id": "per_004",
                    "type": "person",
                    "role": "DGM",
                    "organization": "Meril Healthcare",
                    "committee": "Sexual Harassment Prevention",
                    "contact": {"mobile": "**********", "email": "<EMAIL>"}
                },
                "Ankita Desai": {
                    "id": "per_005",
                    "type": "person",
                    "role": "Policy Drafter",
                    "organization": "NUVO AI Pvt Ltd",
                    "drafted": "NUVO AI HR Policy"
                },
                "Dr. Harshadkumar Panjkar": {
                    "id": "per_006",
                    "type": "person",
                    "role": "Policy Approver",
                    "organization": "NUVO AI Pvt Ltd",
                    "approved": "NUVO AI HR Policy"
                }
            },
            "policies": {
                "privilege leave": {
                    "id": "pol_001",
                    "type": "policy",
                    "category": "hr_policy",
                    "details": "30 days per year, credited January 7th",
                    "organizations": ["NUVO AI Pvt Ltd", "Meril Life Sciences"]
                },
                "sexual harassment": {
                    "id": "pol_002",
                    "type": "policy",
                    "category": "compliance",
                    "committees": ["Sexual Harassment Prevention Committee"],
                    "act": "Sexual Harassment of Women at Workplace Act 2013"
                },
                "maternity leave": {
                    "id": "pol_003",
                    "type": "policy",
                    "category": "hr_policy",
                    "details": "26 weeks (182 days), maximum 2 children",
                    "organizations": ["NUVO AI Pvt Ltd", "Meril Life Sciences"]
                }
            }
        }
    
    def link_entities(self, entities: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
        linked_entities = {}
        
        for entity_type, entity_list in entities.items():
            linked_entities[entity_type] = []
            
            for entity in entity_list:
                linked_entity = entity.copy()
                linked_entity["linked_info"] = self._find_links(entity["text"], entity_type)
                linked_entities[entity_type].append(linked_entity)
        
        return linked_entities
    
    def _find_links(self, entity_text: str, entity_type: str) -> Optional[Dict]:
        entity_lower = entity_text.lower()
        
        # Map entity types to knowledge graph categories
        kg_mapping = {
            "organizations": "organizations",
            "personnel": "personnel", 
            "policies": "policies",
            "departments": "organizations"  # Departments are part of organizations
        }
        
        kg_category = kg_mapping.get(entity_type)
        if not kg_category or kg_category not in self.knowledge_graph:
            return None
        
        # Direct match
        for key, value in self.knowledge_graph[kg_category].items():
            if key.lower() == entity_lower:
                return value
            
            # Check aliases
            if "aliases" in value:
                for alias in value["aliases"]:
                    if alias.lower() == entity_lower:
                        return value
        
        # Partial match for organizations
        if kg_category == "organizations":
            for key, value in self.knowledge_graph[kg_category].items():
                if entity_lower in key.lower() or key.lower() in entity_lower:
                    return value
        
        return None
    
    def get_entity_relationships(self, entity_id: str) -> List[Dict]:
        relationships = []
        
        # Find the entity across all categories
        entity_info = None
        entity_category = None
        
        for category, entities in self.knowledge_graph.items():
            for key, value in entities.items():
                if value.get("id") == entity_id:
                    entity_info = value
                    entity_category = category
                    break
        
        if not entity_info:
            return relationships
        
        # Find relationships based on entity type
        if entity_category == "organizations":
            # Parent-child relationships
            if entity_info.get("parent"):
                relationships.append({
                    "type": "parent_organization",
                    "target": entity_info["parent"]
                })
            
            if entity_info.get("subsidiaries"):
                for subsidiary in entity_info["subsidiaries"]:
                    relationships.append({
                        "type": "subsidiary",
                        "target": subsidiary
                    })
            
            # Personnel relationships
            if entity_info.get("personnel"):
                for person in entity_info["personnel"]:
                    relationships.append({
                        "type": "employee",
                        "target": person
                    })
        
        elif entity_category == "personnel":
            # Organization relationship
            if entity_info.get("organization"):
                relationships.append({
                    "type": "works_at",
                    "target": entity_info["organization"]
                })
            
            # Committee relationships
            if entity_info.get("committee"):
                relationships.append({
                    "type": "member_of",
                    "target": entity_info["committee"]
                })
        
        return relationships