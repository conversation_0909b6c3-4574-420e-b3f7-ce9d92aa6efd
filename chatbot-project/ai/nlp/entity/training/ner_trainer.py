import spacy
from spacy.training import Example
from spacy.util import minibatch, compounding
import random
from typing import List, Tu<PERSON>, Dict

class NERTrainer:
    def __init__(self):
        self.nlp = spacy.blank("en")
        self.ner = self.nlp.add_pipe("ner")
        
    def get_training_data(self) -> List[Tuple[str, Dict]]:
        return [
            ("NUVO AI Pvt Ltd is located in India", {
                "entities": [(0, 16, "ORG")]
            }),
            ("<PERSON> is the presiding officer at Meril Life Sciences", {
                "entities": [(0, 11, "PERSON"), (40, 59, "ORG")]
            }),
            ("The privilege leave policy allows 30 days PL per year", {
                "entities": [(4, 26, "POLICY")]
            }),
            ("HR department handles all leave applications", {
                "entities": [(0, 13, "DEPT")]
            }),
            ("<PERSON><PERSON><PERSON> works at Meril Diagnostics as DGM", {
                "entities": [(0, 12, "PERSON"), (22, 39, "ORG")]
            }),
            ("Sexual harassment committee is established under Act 2013", {
                "entities": [(0, 27, "POLICY")]
            }),
            ("Meril Healthcare has <PERSON><PERSON><PERSON> as presiding officer", {
                "entities": [(0, 16, "ORG"), (21, 35, "PERSON")]
            }),
            ("The maternity leave policy provides 26 weeks leave", {
                "entities": [(4, 26, "POLICY")]
            }),
            ("Finance department processes LTA reimbursements", {
                "entities": [(0, 18, "DEPT"), (29, 32, "POLICY")]
            }),
            ("Dr. Harshadkumar Panjkar approved NUVO AI policies", {
                "entities": [(0, 24, "PERSON"), (34, 42, "ORG")]
            })
        ]
    
    def train_model(self, iterations: int = 100):
        training_data = self.get_training_data()
        
        # Add entity labels to the NER component
        for _, annotations in training_data:
            for ent in annotations.get("entities"):
                self.ner.add_label(ent[2])
        
        # Disable other pipes during training
        other_pipes = [pipe for pipe in self.nlp.pipe_names if pipe != "ner"]
        with self.nlp.disable_pipes(*other_pipes):
            optimizer = self.nlp.begin_training()
            
            for iteration in range(iterations):
                random.shuffle(training_data)
                losses = {}
                
                # Create batches
                batches = minibatch(training_data, size=compounding(4.0, 32.0, 1.001))
                
                for batch in batches:
                    examples = []
                    for text, annotations in batch:
                        doc = self.nlp.make_doc(text)
                        example = Example.from_dict(doc, annotations)
                        examples.append(example)
                    
                    self.nlp.update(examples, drop=0.5, losses=losses, sgd=optimizer)
                
                if iteration % 10 == 0:
                    print(f"Iteration {iteration}, Losses: {losses}")
        
        return self.nlp
    
    def save_model(self, output_dir: str):
        self.nlp.to_disk(output_dir)
        print(f"Model saved to {output_dir}")
    
    def evaluate_model(self, test_data: List[Tuple[str, Dict]]) -> Dict:
        correct = 0
        total = 0
        
        for text, annotations in test_data:
            doc = self.nlp(text)
            predicted_entities = [(ent.start_char, ent.end_char, ent.label_) for ent in doc.ents]
            actual_entities = annotations.get("entities", [])
            
            for entity in actual_entities:
                total += 1
                if entity in predicted_entities:
                    correct += 1
        
        accuracy = correct / total if total > 0 else 0
        return {
            "accuracy": accuracy,
            "correct": correct,
            "total": total
        }