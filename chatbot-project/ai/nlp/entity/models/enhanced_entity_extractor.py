import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from .custom_ner import CustomNER
from linking.entity_linker import EntityLinker
from normalization.entity_normalizer import EntityNormalizer
from resolution.coreference_resolver import CoreferenceResolver
from typing import Dict, List, Optional

class EnhancedEntityExtractor:
    def __init__(self):
        self.custom_ner = CustomNER()
        self.entity_linker = EntityLinker()
        self.entity_normalizer = EntityNormalizer()
        self.coreference_resolver = CoreferenceResolver()
        
    def extract_and_process_entities(self, text: str, context: str = "") -> Dict[str, List[Dict]]:
        """Complete entity extraction and processing pipeline"""
        
        # Step 1: Extract raw entities
        raw_entities = self.custom_ner.extract_entities(text)
        
        # Step 2: Normalize entities
        normalized_entities = self.entity_normalizer.normalize_entities(raw_entities)
        
        # Step 3: Disambiguate entities
        disambiguated_entities = self.entity_normalizer.disambiguate_entities(
            normalized_entities, context
        )
        
        # Step 4: Link entities to knowledge graph
        linked_entities = self.entity_linker.link_entities(disambiguated_entities)
        
        # Step 5: Add relationship information
        enhanced_entities = self._add_relationship_info(linked_entities)
        
        return enhanced_entities
    
    def process_multiple_documents(self, documents: List[str]) -> Dict:
        """Process multiple documents with cross-document entity resolution"""
        
        # Extract entities from each document
        document_entities = {}
        for i, doc in enumerate(documents):
            doc_entities = self.extract_and_process_entities(doc)
            document_entities[i] = doc_entities
        
        # Resolve coreferences across documents
        resolved_entities = self.coreference_resolver.resolve_coreferences(documents)
        
        # Extract cross-document relationships
        relationships = self.coreference_resolver.extract_entity_relationships(resolved_entities)
        
        return {
            "document_entities": document_entities,
            "resolved_entities": resolved_entities,
            "relationships": relationships,
            "summary": self._generate_entity_summary(document_entities, relationships)
        }
    
    def _add_relationship_info(self, entities: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
        """Add relationship information to entities"""
        enhanced_entities = {}
        
        for entity_type, entity_list in entities.items():
            enhanced_entities[entity_type] = []
            
            for entity in entity_list:
                enhanced_entity = entity.copy()
                
                # Add relationships if entity is linked
                if "linked_info" in entity and entity["linked_info"]:
                    entity_id = entity["linked_info"].get("id")
                    if entity_id:
                        relationships = self.entity_linker.get_entity_relationships(entity_id)
                        enhanced_entity["relationships"] = relationships
                
                enhanced_entities[entity_type].append(enhanced_entity)
        
        return enhanced_entities
    
    def _generate_entity_summary(self, document_entities: Dict, relationships: List[Dict]) -> Dict:
        """Generate summary of extracted entities and relationships"""
        
        # Count entities by type
        entity_counts = {}
        unique_entities = set()
        
        for doc_id, doc_entities in document_entities.items():
            for entity_type, entities in doc_entities.items():
                if entity_type not in entity_counts:
                    entity_counts[entity_type] = 0
                
                for entity in entities:
                    canonical_form = entity.get("canonical_form", entity["text"])
                    unique_entities.add((entity_type, canonical_form))
                    entity_counts[entity_type] += 1
        
        # Count relationships by type
        relationship_counts = {}
        for rel in relationships:
            rel_type = rel["type"]
            relationship_counts[rel_type] = relationship_counts.get(rel_type, 0) + 1
        
        return {
            "total_documents": len(document_entities),
            "entity_counts": entity_counts,
            "unique_entities": len(unique_entities),
            "relationship_counts": relationship_counts,
            "total_relationships": len(relationships)
        }
    
    def get_entity_details(self, entity_text: str, entity_type: str) -> Optional[Dict]:
        """Get detailed information about a specific entity"""
        
        # Normalize the entity text
        normalized_entities = self.entity_normalizer.normalize_entities({
            entity_type: [{"text": entity_text, "confidence": 1.0}]
        })
        
        if entity_type in normalized_entities and normalized_entities[entity_type]:
            normalized_entity = normalized_entities[entity_type][0]
            
            # Link to knowledge graph
            linked_entities = self.entity_linker.link_entities({
                entity_type: [normalized_entity]
            })
            
            if entity_type in linked_entities and linked_entities[entity_type]:
                entity_info = linked_entities[entity_type][0]
                
                # Add relationships
                if "linked_info" in entity_info and entity_info["linked_info"]:
                    entity_id = entity_info["linked_info"].get("id")
                    if entity_id:
                        relationships = self.entity_linker.get_entity_relationships(entity_id)
                        entity_info["relationships"] = relationships
                
                return entity_info
        
        return None
    
    def search_entities_by_type(self, entity_type: str, documents: List[str]) -> List[Dict]:
        """Search for all entities of a specific type across documents"""
        
        results = []
        processed_docs = self.process_multiple_documents(documents)
        
        for doc_id, doc_entities in processed_docs["document_entities"].items():
            if entity_type in doc_entities:
                for entity in doc_entities[entity_type]:
                    entity_copy = entity.copy()
                    entity_copy["document_id"] = doc_id
                    results.append(entity_copy)
        
        # Remove duplicates based on canonical form
        unique_results = {}
        for entity in results:
            canonical_form = entity.get("canonical_form", entity["text"])
            if canonical_form not in unique_results:
                unique_results[canonical_form] = entity
            else:
                # Merge information from multiple mentions
                existing = unique_results[canonical_form]
                if "document_mentions" not in existing:
                    existing["document_mentions"] = [existing.get("document_id")]
                existing["document_mentions"].append(entity.get("document_id"))
        
        return list(unique_results.values())
    
    def find_entity_relationships(self, entity1: str, entity2: str, documents: List[str]) -> List[Dict]:
        """Find relationships between two specific entities"""
        
        processed_docs = self.process_multiple_documents(documents)
        relationships = processed_docs["relationships"]
        
        # Find relationships involving both entities
        relevant_relationships = []
        for rel in relationships:
            if ((rel["source"].lower() == entity1.lower() and rel["target"].lower() == entity2.lower()) or
                (rel["source"].lower() == entity2.lower() and rel["target"].lower() == entity1.lower())):
                relevant_relationships.append(rel)
        
        return relevant_relationships