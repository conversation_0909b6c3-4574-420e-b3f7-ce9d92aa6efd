import spacy
from spacy.matcher import Matcher, PhraseMatcher
from spacy.tokens import Span
from typing import List, Dict, Tuple
import re

class CustomNER:
    def __init__(self):
        self.nlp = spacy.load("en_core_web_sm")
        self.matcher = Matcher(self.nlp.vocab)
        self.phrase_matcher = PhraseMatcher(self.nlp.vocab)
        self._setup_custom_patterns()
        
    def _setup_custom_patterns(self):
        # Organization patterns
        org_patterns = [
            [{"LOWER": "nuvo"}, {"LOWER": "ai"}],
            [{"LOWER": "nuvo"}, {"LOWER": "ai"}, {"LOWER": "pvt"}, {"LOWER": "ltd"}],
            [{"LOWER": "meril"}, {"LOWER": "life"}, {"LOWER": "sciences"}],
            [{"LOWER": "meril"}, {"LOWER": "healthcare"}],
            [{"LOWER": "meril"}, {"LOWER": "diagnostics"}],
            [{"LOWER": "meril"}, {"LOWER": "endo-surgery"}],
            [{"LOWER": "meril"}, {"LOWER": "group"}]
        ]
        
        # Department patterns
        dept_patterns = [
            [{"LOWER": "hr"}, {"LOWER": "department"}],
            [{"LOWER": "human"}, {"LOWER": "resources"}],
            [{"LOWER": "finance"}, {"LOWER": "department"}],
            [{"LOWER": "technical"}, {"LOWER": "department"}],
            [{"LOWER": "ai"}, {"LOWER": "department"}]
        ]
        
        # Policy patterns
        policy_patterns = [
            [{"LOWER": "privilege"}, {"LOWER": "leave"}],
            [{"LOWER": "casual"}, {"LOWER": "leave"}],
            [{"LOWER": "sick"}, {"LOWER": "leave"}],
            [{"LOWER": "maternity"}, {"LOWER": "leave"}],
            [{"LOWER": "sexual"}, {"LOWER": "harassment"}],
            [{"LOWER": "travel"}, {"LOWER": "policy"}],
            [{"LOWER": "lta"}, {"LOWER": "policy"}]
        ]
        
        # Personnel patterns
        personnel_patterns = [
            [{"LOWER": "anita"}, {"LOWER": "nagar"}],
            [{"LOWER": "twisha"}, {"LOWER": "hathi"}],
            [{"LOWER": "ami"}, {"LOWER": "rughani"}],
            [{"LOWER": "pallabi"}, {"LOWER": "sarkar"}],
            [{"LOWER": "ankita"}, {"LOWER": "desai"}],
            [{"LOWER": "dr."}, {"LOWER": "harshadkumar"}, {"LOWER": "panjkar"}]
        ]
        
        self.matcher.add("ORG", org_patterns)
        self.matcher.add("DEPT", dept_patterns)
        self.matcher.add("POLICY", policy_patterns)
        self.matcher.add("PERSON", personnel_patterns)
        
        # Add phrase patterns for exact matches
        org_phrases = ["NUVO AI Pvt Ltd", "Meril Life Sciences", "Meril Healthcare", "Meril Diagnostics"]
        self.phrase_matcher.add("ORG_PHRASE", [self.nlp(phrase) for phrase in org_phrases])
        
    def extract_entities(self, text: str) -> Dict[str, List[Dict]]:
        doc = self.nlp(text)
        entities = {
            "organizations": [],
            "departments": [],
            "policies": [],
            "personnel": [],
            "dates": [],
            "money": [],
            "locations": []
        }
        
        # Extract standard spaCy entities
        for ent in doc.ents:
            if ent.label_ == "ORG":
                entities["organizations"].append({
                    "text": ent.text,
                    "start": ent.start_char,
                    "end": ent.end_char,
                    "confidence": 0.8,
                    "source": "spacy"
                })
            elif ent.label_ == "PERSON":
                entities["personnel"].append({
                    "text": ent.text,
                    "start": ent.start_char,
                    "end": ent.end_char,
                    "confidence": 0.8,
                    "source": "spacy"
                })
            elif ent.label_ == "DATE":
                entities["dates"].append({
                    "text": ent.text,
                    "start": ent.start_char,
                    "end": ent.end_char,
                    "confidence": 0.8,
                    "source": "spacy"
                })
            elif ent.label_ == "MONEY":
                entities["money"].append({
                    "text": ent.text,
                    "start": ent.start_char,
                    "end": ent.end_char,
                    "confidence": 0.8,
                    "source": "spacy"
                })
            elif ent.label_ == "GPE":
                entities["locations"].append({
                    "text": ent.text,
                    "start": ent.start_char,
                    "end": ent.end_char,
                    "confidence": 0.8,
                    "source": "spacy"
                })
        
        # Extract custom entities using matchers
        matches = self.matcher(doc)
        for match_id, start, end in matches:
            label = self.nlp.vocab.strings[match_id]
            span = doc[start:end]
            
            if label == "ORG":
                entities["organizations"].append({
                    "text": span.text,
                    "start": span.start_char,
                    "end": span.end_char,
                    "confidence": 0.9,
                    "source": "custom"
                })
            elif label == "DEPT":
                entities["departments"].append({
                    "text": span.text,
                    "start": span.start_char,
                    "end": span.end_char,
                    "confidence": 0.9,
                    "source": "custom"
                })
            elif label == "POLICY":
                entities["policies"].append({
                    "text": span.text,
                    "start": span.start_char,
                    "end": span.end_char,
                    "confidence": 0.9,
                    "source": "custom"
                })
            elif label == "PERSON":
                entities["personnel"].append({
                    "text": span.text,
                    "start": span.start_char,
                    "end": span.end_char,
                    "confidence": 0.95,
                    "source": "custom"
                })
        
        # Extract phrase matches
        phrase_matches = self.phrase_matcher(doc)
        for match_id, start, end in phrase_matches:
            span = doc[start:end]
            entities["organizations"].append({
                "text": span.text,
                "start": span.start_char,
                "end": span.end_char,
                "confidence": 0.95,
                "source": "phrase"
            })
        
        # Remove duplicates and sort by confidence
        for entity_type in entities:
            entities[entity_type] = self._deduplicate_entities(entities[entity_type])
            
        return entities
    
    def _deduplicate_entities(self, entities: List[Dict]) -> List[Dict]:
        seen = set()
        unique_entities = []
        
        for entity in sorted(entities, key=lambda x: x['confidence'], reverse=True):
            key = (entity['text'].lower(), entity['start'], entity['end'])
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)
                
        return unique_entities