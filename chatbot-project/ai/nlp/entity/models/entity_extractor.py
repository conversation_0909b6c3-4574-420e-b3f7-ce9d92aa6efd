import spacy
from typing import List, Dict

class EntityExtractor:
    def __init__(self):
        self.nlp = spacy.load("en_core_web_sm")
        self.org_patterns = ["NuvoAi", "Meril", "MerilLifeSciences", "MerilDiagnostics"]
        self.dept_patterns = ["HR", "Finance", "Technical", "AI", "Product", "Engineering"]
        
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        doc = self.nlp(text)
        
        return {
            "organizations": self._extract_organizations(text),
            "departments": self._extract_departments(text),
            "persons": [ent.text for ent in doc.ents if ent.label_ == "PERSON"],
            "dates": [ent.text for ent in doc.ents if ent.label_ == "DATE"]
        }
    
    def _extract_organizations(self, text: str) -> List[str]:
        return [org for org in self.org_patterns if org.lower() in text.lower()]
    
    def _extract_departments(self, text: str) -> List[str]:
        return [dept for dept in self.dept_patterns if dept.lower() in text.lower()]