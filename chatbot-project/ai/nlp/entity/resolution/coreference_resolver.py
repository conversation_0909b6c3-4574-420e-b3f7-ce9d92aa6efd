import spacy
from typing import Dict, List, Tuple, Set
import re

class CoreferenceResolver:
    def __init__(self):
        self.nlp = spacy.load("en_core_web_sm")
        self.pronouns = {
            "personal": ["he", "she", "it", "they", "him", "her", "them"],
            "possessive": ["his", "her", "its", "their", "hers", "theirs"],
            "demonstrative": ["this", "that", "these", "those"],
            "relative": ["who", "whom", "which", "that"]
        }
        
    def resolve_coreferences(self, documents: List[str]) -> Dict[str, List[Dict]]:
        """Resolve entity coreferences across multiple documents"""
        entity_mentions = {}
        resolved_entities = {}
        
        # Extract entities from all documents
        for doc_id, doc_text in enumerate(documents):
            doc_entities = self._extract_document_entities(doc_text, doc_id)
            entity_mentions[doc_id] = doc_entities
        
        # Build coreference chains
        coreference_chains = self._build_coreference_chains(entity_mentions)
        
        # Resolve entities using chains
        resolved_entities = self._resolve_using_chains(entity_mentions, coreference_chains)
        
        return resolved_entities
    
    def _extract_document_entities(self, text: str, doc_id: int) -> Dict[str, List[Dict]]:
        doc = self.nlp(text)
        entities = {
            "organizations": [],
            "personnel": [],
            "policies": [],
            "pronouns": []
        }
        
        # Extract named entities
        for ent in doc.ents:
            entity_info = {
                "text": ent.text,
                "start": ent.start_char,
                "end": ent.end_char,
                "doc_id": doc_id,
                "sentence_id": self._get_sentence_id(doc, ent.start),
                "label": ent.label_
            }
            
            if ent.label_ == "ORG":
                entities["organizations"].append(entity_info)
            elif ent.label_ == "PERSON":
                entities["personnel"].append(entity_info)
        
        # Extract pronouns and their potential antecedents
        for token in doc:
            if token.text.lower() in self.pronouns["personal"] + self.pronouns["possessive"]:
                pronoun_info = {
                    "text": token.text,
                    "start": token.idx,
                    "end": token.idx + len(token.text),
                    "doc_id": doc_id,
                    "sentence_id": self._get_sentence_id(doc, token.i),
                    "pos": token.pos_,
                    "potential_antecedents": self._find_potential_antecedents(doc, token)
                }
                entities["pronouns"].append(pronoun_info)
        
        return entities
    
    def _get_sentence_id(self, doc, token_pos: int) -> int:
        for i, sent in enumerate(doc.sents):
            if sent.start <= token_pos < sent.end:
                return i
        return 0
    
    def _find_potential_antecedents(self, doc, pronoun_token) -> List[Dict]:
        antecedents = []
        
        # Look for antecedents in the same sentence and previous sentences
        current_sent_id = self._get_sentence_id(doc, pronoun_token.i)
        
        for sent_id, sent in enumerate(doc.sents):
            if sent_id > current_sent_id:
                break
                
            for ent in sent.ents:
                if ent.label_ in ["PERSON", "ORG"] and ent.end <= pronoun_token.i:
                    # Calculate distance and compatibility
                    distance = pronoun_token.i - ent.end
                    compatibility = self._check_pronoun_compatibility(pronoun_token.text, ent.label_)
                    
                    antecedents.append({
                        "text": ent.text,
                        "label": ent.label_,
                        "distance": distance,
                        "compatibility": compatibility,
                        "sentence_id": sent_id
                    })
        
        # Sort by compatibility and distance
        antecedents.sort(key=lambda x: (x["compatibility"], -x["distance"]), reverse=True)
        return antecedents[:3]  # Top 3 candidates
    
    def _check_pronoun_compatibility(self, pronoun: str, entity_label: str) -> float:
        pronoun_lower = pronoun.lower()
        
        if entity_label == "PERSON":
            if pronoun_lower in ["he", "him", "his"]:
                return 0.8  # Assume male unless specified
            elif pronoun_lower in ["she", "her", "hers"]:
                return 0.8  # Assume female unless specified
            elif pronoun_lower in ["they", "them", "their", "theirs"]:
                return 0.9  # Gender neutral
        elif entity_label == "ORG":
            if pronoun_lower in ["it", "its", "they", "them", "their", "theirs"]:
                return 0.9
        
        return 0.1
    
    def _build_coreference_chains(self, entity_mentions: Dict) -> List[List[Dict]]:
        chains = []
        all_entities = []
        
        # Collect all entities across documents
        for doc_id, doc_entities in entity_mentions.items():
            for entity_type, entities in doc_entities.items():
                if entity_type != "pronouns":
                    for entity in entities:
                        entity["type"] = entity_type
                        all_entities.append(entity)
        
        # Build chains using string matching and context
        used_entities = set()
        
        for i, entity1 in enumerate(all_entities):
            if i in used_entities:
                continue
                
            chain = [entity1]
            used_entities.add(i)
            
            for j, entity2 in enumerate(all_entities[i+1:], i+1):
                if j in used_entities:
                    continue
                    
                if self._are_coreferent(entity1, entity2):
                    chain.append(entity2)
                    used_entities.add(j)
            
            if len(chain) > 1:
                chains.append(chain)
        
        return chains
    
    def _are_coreferent(self, entity1: Dict, entity2: Dict) -> bool:
        # Same text (exact match)
        if entity1["text"].lower() == entity2["text"].lower():
            return True
        
        # Partial name match for persons
        if entity1.get("type") == "personnel" and entity2.get("type") == "personnel":
            return self._partial_name_match(entity1["text"], entity2["text"])
        
        # Organization name variations
        if entity1.get("type") == "organizations" and entity2.get("type") == "organizations":
            return self._organization_match(entity1["text"], entity2["text"])
        
        return False
    
    def _partial_name_match(self, name1: str, name2: str) -> bool:
        # Split names and check for overlap
        parts1 = set(name1.lower().split())
        parts2 = set(name2.lower().split())
        
        # Remove common titles
        titles = {"mr", "ms", "mrs", "dr", "prof"}
        parts1 -= titles
        parts2 -= titles
        
        if not parts1 or not parts2:
            return False
        
        # Check if there's significant overlap
        overlap = len(parts1 & parts2)
        min_parts = min(len(parts1), len(parts2))
        
        return overlap / min_parts >= 0.5
    
    def _organization_match(self, org1: str, org2: str) -> bool:
        # Normalize organization names
        org1_norm = re.sub(r'\b(pvt\.?\s*ltd\.?|ltd\.?|inc\.?)\b', '', org1.lower()).strip()
        org2_norm = re.sub(r'\b(pvt\.?\s*ltd\.?|ltd\.?|inc\.?)\b', '', org2.lower()).strip()
        
        # Check for substring match
        return org1_norm in org2_norm or org2_norm in org1_norm
    
    def _resolve_using_chains(self, entity_mentions: Dict, chains: List[List[Dict]]) -> Dict[str, List[Dict]]:
        resolved_entities = {}
        
        # Create mapping from entity to canonical form
        entity_to_canonical = {}
        for chain in chains:
            # Choose the most complete/formal name as canonical
            canonical = max(chain, key=lambda x: len(x["text"]))
            for entity in chain:
                key = (entity["doc_id"], entity["start"], entity["end"])
                entity_to_canonical[key] = canonical
        
        # Apply resolution to all documents
        for doc_id, doc_entities in entity_mentions.items():
            if doc_id not in resolved_entities:
                resolved_entities[doc_id] = {}
            
            for entity_type, entities in doc_entities.items():
                if entity_type == "pronouns":
                    continue
                    
                resolved_list = []
                for entity in entities:
                    key = (entity["doc_id"], entity["start"], entity["end"])
                    if key in entity_to_canonical:
                        resolved_entity = entity.copy()
                        canonical = entity_to_canonical[key]
                        resolved_entity["canonical_form"] = canonical["text"]
                        resolved_entity["coreference_chain_id"] = id(canonical)
                        resolved_list.append(resolved_entity)
                    else:
                        resolved_list.append(entity)
                
                resolved_entities[doc_id][entity_type] = resolved_list
        
        return resolved_entities
    
    def extract_entity_relationships(self, resolved_entities: Dict) -> List[Dict]:
        """Extract relationships between entities across documents"""
        relationships = []
        
        # Collect all entities with their canonical forms
        canonical_entities = {}
        for doc_id, doc_entities in resolved_entities.items():
            for entity_type, entities in doc_entities.items():
                for entity in entities:
                    canonical_form = entity.get("canonical_form", entity["text"])
                    if canonical_form not in canonical_entities:
                        canonical_entities[canonical_form] = {
                            "type": entity_type,
                            "mentions": []
                        }
                    canonical_entities[canonical_form]["mentions"].append(entity)
        
        # Extract relationships based on co-occurrence patterns
        for doc_id, doc_entities in resolved_entities.items():
            doc_orgs = doc_entities.get("organizations", [])
            doc_personnel = doc_entities.get("personnel", [])
            
            # Person-Organization relationships
            for person in doc_personnel:
                for org in doc_orgs:
                    # Check if they appear in the same document/context
                    person_canonical = person.get("canonical_form", person["text"])
                    org_canonical = org.get("canonical_form", org["text"])
                    
                    relationships.append({
                        "type": "works_at",
                        "source": person_canonical,
                        "target": org_canonical,
                        "confidence": 0.7,
                        "evidence_doc": doc_id
                    })
        
        return relationships