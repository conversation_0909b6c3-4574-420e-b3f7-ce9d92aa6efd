import re
from typing import Dict, List, Tuple
from difflib import SequenceMatcher

class EntityNormalizer:
    def __init__(self):
        self.normalization_rules = {
            "organizations": {
                "NUVO AI": ["nuvo ai", "nuvoai", "nai", "nuvo ai pvt ltd", "nuvo ai pvt. ltd."],
                "Meril Life Sciences": ["meril", "mls", "meril life sciences pvt ltd", "meril life sciences pvt. ltd."],
                "Meril Healthcare": ["meril healthcare pvt ltd", "meril healthcare pvt. ltd."],
                "Meril Diagnostics": ["meril diagnostics pvt ltd", "meril diagnostics pvt. ltd."],
                "Meril Endo-Surgery": ["meril endo surgery", "meril endosurgery", "meril endo-surgery pvt ltd"]
            },
            "personnel": {
                "Anita Nagar": ["anita", "ms anita nagar", "ms. anita nagar"],
                "Twisha Hathi": ["twisha", "ms twisha hathi", "ms. twisha hathi"],
                "Ami <PERSON>": ["ami", "ms ami rughani", "ms. ami rughani"],
                "<PERSON>llabi <PERSON>rkar": ["pallabi", "ms pallabi sarkar", "ms. pallabi sarkar"],
                "<PERSON>kita <PERSON>ai": ["ankita", "ms ankita desai", "ms. ankita desai"],
                "Dr. <PERSON><PERSON><PERSON>kumar <PERSON>jkar": ["dr panjkar", "harshad<PERSON> panjkar", "dr. panjkar"]
            },
            "policies": {
                "privilege leave": ["pl", "privilege leaves", "privileged leave", "annual leave"],
                "casual leave": ["cl", "casual leaves"],
                "sick leave": ["sl", "sick leaves", "medical leave"],
                "maternity leave": ["maternity leaves", "maternal leave"],
                "sexual harassment": ["harassment", "sexual harassment policy", "harassment policy"],
                "travel policy": ["travel policies", "tour policy", "tour and travel policy"]
            },
            "departments": {
                "HR Department": ["hr", "human resources", "hr dept", "human resource department"],
                "Finance Department": ["finance", "finance dept", "accounts"],
                "Technical Department": ["technical", "tech dept", "it department", "it"],
                "AI Department": ["ai", "artificial intelligence", "ai dept"]
            }
        }
        
        self.abbreviation_expansions = {
            "pl": "privilege leave",
            "cl": "casual leave", 
            "sl": "sick leave",
            "lta": "leave travel assistance",
            "hr": "human resources",
            "dgm": "deputy general manager",
            "gm": "general manager",
            "hod": "head of department",
            "cin": "corporate identification number"
        }
    
    def normalize_entities(self, entities: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
        normalized_entities = {}
        
        for entity_type, entity_list in entities.items():
            normalized_entities[entity_type] = []
            
            for entity in entity_list:
                normalized_entity = entity.copy()
                normalized_text = self._normalize_entity_text(entity["text"], entity_type)
                
                if normalized_text != entity["text"]:
                    normalized_entity["original_text"] = entity["text"]
                    normalized_entity["text"] = normalized_text
                    normalized_entity["normalized"] = True
                else:
                    normalized_entity["normalized"] = False
                
                normalized_entities[entity_type].append(normalized_entity)
        
        return normalized_entities
    
    def _normalize_entity_text(self, text: str, entity_type: str) -> str:
        text_lower = text.lower().strip()
        
        # Expand abbreviations first
        expanded_text = self._expand_abbreviations(text_lower)
        
        # Apply normalization rules
        if entity_type in self.normalization_rules:
            for canonical_form, variations in self.normalization_rules[entity_type].items():
                if expanded_text in [v.lower() for v in variations]:
                    return canonical_form
                
                # Fuzzy matching for close variations
                for variation in variations:
                    if self._similarity_score(expanded_text, variation.lower()) > 0.8:
                        return canonical_form
        
        # Clean up the text
        cleaned_text = self._clean_text(expanded_text)
        
        # Try to match with canonical forms using fuzzy matching
        if entity_type in self.normalization_rules:
            best_match = None
            best_score = 0.0
            
            for canonical_form in self.normalization_rules[entity_type].keys():
                score = self._similarity_score(cleaned_text, canonical_form.lower())
                if score > best_score and score > 0.7:
                    best_score = score
                    best_match = canonical_form
            
            if best_match:
                return best_match
        
        return self._title_case(cleaned_text)
    
    def _expand_abbreviations(self, text: str) -> str:
        words = text.split()
        expanded_words = []
        
        for word in words:
            # Remove punctuation for matching
            clean_word = re.sub(r'[^\w]', '', word.lower())
            if clean_word in self.abbreviation_expansions:
                expanded_words.append(self.abbreviation_expansions[clean_word])
            else:
                expanded_words.append(word)
        
        return ' '.join(expanded_words)
    
    def _clean_text(self, text: str) -> str:
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common prefixes/suffixes
        text = re.sub(r'\b(pvt\.?\s*ltd\.?|ltd\.?|inc\.?|corp\.?)\b', '', text, flags=re.IGNORECASE)
        text = re.sub(r'\b(mr\.?|ms\.?|mrs\.?|dr\.?)\s*', '', text, flags=re.IGNORECASE)
        
        # Clean up punctuation
        text = re.sub(r'[^\w\s-]', '', text)
        
        return text.strip()
    
    def _title_case(self, text: str) -> str:
        # Custom title case that handles organizational names properly
        words = text.split()
        title_words = []
        
        for word in words:
            if word.lower() in ['and', 'of', 'the', 'in', 'at', 'for', 'to', 'with']:
                title_words.append(word.lower())
            elif word.upper() in ['AI', 'IT', 'HR', 'CEO', 'CTO', 'DGM', 'GM']:
                title_words.append(word.upper())
            else:
                title_words.append(word.capitalize())
        
        return ' '.join(title_words)
    
    def _similarity_score(self, text1: str, text2: str) -> float:
        return SequenceMatcher(None, text1, text2).ratio()
    
    def disambiguate_entities(self, entities: Dict[str, List[Dict]], context: str = "") -> Dict[str, List[Dict]]:
        disambiguated_entities = {}
        
        for entity_type, entity_list in entities.items():
            disambiguated_entities[entity_type] = []
            
            # Group similar entities
            entity_groups = self._group_similar_entities(entity_list)
            
            for group in entity_groups:
                if len(group) == 1:
                    disambiguated_entities[entity_type].append(group[0])
                else:
                    # Resolve ambiguity using context and confidence scores
                    best_entity = self._resolve_ambiguity(group, context)
                    disambiguated_entities[entity_type].append(best_entity)
        
        return disambiguated_entities
    
    def _group_similar_entities(self, entities: List[Dict]) -> List[List[Dict]]:
        groups = []
        used_indices = set()
        
        for i, entity1 in enumerate(entities):
            if i in used_indices:
                continue
                
            group = [entity1]
            used_indices.add(i)
            
            for j, entity2 in enumerate(entities[i+1:], i+1):
                if j in used_indices:
                    continue
                    
                if self._are_similar_entities(entity1, entity2):
                    group.append(entity2)
                    used_indices.add(j)
            
            groups.append(group)
        
        return groups
    
    def _are_similar_entities(self, entity1: Dict, entity2: Dict) -> bool:
        # Check text similarity
        text_similarity = self._similarity_score(entity1["text"].lower(), entity2["text"].lower())
        
        # Check position overlap
        pos_overlap = self._position_overlap(entity1, entity2)
        
        return text_similarity > 0.8 or pos_overlap > 0.5
    
    def _position_overlap(self, entity1: Dict, entity2: Dict) -> float:
        start1, end1 = entity1.get("start", 0), entity1.get("end", 0)
        start2, end2 = entity2.get("start", 0), entity2.get("end", 0)
        
        if end1 <= start2 or end2 <= start1:
            return 0.0
        
        overlap = min(end1, end2) - max(start1, start2)
        total = max(end1, end2) - min(start1, start2)
        
        return overlap / total if total > 0 else 0.0
    
    def _resolve_ambiguity(self, entities: List[Dict], context: str) -> Dict:
        # Choose entity with highest confidence
        best_entity = max(entities, key=lambda x: x.get("confidence", 0))
        
        # Add disambiguation info
        best_entity["disambiguation"] = {
            "ambiguous": True,
            "alternatives": [e["text"] for e in entities if e != best_entity],
            "resolution_method": "confidence_based"
        }
        
        return best_entity