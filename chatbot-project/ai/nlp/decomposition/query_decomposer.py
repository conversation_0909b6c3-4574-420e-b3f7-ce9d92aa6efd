import re
from typing import List, Dict, Tuple
from dataclasses import dataclass

@dataclass
class SubQuery:
    text: str
    intent: str
    priority: int
    dependencies: List[str]

class QueryDecomposer:
    def __init__(self):
        self.conjunction_patterns = [
            r'\band\b', r'\bor\b', r'\balso\b', r'\bplus\b',
            r'\badditionally\b', r'\bfurthermore\b'
        ]
        self.question_patterns = [
            r'\?', r'\bwhat\b', r'\bhow\b', r'\bwhen\b', 
            r'\bwhere\b', r'\bwhy\b', r'\bwho\b'
        ]
        
    def decompose_query(self, query: str) -> Dict:
        # Check if query needs decomposition
        if not self._needs_decomposition(query):
            return {
                "needs_decomposition": False,
                "sub_queries": [SubQuery(query, "unknown", 1, [])],
                "dependency_graph": {},
                "execution_order": [query]
            }
        
        # Extract sub-queries
        sub_queries = self._extract_sub_queries(query)
        
        # Build dependency graph
        dependency_graph = self._build_dependency_graph(sub_queries)
        
        # Determine execution order
        execution_order = self._determine_execution_order(dependency_graph)
        
        return {
            "needs_decomposition": True,
            "sub_queries": sub_queries,
            "dependency_graph": dependency_graph,
            "execution_order": execution_order
        }
    
    def _needs_decomposition(self, query: str) -> bool:
        # Check for conjunctions
        for pattern in self.conjunction_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return True
        
        # Check for multiple questions
        question_count = sum(1 for pattern in self.question_patterns 
                           if re.search(pattern, query, re.IGNORECASE))
        
        return question_count > 1 or len(query.split()) > 20
    
    def _extract_sub_queries(self, query: str) -> List[SubQuery]:
        sub_queries = []
        
        # Split by conjunctions
        parts = re.split(r'\band\b|\bor\b|\balso\b', query, flags=re.IGNORECASE)
        
        for i, part in enumerate(parts):
            part = part.strip()
            if part:
                # Determine intent based on keywords
                intent = self._classify_sub_query(part)
                
                # Determine priority (earlier parts have higher priority)
                priority = len(parts) - i
                
                # Simple dependency detection
                dependencies = self._detect_dependencies(part, parts[:i])
                
                sub_queries.append(SubQuery(part, intent, priority, dependencies))
        
        return sub_queries
    
    def _classify_sub_query(self, text: str) -> str:
        text_lower = text.lower()
        
        if any(word in text_lower for word in ['vacation', 'leave', 'time off']):
            return 'vacation_request'
        elif any(word in text_lower for word in ['policy', 'rule', 'guideline']):
            return 'hr_policy'
        elif any(word in text_lower for word in ['salary', 'bonus', 'payment']):
            return 'finance_query'
        elif any(word in text_lower for word in ['project', 'timeline', 'development']):
            return 'project_info'
        else:
            return 'general_info'
    
    def _detect_dependencies(self, current_part: str, previous_parts: List[str]) -> List[str]:
        dependencies = []
        
        # Check for pronouns or references
        if re.search(r'\bit\b|\bthis\b|\bthat\b|\bthey\b', current_part, re.IGNORECASE):
            if previous_parts:
                dependencies.append(previous_parts[-1].strip())
        
        return dependencies
    
    def _build_dependency_graph(self, sub_queries: List[SubQuery]) -> Dict:
        graph = {}
        
        for sq in sub_queries:
            graph[sq.text] = {
                "intent": sq.intent,
                "priority": sq.priority,
                "dependencies": sq.dependencies
            }
        
        return graph
    
    def _determine_execution_order(self, dependency_graph: Dict) -> List[str]:
        # Simple topological sort based on dependencies and priority
        ordered = []
        remaining = list(dependency_graph.keys())
        
        while remaining:
            # Find queries with no unresolved dependencies
            ready = []
            for query in remaining:
                deps = dependency_graph[query]["dependencies"]
                if all(dep in ordered or dep not in remaining for dep in deps):
                    ready.append(query)
            
            if not ready:
                # No dependencies, sort by priority
                ready = [min(remaining, key=lambda x: dependency_graph[x]["priority"])]
            
            # Sort ready queries by priority
            ready.sort(key=lambda x: dependency_graph[x]["priority"], reverse=True)
            
            # Add to ordered list
            for query in ready:
                ordered.append(query)
                remaining.remove(query)
        
        return ordered