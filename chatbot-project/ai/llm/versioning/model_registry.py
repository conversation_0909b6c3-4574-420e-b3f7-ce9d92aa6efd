import json
import os
import shutil
import hashlib
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

class ModelStatus(Enum):
    TRAINING = "training"
    READY = "ready"
    DEPLOYED = "deployed"
    DEPRECATED = "deprecated"
    FAILED = "failed"

@dataclass
class ModelVersion:
    model_id: str
    version: str
    model_name: str
    model_path: str
    config_path: str
    status: ModelStatus
    created_at: str
    deployed_at: Optional[str] = None
    performance_metrics: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    checksum: Optional[str] = None

class ModelRegistry:
    def __init__(self, registry_path: str = "./model_registry"):
        self.registry_path = registry_path
        self.models_path = os.path.join(registry_path, "models")
        self.metadata_path = os.path.join(registry_path, "metadata")
        self.deployments_path = os.path.join(registry_path, "deployments")
        
        # Create directories
        os.makedirs(self.models_path, exist_ok=True)
        os.makedirs(self.metadata_path, exist_ok=True)
        os.makedirs(self.deployments_path, exist_ok=True)
        
        self.registry_file = os.path.join(registry_path, "registry.json")
        self.models = self._load_registry()
    
    def _load_registry(self) -> Dict[str, ModelVersion]:
        """Load model registry from file"""
        if os.path.exists(self.registry_file):
            try:
                with open(self.registry_file, 'r') as f:
                    data = json.load(f)
                
                models = {}
                for model_id, model_data in data.items():
                    model_data["status"] = ModelStatus(model_data["status"])
                    models[model_id] = ModelVersion(**model_data)
                
                return models
            except Exception as e:
                print(f"Error loading registry: {e}")
                return {}
        
        return {}
    
    def _save_registry(self):
        """Save model registry to file"""
        try:
            data = {}
            for model_id, model_version in self.models.items():
                model_dict = asdict(model_version)
                model_dict["status"] = model_version.status.value
                data[model_id] = model_dict
            
            with open(self.registry_file, 'w') as f:
                json.dump(data, f, indent=2)
        
        except Exception as e:
            print(f"Error saving registry: {e}")
    
    def register_model(self, model_name: str, model_path: str, 
                      config_path: str = None, metadata: Dict[str, Any] = None) -> str:
        """Register a new model version"""
        # Generate model ID and version
        model_id = f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        version = self._generate_version(model_name)
        
        # Calculate checksum
        checksum = self._calculate_checksum(model_path)
        
        # Copy model files to registry
        registry_model_path = os.path.join(self.models_path, model_id)
        os.makedirs(registry_model_path, exist_ok=True)
        
        # Copy model
        model_filename = os.path.basename(model_path)
        registry_model_file = os.path.join(registry_model_path, model_filename)
        
        if os.path.isdir(model_path):
            shutil.copytree(model_path, registry_model_file, dirs_exist_ok=True)
        else:
            shutil.copy2(model_path, registry_model_file)
        
        # Copy config if provided
        registry_config_path = None
        if config_path and os.path.exists(config_path):
            config_filename = os.path.basename(config_path)
            registry_config_path = os.path.join(registry_model_path, config_filename)
            shutil.copy2(config_path, registry_config_path)
        
        # Create model version
        model_version = ModelVersion(
            model_id=model_id,
            version=version,
            model_name=model_name,
            model_path=registry_model_file,
            config_path=registry_config_path,
            status=ModelStatus.READY,
            created_at=datetime.now().isoformat(),
            metadata=metadata or {},
            checksum=checksum
        )
        
        # Add to registry
        self.models[model_id] = model_version
        self._save_registry()
        
        print(f"Model registered: {model_id} (version {version})")
        return model_id
    
    def get_model(self, model_id: str) -> Optional[ModelVersion]:
        """Get model by ID"""
        return self.models.get(model_id)
    
    def get_latest_version(self, model_name: str) -> Optional[ModelVersion]:
        """Get latest version of a model"""
        model_versions = [
            model for model in self.models.values()
            if model.model_name == model_name and model.status != ModelStatus.DEPRECATED
        ]
        
        if not model_versions:
            return None
        
        # Sort by creation time
        model_versions.sort(key=lambda x: x.created_at, reverse=True)
        return model_versions[0]
    
    def list_models(self, model_name: str = None, status: ModelStatus = None) -> List[ModelVersion]:
        """List models with optional filtering"""
        models = list(self.models.values())
        
        if model_name:
            models = [m for m in models if m.model_name == model_name]
        
        if status:
            models = [m for m in models if m.status == status]
        
        return sorted(models, key=lambda x: x.created_at, reverse=True)
    
    def deploy_model(self, model_id: str, deployment_config: Dict[str, Any] = None) -> bool:
        """Mark model as deployed"""
        if model_id not in self.models:
            print(f"Model {model_id} not found")
            return False
        
        model = self.models[model_id]
        
        # Update status
        model.status = ModelStatus.DEPLOYED
        model.deployed_at = datetime.now().isoformat()
        
        # Save deployment config
        if deployment_config:
            deployment_file = os.path.join(self.deployments_path, f"{model_id}_deployment.json")
            with open(deployment_file, 'w') as f:
                json.dump(deployment_config, f, indent=2)
        
        self._save_registry()
        print(f"Model {model_id} marked as deployed")
        return True
    
    def update_performance_metrics(self, model_id: str, metrics: Dict[str, Any]) -> bool:
        """Update model performance metrics"""
        if model_id not in self.models:
            return False
        
        model = self.models[model_id]
        if model.performance_metrics is None:
            model.performance_metrics = {}
        
        model.performance_metrics.update(metrics)
        model.performance_metrics["last_updated"] = datetime.now().isoformat()
        
        self._save_registry()
        return True
    
    def deprecate_model(self, model_id: str, reason: str = None) -> bool:
        """Deprecate a model version"""
        if model_id not in self.models:
            return False
        
        model = self.models[model_id]
        model.status = ModelStatus.DEPRECATED
        
        if reason:
            if model.metadata is None:
                model.metadata = {}
            model.metadata["deprecation_reason"] = reason
            model.metadata["deprecated_at"] = datetime.now().isoformat()
        
        self._save_registry()
        print(f"Model {model_id} deprecated")
        return True
    
    def create_canary_deployment(self, model_id: str, traffic_percentage: float = 10.0) -> Dict[str, Any]:
        """Create canary deployment configuration"""
        if model_id not in self.models:
            return {"error": "Model not found"}
        
        model = self.models[model_id]
        
        # Get current production model
        production_models = [
            m for m in self.models.values()
            if m.model_name == model.model_name and m.status == ModelStatus.DEPLOYED
        ]
        
        canary_config = {
            "canary_model_id": model_id,
            "canary_version": model.version,
            "traffic_percentage": traffic_percentage,
            "created_at": datetime.now().isoformat(),
            "status": "active"
        }
        
        if production_models:
            production_model = production_models[0]  # Assume latest deployed
            canary_config["production_model_id"] = production_model.model_id
            canary_config["production_version"] = production_model.version
        
        # Save canary config
        canary_file = os.path.join(self.deployments_path, f"canary_{model_id}.json")
        with open(canary_file, 'w') as f:
            json.dump(canary_config, f, indent=2)
        
        print(f"Canary deployment created for {model_id} with {traffic_percentage}% traffic")
        return canary_config
    
    def rollback_deployment(self, model_name: str, target_version: str = None) -> bool:
        """Rollback to previous model version"""
        # Get deployment history
        deployed_models = [
            m for m in self.models.values()
            if m.model_name == model_name and m.deployed_at is not None
        ]
        
        if len(deployed_models) < 2:
            print("No previous version available for rollback")
            return False
        
        # Sort by deployment time
        deployed_models.sort(key=lambda x: x.deployed_at, reverse=True)
        
        current_model = deployed_models[0]
        
        if target_version:
            # Find specific version
            target_model = next(
                (m for m in deployed_models if m.version == target_version), None
            )
            if not target_model:
                print(f"Target version {target_version} not found")
                return False
        else:
            # Use previous version
            target_model = deployed_models[1]
        
        # Update statuses
        current_model.status = ModelStatus.READY
        target_model.status = ModelStatus.DEPLOYED
        target_model.deployed_at = datetime.now().isoformat()
        
        # Save rollback info
        rollback_info = {
            "rollback_at": datetime.now().isoformat(),
            "from_model": current_model.model_id,
            "to_model": target_model.model_id,
            "from_version": current_model.version,
            "to_version": target_model.version
        }
        
        rollback_file = os.path.join(self.deployments_path, f"rollback_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(rollback_file, 'w') as f:
            json.dump(rollback_info, f, indent=2)
        
        self._save_registry()
        print(f"Rolled back {model_name} from {current_model.version} to {target_model.version}")
        return True
    
    def _generate_version(self, model_name: str) -> str:
        """Generate version number for model"""
        existing_versions = [
            m.version for m in self.models.values()
            if m.model_name == model_name
        ]
        
        if not existing_versions:
            return "1.0.0"
        
        # Simple version increment (major.minor.patch)
        latest_version = max(existing_versions)
        parts = latest_version.split('.')
        
        try:
            patch = int(parts[2]) + 1
            return f"{parts[0]}.{parts[1]}.{patch}"
        except:
            return f"{latest_version}.1"
    
    def _calculate_checksum(self, file_path: str) -> str:
        """Calculate file checksum"""
        if os.path.isdir(file_path):
            # For directories, calculate checksum of all files
            checksums = []
            for root, dirs, files in os.walk(file_path):
                for file in sorted(files):
                    file_path_full = os.path.join(root, file)
                    with open(file_path_full, 'rb') as f:
                        checksums.append(hashlib.md5(f.read()).hexdigest())
            
            combined = ''.join(checksums)
            return hashlib.md5(combined.encode()).hexdigest()
        else:
            # Single file checksum
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics"""
        status_counts = {}
        for model in self.models.values():
            status = model.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        model_names = set(m.model_name for m in self.models.values())
        
        return {
            "total_models": len(self.models),
            "unique_model_names": len(model_names),
            "status_breakdown": status_counts,
            "registry_path": self.registry_path,
            "last_updated": datetime.now().isoformat()
        }