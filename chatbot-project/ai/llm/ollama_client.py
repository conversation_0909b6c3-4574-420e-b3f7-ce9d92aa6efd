"""Ollama Client - Uses your local Ollama models."""

import requests
import json
from typing import Dict, Any, Optional, List

class OllamaClient:
    """Client for local Ollama models."""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.available_models = []
        self.default_model = "llama2:latest"  # Your installed model
        self._check_connection()
    
    def _check_connection(self):
        """Check Ollama connection and available models."""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.available_models = [model["name"] for model in data.get("models", [])]
                print(f"✅ Connected to Ollama. Available models: {self.available_models}")
                
                # Set default to deepseek-r1 if available (more capable)
                if "deepseek-r1:latest" in self.available_models:
                    self.default_model = "deepseek-r1:latest"
                    print(f"✅ Using DeepSeek-R1 as default model")
                elif "llama2:latest" in self.available_models:
                    self.default_model = "llama2:latest"
                    print(f"✅ Using Llama2 as default model")
            else:
                print(f"❌ Ollama connection failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Cannot connect to Ollama: {e}")
    
    def generate(self, prompt: str, model: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate response using Ollama model."""
        model = model or self.default_model
        
        if model not in self.available_models:
            return {
                "response": f"Model {model} not available. Available: {self.available_models}",
                "error": "model_not_found"
            }
        
        try:
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "top_p": kwargs.get("top_p", 0.9),
                    "max_tokens": kwargs.get("max_tokens", 500)
                }
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "response": result.get("response", ""),
                    "model": model,
                    "done": result.get("done", True),
                    "context": result.get("context", [])
                }
            else:
                return {
                    "response": f"Error: {response.status_code}",
                    "error": "generation_failed"
                }
                
        except Exception as e:
            return {
                "response": f"Generation error: {str(e)}",
                "error": "exception"
            }
    
    def chat(self, messages: List[Dict[str, str]], model: Optional[str] = None) -> Dict[str, Any]:
        """Chat with Ollama model using conversation format."""
        model = model or self.default_model
        
        # Convert messages to a single prompt for Ollama
        prompt = self._format_chat_prompt(messages)
        return self.generate(prompt, model)
    
    def _format_chat_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Format chat messages into a single prompt."""
        formatted = ""
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "system":
                formatted += f"System: {content}\n\n"
            elif role == "user":
                formatted += f"Human: {content}\n\n"
            elif role == "assistant":
                formatted += f"Assistant: {content}\n\n"
        
        formatted += "Assistant: "
        return formatted
    
    def get_model_info(self, model: Optional[str] = None) -> Dict[str, Any]:
        """Get information about a specific model."""
        model = model or self.default_model
        
        try:
            response = requests.post(
                f"{self.base_url}/api/show",
                json={"name": model},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Failed to get model info: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"Exception getting model info: {str(e)}"}
    
    def is_available(self) -> bool:
        """Check if Ollama service is available."""
        return len(self.available_models) > 0