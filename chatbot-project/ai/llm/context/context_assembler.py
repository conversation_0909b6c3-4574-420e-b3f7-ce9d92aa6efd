"""Context assembly for LLM prompts."""

from typing import Dict, List, Any, Optional
from datetime import datetime

class ContextAssembler:
    def __init__(self):
        self.max_context_length = 4000
        self.context_priorities = {
            "user_query": 1.0,
            "conversation_history": 0.8,
            "retrieved_knowledge": 0.9,
            "agent_context": 0.7,
            "tool_results": 0.85,
            "reasoning_context": 0.75
        }
    
    def assemble_context(self, components: Dict[str, Any]) -> Dict[str, Any]:
        """Assemble context from various components."""
        context = {
            "timestamp": datetime.utcnow().isoformat(),
            "components": {},
            "total_length": 0,
            "truncated": False
        }
        
        # Sort components by priority
        sorted_components = sorted(
            components.items(),
            key=lambda x: self.context_priorities.get(x[0], 0.5),
            reverse=True
        )
        
        current_length = 0
        
        for component_name, component_data in sorted_components:
            component_text = self._format_component(component_name, component_data)
            component_length = len(component_text)
            
            if current_length + component_length <= self.max_context_length:
                context["components"][component_name] = {
                    "data": component_data,
                    "formatted_text": component_text,
                    "length": component_length
                }
                current_length += component_length
            else:
                # Try to fit partial component
                remaining_space = self.max_context_length - current_length
                if remaining_space > 100:  # Minimum useful space
                    truncated_text = self._truncate_component(component_text, remaining_space)
                    context["components"][component_name] = {
                        "data": component_data,
                        "formatted_text": truncated_text,
                        "length": len(truncated_text),
                        "truncated": True
                    }
                    context["truncated"] = True
                    current_length += len(truncated_text)
                break
        
        context["total_length"] = current_length
        return context
    
    def _format_component(self, component_name: str, component_data: Any) -> str:
        """Format component data into text."""
        if component_name == "user_query":
            return f"User Query: {component_data}\n\n"
        
        elif component_name == "conversation_history":
            if isinstance(component_data, list):
                history_text = "Conversation History:\n"
                for msg in component_data[-5:]:  # Last 5 messages
                    role = "User" if msg.get("is_user") else "Assistant"
                    history_text += f"{role}: {msg.get('content', '')}\n"
                return history_text + "\n"
            return f"Conversation History: {component_data}\n\n"
        
        elif component_name == "retrieved_knowledge":
            if isinstance(component_data, list):
                knowledge_text = "Retrieved Knowledge:\n"
                for i, doc in enumerate(component_data[:3]):  # Top 3 documents
                    knowledge_text += f"{i+1}. {doc.get('title', 'Document')}: {doc.get('content', '')[:200]}...\n"
                return knowledge_text + "\n"
            return f"Retrieved Knowledge: {component_data}\n\n"
        
        elif component_name == "agent_context":
            agent_text = "Agent Context:\n"
            if isinstance(component_data, dict):
                for key, value in component_data.items():
                    agent_text += f"- {key}: {value}\n"
            return agent_text + "\n"
        
        elif component_name == "tool_results":
            if isinstance(component_data, list):
                tools_text = "Tool Results:\n"
                for result in component_data:
                    tool_name = result.get("tool_name", "Unknown")
                    tool_result = result.get("result", "No result")
                    tools_text += f"- {tool_name}: {tool_result}\n"
                return tools_text + "\n"
            return f"Tool Results: {component_data}\n\n"
        
        elif component_name == "reasoning_context":
            if isinstance(component_data, dict):
                reasoning_text = "Reasoning Context:\n"
                if "previous_steps" in component_data:
                    reasoning_text += "Previous Steps:\n"
                    for step in component_data["previous_steps"]:
                        reasoning_text += f"- {step}\n"
                if "current_goal" in component_data:
                    reasoning_text += f"Current Goal: {component_data['current_goal']}\n"
                return reasoning_text + "\n"
            return f"Reasoning Context: {component_data}\n\n"
        
        else:
            return f"{component_name}: {str(component_data)}\n\n"
    
    def _truncate_component(self, text: str, max_length: int) -> str:
        """Truncate component text to fit within length limit."""
        if len(text) <= max_length:
            return text
        
        # Try to truncate at sentence boundary
        sentences = text.split('.')
        truncated = ""
        
        for sentence in sentences:
            if len(truncated + sentence + ".") <= max_length - 20:  # Leave space for "..."
                truncated += sentence + "."
            else:
                break
        
        if truncated:
            return truncated + "..."
        else:
            # Truncate at word boundary
            words = text.split()
            truncated_words = []
            current_length = 0
            
            for word in words:
                if current_length + len(word) + 1 <= max_length - 3:
                    truncated_words.append(word)
                    current_length += len(word) + 1
                else:
                    break
            
            return " ".join(truncated_words) + "..."
    
    def create_prompt_context(self, query: str, context_data: Dict[str, Any], 
                            prompt_template: str = None) -> str:
        """Create complete prompt with context."""
        # Assemble context
        context = self.assemble_context(context_data)
        
        # Build context string
        context_string = ""
        for component_name, component_info in context["components"].items():
            context_string += component_info["formatted_text"]
        
        # Use template or default format
        if prompt_template:
            return prompt_template.format(
                context=context_string,
                query=query,
                timestamp=context["timestamp"]
            )
        else:
            return f"""Context Information:
{context_string}

User Query: {query}

Please provide a comprehensive response based on the context information above."""
    
    def optimize_context_for_reasoning(self, reasoning_type: str, context_data: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize context assembly for specific reasoning types."""
        if reasoning_type == "analytical":
            # Prioritize evidence and data
            self.context_priorities["retrieved_knowledge"] = 1.0
            self.context_priorities["tool_results"] = 0.95
            
        elif reasoning_type == "conversational":
            # Prioritize conversation history
            self.context_priorities["conversation_history"] = 1.0
            self.context_priorities["user_query"] = 0.95
            
        elif reasoning_type == "problem_solving":
            # Prioritize reasoning context and tools
            self.context_priorities["reasoning_context"] = 1.0
            self.context_priorities["tool_results"] = 0.9
            
        return self.assemble_context(context_data)
    
    def get_context_summary(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get summary of assembled context."""
        summary = {
            "total_components": len(context["components"]),
            "total_length": context["total_length"],
            "truncated": context["truncated"],
            "components_included": list(context["components"].keys()),
            "length_by_component": {
                name: info["length"] 
                for name, info in context["components"].items()
            }
        }
        
        return summary