import time
import psutil
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import deque
import json

class PerformanceMonitor:
    def __init__(self, window_size: int = 1000):
        self.window_size = window_size
        self.latency_history = deque(maxlen=window_size)
        self.throughput_history = deque(maxlen=window_size)
        self.quality_metrics = deque(maxlen=window_size)
        self.system_metrics = deque(maxlen=window_size)
        
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Performance thresholds
        self.thresholds = {
            "max_latency": 5.0,  # seconds
            "min_throughput": 1.0,  # requests/second
            "max_memory_usage": 90.0,  # percentage
            "max_gpu_memory": 90.0  # percentage
        }
        
        # Alerts
        self.alerts = []
        self.alert_callbacks = []
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        print("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        print("Performance monitoring stopped")
    
    def record_latency(self, latency: float, request_id: str = None):
        """Record request latency"""
        timestamp = datetime.now()
        
        self.latency_history.append({
            "timestamp": timestamp.isoformat(),
            "latency": latency,
            "request_id": request_id
        })
        
        # Check latency threshold
        if latency > self.thresholds["max_latency"]:
            self._trigger_alert("high_latency", {
                "latency": latency,
                "threshold": self.thresholds["max_latency"],
                "request_id": request_id
            })
    
    def record_throughput(self, requests_count: int, time_window: float):
        """Record throughput measurement"""
        throughput = requests_count / time_window if time_window > 0 else 0
        timestamp = datetime.now()
        
        self.throughput_history.append({
            "timestamp": timestamp.isoformat(),
            "throughput": throughput,
            "requests_count": requests_count,
            "time_window": time_window
        })
        
        # Check throughput threshold
        if throughput < self.thresholds["min_throughput"]:
            self._trigger_alert("low_throughput", {
                "throughput": throughput,
                "threshold": self.thresholds["min_throughput"]
            })
    
    def record_quality_metric(self, metric_name: str, value: float, context: Dict[str, Any] = None):
        """Record quality metrics"""
        timestamp = datetime.now()
        
        self.quality_metrics.append({
            "timestamp": timestamp.isoformat(),
            "metric_name": metric_name,
            "value": value,
            "context": context or {}
        })
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Sleep for monitoring interval
                time.sleep(10)  # Monitor every 10 seconds
                
            except Exception as e:
                print(f"Monitoring error: {e}")
                time.sleep(5)
    
    def _collect_system_metrics(self):
        """Collect system performance metrics"""
        timestamp = datetime.now()
        
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        metrics = {
            "timestamp": timestamp.isoformat(),
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_used_gb": memory.used / (1024**3),
            "memory_total_gb": memory.total / (1024**3)
        }
        
        # GPU metrics if available
        try:
            import torch
            if torch.cuda.is_available():
                gpu_metrics = []
                for i in range(torch.cuda.device_count()):
                    gpu_memory_used = torch.cuda.memory_allocated(i) / (1024**3)
                    gpu_memory_total = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                    gpu_utilization = (gpu_memory_used / gpu_memory_total) * 100
                    
                    gpu_metrics.append({
                        "device": i,
                        "memory_used_gb": gpu_memory_used,
                        "memory_total_gb": gpu_memory_total,
                        "utilization_percent": gpu_utilization
                    })
                    
                    # Check GPU memory threshold
                    if gpu_utilization > self.thresholds["max_gpu_memory"]:
                        self._trigger_alert("high_gpu_memory", {
                            "device": i,
                            "utilization": gpu_utilization,
                            "threshold": self.thresholds["max_gpu_memory"]
                        })
                
                metrics["gpu_metrics"] = gpu_metrics
        except:
            pass
        
        self.system_metrics.append(metrics)
        
        # Check memory threshold
        if memory.percent > self.thresholds["max_memory_usage"]:
            self._trigger_alert("high_memory_usage", {
                "usage": memory.percent,
                "threshold": self.thresholds["max_memory_usage"]
            })
    
    def get_latency_stats(self, minutes: int = 60) -> Dict[str, Any]:
        """Get latency statistics for specified time window"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        recent_latencies = [
            record for record in self.latency_history
            if datetime.fromisoformat(record["timestamp"]) > cutoff_time
        ]
        
        if not recent_latencies:
            return {"error": "No latency data available"}
        
        latencies = [record["latency"] for record in recent_latencies]
        
        return {
            "count": len(latencies),
            "avg_latency": sum(latencies) / len(latencies),
            "min_latency": min(latencies),
            "max_latency": max(latencies),
            "p50_latency": self._percentile(latencies, 50),
            "p95_latency": self._percentile(latencies, 95),
            "p99_latency": self._percentile(latencies, 99),
            "time_window_minutes": minutes
        }
    
    def get_throughput_stats(self, minutes: int = 60) -> Dict[str, Any]:
        """Get throughput statistics"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        recent_throughput = [
            record for record in self.throughput_history
            if datetime.fromisoformat(record["timestamp"]) > cutoff_time
        ]
        
        if not recent_throughput:
            return {"error": "No throughput data available"}
        
        throughputs = [record["throughput"] for record in recent_throughput]
        
        return {
            "count": len(throughputs),
            "avg_throughput": sum(throughputs) / len(throughputs),
            "min_throughput": min(throughputs),
            "max_throughput": max(throughputs),
            "total_requests": sum(record["requests_count"] for record in recent_throughput),
            "time_window_minutes": minutes
        }
    
    def get_quality_stats(self, metric_name: str = None, minutes: int = 60) -> Dict[str, Any]:
        """Get quality metrics statistics"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        recent_metrics = [
            record for record in self.quality_metrics
            if datetime.fromisoformat(record["timestamp"]) > cutoff_time
        ]
        
        if metric_name:
            recent_metrics = [
                record for record in recent_metrics
                if record["metric_name"] == metric_name
            ]
        
        if not recent_metrics:
            return {"error": "No quality metrics available"}
        
        # Group by metric name
        metrics_by_name = {}
        for record in recent_metrics:
            name = record["metric_name"]
            if name not in metrics_by_name:
                metrics_by_name[name] = []
            metrics_by_name[name].append(record["value"])
        
        # Calculate stats for each metric
        stats = {}
        for name, values in metrics_by_name.items():
            stats[name] = {
                "count": len(values),
                "avg": sum(values) / len(values),
                "min": min(values),
                "max": max(values)
            }
        
        return {
            "metrics": stats,
            "time_window_minutes": minutes
        }
    
    def get_system_stats(self, minutes: int = 60) -> Dict[str, Any]:
        """Get system performance statistics"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        recent_metrics = [
            record for record in self.system_metrics
            if datetime.fromisoformat(record["timestamp"]) > cutoff_time
        ]
        
        if not recent_metrics:
            return {"error": "No system metrics available"}
        
        # Calculate averages
        cpu_values = [record["cpu_percent"] for record in recent_metrics]
        memory_values = [record["memory_percent"] for record in recent_metrics]
        
        stats = {
            "cpu": {
                "avg": sum(cpu_values) / len(cpu_values),
                "min": min(cpu_values),
                "max": max(cpu_values)
            },
            "memory": {
                "avg": sum(memory_values) / len(memory_values),
                "min": min(memory_values),
                "max": max(memory_values)
            }
        }
        
        # GPU stats if available
        gpu_records = [record for record in recent_metrics if "gpu_metrics" in record]
        if gpu_records:
            gpu_stats = {}
            for record in gpu_records:
                for gpu in record["gpu_metrics"]:
                    device_id = gpu["device"]
                    if device_id not in gpu_stats:
                        gpu_stats[device_id] = []
                    gpu_stats[device_id].append(gpu["utilization_percent"])
            
            for device_id, utilizations in gpu_stats.items():
                stats[f"gpu_{device_id}"] = {
                    "avg": sum(utilizations) / len(utilizations),
                    "min": min(utilizations),
                    "max": max(utilizations)
                }
        
        return stats
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile"""
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def _trigger_alert(self, alert_type: str, data: Dict[str, Any]):
        """Trigger performance alert"""
        alert = {
            "timestamp": datetime.now().isoformat(),
            "type": alert_type,
            "data": data,
            "severity": self._get_alert_severity(alert_type)
        }
        
        self.alerts.append(alert)
        
        # Keep only recent alerts
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
        
        # Call alert callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                print(f"Alert callback error: {e}")
    
    def _get_alert_severity(self, alert_type: str) -> str:
        """Get alert severity level"""
        severity_map = {
            "high_latency": "warning",
            "low_throughput": "warning", 
            "high_memory_usage": "critical",
            "high_gpu_memory": "warning"
        }
        
        return severity_map.get(alert_type, "info")
    
    def add_alert_callback(self, callback):
        """Add callback for alerts"""
        self.alert_callbacks.append(callback)
    
    def get_recent_alerts(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """Get recent alerts"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        return [
            alert for alert in self.alerts
            if datetime.fromisoformat(alert["timestamp"]) > cutoff_time
        ]
    
    def export_metrics(self, filepath: str):
        """Export metrics to file"""
        metrics_data = {
            "latency_history": list(self.latency_history),
            "throughput_history": list(self.throughput_history),
            "quality_metrics": list(self.quality_metrics),
            "system_metrics": list(self.system_metrics),
            "alerts": self.alerts,
            "export_timestamp": datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            json.dump(metrics_data, f, indent=2)
        
        print(f"Metrics exported to {filepath}")