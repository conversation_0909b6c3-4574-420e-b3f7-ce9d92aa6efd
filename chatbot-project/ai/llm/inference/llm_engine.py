from typing import Dict, List, Any, Optional
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import asyncio

class LLMEngine:
    def __init__(self, model_name: str = "microsoft/DialoGPT-medium"):
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.max_length = 512
        
    async def initialize(self):
        """Initialize LLM model"""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForCausalLM.from_pretrained(self.model_name)
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            self.model.to(self.device)
            self.model.eval()
            
            return True
        except Exception as e:
            print(f"Failed to initialize LLM: {e}")
            return False
    
    async def generate_response(self, prompt: str, context: Dict = None, max_tokens: int = 150) -> str:
        """Generate response using LLM"""
        if not self.model or not self.tokenizer:
            return "LLM not initialized"
        
        try:
            # Prepare input
            input_text = self.prepare_prompt(prompt, context)
            
            # Tokenize
            inputs = self.tokenizer.encode(input_text, return_tensors="pt", max_length=self.max_length, truncation=True)
            inputs = inputs.to(self.device)
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + max_tokens,
                    num_return_sequences=1,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract generated part
            generated_text = response[len(input_text):].strip()
            
            return generated_text if generated_text else "I need more information to provide a helpful response."
            
        except Exception as e:
            return f"Error generating response: {str(e)}"
    
    def prepare_prompt(self, query: str, context: Dict = None) -> str:
        """Prepare prompt with context"""
        if not context:
            return f"User: {query}\nAssistant:"
        
        # Add context information
        context_parts = []
        
        if "knowledge_results" in context:
            context_parts.append("Relevant information:")
            for result in context["knowledge_results"][:2]:
                context_parts.append(f"- {result.get('text', '')[:100]}...")
        
        if "user_context" in context:
            user_ctx = context["user_context"]
            if "organization" in user_ctx:
                context_parts.append(f"Organization: {user_ctx['organization']}")
        
        context_str = "\n".join(context_parts)
        
        return f"{context_str}\n\nUser: {query}\nAssistant:"
    
    async def generate_reasoning_explanation(self, reasoning_steps: List[str], conclusion: str) -> str:
        """Generate explanation for reasoning process"""
        steps_text = "\n".join([f"{i+1}. {step}" for i, step in enumerate(reasoning_steps)])
        
        prompt = f"""
        Reasoning Process:
        {steps_text}
        
        Conclusion: {conclusion}
        
        Please provide a clear explanation of this reasoning:
        """
        
        return await self.generate_response(prompt, max_tokens=200)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            "model_name": self.model_name,
            "device": self.device,
            "max_length": self.max_length,
            "initialized": self.model is not None
        }