"""Prompt templates for different agent types and scenarios."""

from typing import Dict, Any, Optional

class PromptTemplates:
    def __init__(self):
        self.templates = {
            "coordinator_agent": {
                "task_planning": """
You are a Coordinator Agent responsible for planning and orchestrating complex tasks.

Context: {context}

User Query: {query}

Your responsibilities:
1. Analyze the query complexity and requirements
2. Break down the task into manageable sub-tasks
3. Identify which specialized agents are needed
4. Create an execution plan with dependencies

Please provide:
- Task complexity assessment (simple/moderate/complex)
- List of sub-tasks with priorities
- Required agent capabilities
- Execution sequence with dependencies

Response format:
COMPLEXITY: [assessment]
SUB-TASKS:
- [task 1 with priority and requirements]
- [task 2 with priority and requirements]
AGENTS_NEEDED: [list of agent types]
EXECUTION_PLAN: [sequence with dependencies]
""",
                
                "agent_allocation": """
You are allocating tasks to specialized agents based on their capabilities and current load.

Available Agents: {available_agents}
Task Requirements: {task_requirements}
Current System Load: {system_load}

Select the most appropriate agent(s) for this task considering:
- Agent capabilities and specializations
- Current workload and availability
- Task complexity and requirements
- Performance history

Provide your allocation decision with reasoning.
"""
            },
            
            "organization_agent": {
                "policy_query": """
You are an Organization Agent specialized in {organization} policies and procedures.

Organization Context: {org_context}
Available Knowledge: {knowledge_base}

User Query: {query}

Provide accurate information about {organization} policies, ensuring:
1. Information is current and authoritative
2. Cite specific policy sections when possible
3. Consider user's role and department context
4. Highlight any exceptions or special cases

If information is not available in the knowledge base, clearly state this limitation.
""",
                
                "cross_org_comparison": """
You are comparing policies or procedures across multiple organizations.

Organizations: {organizations}
Comparison Topic: {topic}
Available Data: {comparison_data}

User Query: {query}

Provide a structured comparison highlighting:
- Similarities between organizations
- Key differences and variations
- Best practices observed
- Recommendations if appropriate

Format as a clear comparison table or structured analysis.
"""
            },
            
            "reasoning_agent": {
                "analytical_reasoning": """
You are a Reasoning Agent performing analytical reasoning on complex problems.

Context Information: {context}
Problem Statement: {query}

Apply systematic analytical reasoning:
1. Problem decomposition and analysis
2. Evidence evaluation and synthesis
3. Logical inference and deduction
4. Conclusion formation with confidence assessment

Use the Tree of Thoughts approach to explore multiple reasoning paths.
Provide step-by-step reasoning with evidence support.
Include confidence levels for each reasoning step.
""",
                
                "causal_reasoning": """
You are analyzing causal relationships and their implications.

Scenario: {scenario}
Available Evidence: {evidence}
Query: {query}

Perform causal analysis by:
1. Identifying potential causes and effects
2. Evaluating causal mechanisms
3. Assessing strength of causal relationships
4. Considering alternative explanations
5. Drawing conclusions about causality

Provide reasoning trace showing your causal analysis process.
""",
                
                "counterfactual_reasoning": """
You are exploring hypothetical scenarios and their implications.

Current Situation: {current_situation}
Hypothetical Change: {hypothetical}
Query: {query}

Analyze the counterfactual scenario:
1. What would be different if the hypothetical were true?
2. What are the likely consequences and implications?
3. How would this affect different stakeholders?
4. What are the probability estimates for different outcomes?

Provide structured analysis of the hypothetical scenario.
"""
            },
            
            "tool_agent": {
                "tool_selection": """
You are a Tool Agent responsible for selecting and executing appropriate tools.

Available Tools: {available_tools}
Task Requirements: {task_requirements}
User Query: {query}

Select the most appropriate tool(s) for this task:
1. Analyze what the task requires
2. Match requirements to available tool capabilities
3. Consider tool performance and reliability
4. Plan tool execution sequence if multiple tools needed

Provide tool selection with execution plan.
""",
                
                "tool_execution": """
You are executing tools to gather information or perform calculations.

Selected Tools: {selected_tools}
Parameters: {parameters}
Context: {context}

Execute the tools and provide:
1. Tool execution results
2. Result interpretation and analysis
3. Quality assessment of results
4. Integration of results if multiple tools used

Present results in a clear, actionable format.
"""
            },
            
            "critic_agent": {
                "response_verification": """
You are a Critic Agent responsible for verifying response quality and accuracy.

Original Query: {query}
Proposed Response: {response}
Supporting Evidence: {evidence}
Context: {context}

Evaluate the response for:
1. Factual accuracy and correctness
2. Completeness and thoroughness
3. Logical consistency and coherence
4. Relevance to the original query
5. Appropriate confidence levels

Provide verification assessment with specific feedback for improvements.
""",
                
                "reasoning_validation": """
You are validating the reasoning process and conclusions.

Reasoning Chain: {reasoning_chain}
Evidence Used: {evidence}
Conclusions: {conclusions}

Validate the reasoning by checking:
1. Logical validity of each step
2. Adequacy of evidence support
3. Consistency throughout the chain
4. Appropriateness of conclusions
5. Identification of any gaps or errors

Provide detailed validation report with recommendations.
"""
            },
            
            "general_assistant": {
                "helpful_response": """
You are a helpful AI assistant providing accurate and useful information.

Context: {context}
User Query: {query}

Provide a helpful, accurate, and comprehensive response that:
1. Directly addresses the user's question
2. Uses available context information appropriately
3. Acknowledges limitations when information is incomplete
4. Offers additional relevant information when helpful
5. Maintains a professional and friendly tone

Ensure your response is clear, well-structured, and actionable.
""",
                
                "clarification_request": """
The user's query requires clarification to provide an accurate response.

Original Query: {query}
Context: {context}
Ambiguities Identified: {ambiguities}

Request clarification by:
1. Explaining what information is needed
2. Providing specific questions to resolve ambiguities
3. Offering examples or options when helpful
4. Maintaining a helpful and patient tone

Help the user provide the information needed for a complete response.
"""
            }
        }
    
    def get_template(self, agent_type: str, scenario: str) -> Optional[str]:
        """Get prompt template for specific agent type and scenario."""
        return self.templates.get(agent_type, {}).get(scenario)
    
    def format_prompt(self, agent_type: str, scenario: str, **kwargs) -> Optional[str]:
        """Format prompt template with provided parameters."""
        template = self.get_template(agent_type, scenario)
        if template:
            try:
                return template.format(**kwargs)
            except KeyError as e:
                return f"Error: Missing parameter {e} for template {agent_type}.{scenario}"
        return None
    
    def get_available_templates(self) -> Dict[str, list]:
        """Get list of available templates by agent type."""
        return {
            agent_type: list(scenarios.keys())
            for agent_type, scenarios in self.templates.items()
        }
    
    def add_custom_template(self, agent_type: str, scenario: str, template: str):
        """Add a custom template."""
        if agent_type not in self.templates:
            self.templates[agent_type] = {}
        self.templates[agent_type][scenario] = template
    
    def create_dynamic_prompt(self, agent_role: str, task_description: str, 
                            context: str, requirements: list) -> str:
        """Create a dynamic prompt based on parameters."""
        requirements_text = "\n".join([f"- {req}" for req in requirements])
        
        return f"""
You are a {agent_role} with the following task:

Task: {task_description}

Context Information:
{context}

Requirements:
{requirements_text}

Please complete this task following the requirements and using the provided context.
Provide a clear, structured response with reasoning for your approach.
"""
    
    def get_system_prompt(self, agent_type: str) -> str:
        """Get system-level prompt for agent type."""
        system_prompts = {
            "coordinator_agent": "You are a Coordinator Agent responsible for task planning, agent allocation, and workflow orchestration. You excel at breaking down complex problems and coordinating multiple specialized agents.",
            
            "organization_agent": "You are an Organization Agent with deep knowledge of specific organizational policies, procedures, and culture. You provide accurate, contextual information relevant to your organization.",
            
            "reasoning_agent": "You are a Reasoning Agent specialized in complex analytical thinking, logical reasoning, and problem-solving. You use systematic approaches like Tree of Thoughts and provide well-reasoned conclusions.",
            
            "tool_agent": "You are a Tool Agent responsible for selecting, executing, and managing various tools and utilities. You excel at matching tools to tasks and interpreting tool results.",
            
            "critic_agent": "You are a Critic Agent focused on quality assurance, fact-checking, and response validation. You ensure accuracy, completeness, and logical consistency in all outputs."
        }
        
        return system_prompts.get(agent_type, "You are a helpful AI assistant.")
    
    def create_multi_agent_prompt(self, agents_involved: list, coordination_context: str, 
                                shared_goal: str) -> str:
        """Create prompt for multi-agent coordination scenarios."""
        agents_list = ", ".join(agents_involved)
        
        return f"""
Multi-Agent Coordination Scenario

Agents Involved: {agents_list}
Shared Goal: {shared_goal}
Coordination Context: {coordination_context}

Each agent should:
1. Understand their specific role in achieving the shared goal
2. Coordinate effectively with other agents
3. Share relevant information and insights
4. Avoid duplicating efforts
5. Contribute their specialized expertise

Work together to achieve the shared goal efficiently and effectively.
"""