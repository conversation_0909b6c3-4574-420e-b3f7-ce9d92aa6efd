"""Dynamic Tool Integration Framework for Advanced Agentic RAG."""

import asyncio
from typing import Dict, List, Any, Optional, Callable, Type
from dataclasses import dataclass
from enum import Enum
from abc import ABC, abstractmethod
import inspect
import json
import uuid
from datetime import datetime

class ToolType(Enum):
    SEARCH = "search"
    COMPUTATION = "computation"
    DATA_PROCESSING = "data_processing"
    COMMUNICATION = "communication"
    ANALYSIS = "analysis"
    CUSTOM = "custom"

class ToolStatus(Enum):
    AVAILABLE = "available"
    BUSY = "busy"
    ERROR = "error"
    DISABLED = "disabled"

@dataclass
class ToolMetadata:
    name: str
    description: str
    tool_type: ToolType
    parameters: Dict[str, Any]
    return_type: str
    created_by: str
    created_at: datetime
    usage_count: int = 0
    success_rate: float = 1.0
    average_execution_time: float = 0.0

class BaseTool(ABC):
    """Base class for all tools"""
    
    def __init__(self, name: str, description: str, tool_type: ToolType):
        self.name = name
        self.description = description
        self.tool_type = tool_type
        self.status = ToolStatus.AVAILABLE
        self.metadata = None
    
    @abstractmethod
    async def execute(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the tool with given parameters"""
        pass
    
    @abstractmethod
    def get_parameters_schema(self) -> Dict[str, Any]:
        """Get the parameters schema for this tool"""
        pass
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """Validate parameters against schema"""
        schema = self.get_parameters_schema()
        required_params = schema.get("required", [])
        
        # Check required parameters
        for param in required_params:
            if param not in parameters:
                return False
        
        return True

class SearchTool(BaseTool):
    """Tool for searching knowledge bases"""
    
    def __init__(self, search_engine):
        super().__init__("search_tool", "Search knowledge bases", ToolType.SEARCH)
        self.search_engine = search_engine
    
    async def execute(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        query = parameters.get("query", "")
        limit = parameters.get("limit", 10)
        filters = parameters.get("filters", {})
        
        try:
            results = await self.search_engine.search(query, limit, filters)
            return {
                "success": True,
                "results": results,
                "query": query,
                "count": len(results)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }
    
    def get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "required": ["query"],
            "optional": ["limit", "filters"],
            "properties": {
                "query": {"type": "string", "description": "Search query"},
                "limit": {"type": "integer", "description": "Maximum results"},
                "filters": {"type": "object", "description": "Search filters"}
            }
        }

class ComputationTool(BaseTool):
    """Tool for mathematical computations"""
    
    def __init__(self):
        super().__init__("computation_tool", "Perform mathematical computations", ToolType.COMPUTATION)
    
    async def execute(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        expression = parameters.get("expression", "")
        
        try:
            # Safe evaluation (in production, use a proper math parser)
            result = eval(expression, {"__builtins__": {}}, {
                "abs": abs, "round": round, "min": min, "max": max,
                "sum": sum, "len": len, "pow": pow
            })
            
            return {
                "success": True,
                "result": result,
                "expression": expression
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "expression": expression
            }
    
    def get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "required": ["expression"],
            "properties": {
                "expression": {"type": "string", "description": "Mathematical expression to evaluate"}
            }
        }

class DynamicToolFramework:
    """Framework for dynamic tool creation and management"""
    
    def __init__(self):
        self.tools = {}
        self.tool_metadata = {}
        self.tool_usage_stats = {}
        self.custom_tool_templates = {}
        
        # Register default tools
        self.register_default_tools()
    
    def register_default_tools(self):
        """Register default system tools"""
        # Computation tool
        computation_tool = ComputationTool()
        self.register_tool(computation_tool, "system")
    
    def register_tool(self, tool: BaseTool, created_by: str = "system") -> str:
        """Register a tool with the framework"""
        tool_id = f"{tool.name}_{uuid.uuid4().hex[:8]}"
        
        # Create metadata
        metadata = ToolMetadata(
            name=tool.name,
            description=tool.description,
            tool_type=tool.tool_type,
            parameters=tool.get_parameters_schema(),
            return_type="Dict[str, Any]",
            created_by=created_by,
            created_at=datetime.now()
        )
        
        self.tools[tool_id] = tool
        self.tool_metadata[tool_id] = metadata
        self.tool_usage_stats[tool_id] = {
            "executions": 0,
            "successes": 0,
            "failures": 0,
            "total_execution_time": 0.0
        }
        
        print(f"Tool registered: {tool.name} (ID: {tool_id})")
        return tool_id
    
    async def create_dynamic_tool(self, tool_spec: Dict[str, Any], created_by: str) -> str:
        """Create a tool dynamically from specification"""
        tool_name = tool_spec.get("name", f"dynamic_tool_{uuid.uuid4().hex[:8]}")
        tool_description = tool_spec.get("description", "Dynamically created tool")
        tool_type = ToolType(tool_spec.get("type", "custom"))
        
        # Create tool class dynamically
        class DynamicTool(BaseTool):
            def __init__(self, spec):
                super().__init__(tool_name, tool_description, tool_type)
                self.spec = spec
            
            async def execute(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
                # Execute based on specification
                if "code" in self.spec:
                    return await self.execute_code(parameters)
                elif "api_endpoint" in self.spec:
                    return await self.execute_api_call(parameters)
                else:
                    return {"success": False, "error": "No execution method specified"}
            
            async def execute_code(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
                """Execute code-based tool"""
                try:
                    code = self.spec["code"]
                    # Safe execution environment
                    exec_globals = {
                        "__builtins__": {},
                        "parameters": parameters,
                        "result": {}
                    }
                    
                    exec(code, exec_globals)
                    return exec_globals.get("result", {"success": True})
                except Exception as e:
                    return {"success": False, "error": str(e)}
            
            async def execute_api_call(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
                """Execute API-based tool"""
                # Implementation for API calls
                return {"success": False, "error": "API execution not implemented"}
            
            def get_parameters_schema(self) -> Dict[str, Any]:
                return self.spec.get("parameters_schema", {})
        
        # Create and register the tool
        dynamic_tool = DynamicTool(tool_spec)
        tool_id = self.register_tool(dynamic_tool, created_by)
        
        return tool_id
    
    async def execute_tool(self, tool_id: str, parameters: Dict[str, Any], 
                          agent_id: str = "unknown") -> Dict[str, Any]:
        """Execute a tool with given parameters"""
        if tool_id not in self.tools:
            return {"success": False, "error": f"Tool {tool_id} not found"}
        
        tool = self.tools[tool_id]
        
        # Check tool status
        if tool.status != ToolStatus.AVAILABLE:
            return {"success": False, "error": f"Tool {tool_id} is not available (status: {tool.status})"}
        
        # Validate parameters
        if not tool.validate_parameters(parameters):
            return {"success": False, "error": "Invalid parameters"}
        
        # Update tool status
        tool.status = ToolStatus.BUSY
        
        # Record execution start
        start_time = datetime.now()
        
        try:
            # Execute tool
            result = await tool.execute(parameters)
            
            # Update statistics
            execution_time = (datetime.now() - start_time).total_seconds()
            self.update_tool_stats(tool_id, True, execution_time)
            
            # Add execution metadata
            result["execution_metadata"] = {
                "tool_id": tool_id,
                "tool_name": tool.name,
                "agent_id": agent_id,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            # Update statistics
            execution_time = (datetime.now() - start_time).total_seconds()
            self.update_tool_stats(tool_id, False, execution_time)
            
            return {
                "success": False,
                "error": f"Tool execution failed: {str(e)}",
                "tool_id": tool_id
            }
        
        finally:
            # Reset tool status
            tool.status = ToolStatus.AVAILABLE
    
    def update_tool_stats(self, tool_id: str, success: bool, execution_time: float):
        """Update tool usage statistics"""
        if tool_id not in self.tool_usage_stats:
            return
        
        stats = self.tool_usage_stats[tool_id]
        stats["executions"] += 1
        stats["total_execution_time"] += execution_time
        
        if success:
            stats["successes"] += 1
        else:
            stats["failures"] += 1
        
        # Update metadata
        if tool_id in self.tool_metadata:
            metadata = self.tool_metadata[tool_id]
            metadata.usage_count = stats["executions"]
            metadata.success_rate = stats["successes"] / stats["executions"]
            metadata.average_execution_time = stats["total_execution_time"] / stats["executions"]
    
    def get_available_tools(self, tool_type: Optional[ToolType] = None) -> List[Dict[str, Any]]:
        """Get list of available tools"""
        available_tools = []
        
        for tool_id, tool in self.tools.items():
            if tool_type is None or tool.tool_type == tool_type:
                if tool.status == ToolStatus.AVAILABLE:
                    metadata = self.tool_metadata.get(tool_id)
                    available_tools.append({
                        "tool_id": tool_id,
                        "name": tool.name,
                        "description": tool.description,
                        "type": tool.tool_type.value,
                        "parameters_schema": tool.get_parameters_schema(),
                        "metadata": metadata.__dict__ if metadata else None
                    })
        
        return available_tools
    
    def recommend_tools(self, task_description: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Recommend tools based on task description"""
        # Simple keyword-based recommendation
        task_words = set(task_description.lower().split())
        
        tool_scores = []
        for tool_id, tool in self.tools.items():
            if tool.status != ToolStatus.AVAILABLE:
                continue
            
            # Calculate relevance score
            tool_words = set((tool.name + " " + tool.description).lower().split())
            intersection = task_words.intersection(tool_words)
            score = len(intersection) / len(task_words) if task_words else 0
            
            # Boost score based on success rate and usage
            metadata = self.tool_metadata.get(tool_id)
            if metadata:
                score *= metadata.success_rate
                score += min(metadata.usage_count / 100, 0.1)  # Small boost for popular tools
            
            if score > 0:
                tool_scores.append((tool_id, score))
        
        # Sort by score and return top recommendations
        tool_scores.sort(key=lambda x: x[1], reverse=True)
        
        recommendations = []
        for tool_id, score in tool_scores[:limit]:
            tool = self.tools[tool_id]
            metadata = self.tool_metadata.get(tool_id)
            
            recommendations.append({
                "tool_id": tool_id,
                "name": tool.name,
                "description": tool.description,
                "type": tool.tool_type.value,
                "relevance_score": score,
                "success_rate": metadata.success_rate if metadata else 1.0,
                "parameters_schema": tool.get_parameters_schema()
            })
        
        return recommendations
    
    def get_tool_statistics(self) -> Dict[str, Any]:
        """Get framework statistics"""
        total_tools = len(self.tools)
        available_tools = len([t for t in self.tools.values() if t.status == ToolStatus.AVAILABLE])
        
        tool_types = {}
        for tool in self.tools.values():
            tool_type = tool.tool_type.value
            tool_types[tool_type] = tool_types.get(tool_type, 0) + 1
        
        total_executions = sum(stats["executions"] for stats in self.tool_usage_stats.values())
        total_successes = sum(stats["successes"] for stats in self.tool_usage_stats.values())
        
        return {
            "total_tools": total_tools,
            "available_tools": available_tools,
            "tools_by_type": tool_types,
            "total_executions": total_executions,
            "overall_success_rate": total_successes / total_executions if total_executions > 0 else 0,
            "most_used_tools": self.get_most_used_tools(5)
        }
    
    def get_most_used_tools(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get most used tools"""
        tool_usage = []
        
        for tool_id, stats in self.tool_usage_stats.items():
            if stats["executions"] > 0:
                tool = self.tools[tool_id]
                tool_usage.append({
                    "tool_id": tool_id,
                    "name": tool.name,
                    "executions": stats["executions"],
                    "success_rate": stats["successes"] / stats["executions"]
                })
        
        tool_usage.sort(key=lambda x: x["executions"], reverse=True)
        return tool_usage[:limit]
