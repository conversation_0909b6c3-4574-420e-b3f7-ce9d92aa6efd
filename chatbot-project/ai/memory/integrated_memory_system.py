"""Integrated Memory System for Advanced Agentic RAG."""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import json
import uuid

class MemoryType(Enum):
    CONVERSATION = "conversation"
    EPISODIC = "episodic"
    SEMANTIC = "semantic"
    WORKING = "working"
    AGENT_SPECIFIC = "agent_specific"

@dataclass
class MemoryEntry:
    id: str
    memory_type: MemoryType
    content: Dict[str, Any]
    timestamp: datetime
    agent_id: Optional[str] = None
    organization: Optional[str] = None
    department: Optional[str] = None
    importance: float = 0.5
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

class IntegratedMemorySystem:
    def __init__(self, vector_store=None, graph_db=None):
        self.vector_store = vector_store
        self.graph_db = graph_db
        self.memory_stores = {
            MemoryType.CONVERSATION: {},
            MemoryType.EPISODIC: {},
            MemoryType.SEMANTIC: {},
            MemoryType.WORKING: {},
            MemoryType.AGENT_SPECIFIC: {}
        }
        self.memory_index = {}  # For fast lookup
        self.retention_policies = {
            MemoryType.CONVERSATION: timedelta(days=30),
            MemoryType.EPISODIC: timedelta(days=90),
            MemoryType.SEMANTIC: timedelta(days=365),
            MemoryType.WORKING: timedelta(hours=24),
            MemoryType.AGENT_SPECIFIC: timedelta(days=180)
        }
    
    async def store_memory(self, memory_type: MemoryType, content: Dict[str, Any], 
                          agent_id: Optional[str] = None, organization: Optional[str] = None,
                          department: Optional[str] = None, importance: float = 0.5,
                          tags: List[str] = None) -> str:
        """Store a memory entry"""
        memory_id = str(uuid.uuid4())
        
        memory_entry = MemoryEntry(
            id=memory_id,
            memory_type=memory_type,
            content=content,
            timestamp=datetime.now(),
            agent_id=agent_id,
            organization=organization,
            department=department,
            importance=importance,
            tags=tags or []
        )
        
        # Store in appropriate memory store
        self.memory_stores[memory_type][memory_id] = memory_entry
        
        # Update index
        self.update_memory_index(memory_entry)
        
        # Store in vector database if available
        if self.vector_store:
            await self.store_in_vector_db(memory_entry)
        
        # Store relationships in graph database if available
        if self.graph_db:
            await self.store_memory_relationships(memory_entry)
        
        return memory_id
    
    async def retrieve_memory(self, query: Dict[str, Any], memory_types: List[MemoryType] = None,
                             limit: int = 10, agent_id: Optional[str] = None) -> List[MemoryEntry]:
        """Retrieve relevant memories"""
        if memory_types is None:
            memory_types = list(MemoryType)
        
        relevant_memories = []
        
        # Search in each specified memory type
        for memory_type in memory_types:
            memories = await self.search_memory_type(memory_type, query, limit, agent_id)
            relevant_memories.extend(memories)
        
        # Sort by relevance and importance
        relevant_memories.sort(key=lambda m: (m.importance, -m.access_count), reverse=True)
        
        # Update access statistics
        for memory in relevant_memories[:limit]:
            memory.access_count += 1
            memory.last_accessed = datetime.now()
        
        return relevant_memories[:limit]
    
    async def get_relevant_context(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Get relevant context for a task"""
        context = {
            "conversation_history": [],
            "relevant_episodes": [],
            "semantic_knowledge": [],
            "agent_experiences": []
        }
        
        # Extract query information
        query_text = task.get("query", "")
        organization = task.get("organization")
        department = task.get("department")
        agent_id = task.get("agent_id")
        
        # Retrieve conversation history
        conv_memories = await self.retrieve_memory(
            {"query": query_text, "organization": organization},
            [MemoryType.CONVERSATION],
            limit=5
        )
        context["conversation_history"] = [m.content for m in conv_memories]
        
        # Retrieve relevant episodes
        episodic_memories = await self.retrieve_memory(
            {"query": query_text, "organization": organization, "department": department},
            [MemoryType.EPISODIC],
            limit=3
        )
        context["relevant_episodes"] = [m.content for m in episodic_memories]
        
        # Retrieve semantic knowledge
        semantic_memories = await self.retrieve_memory(
            {"query": query_text},
            [MemoryType.SEMANTIC],
            limit=5
        )
        context["semantic_knowledge"] = [m.content for m in semantic_memories]
        
        # Retrieve agent-specific experiences
        if agent_id:
            agent_memories = await self.retrieve_memory(
                {"query": query_text},
                [MemoryType.AGENT_SPECIFIC],
                limit=3,
                agent_id=agent_id
            )
            context["agent_experiences"] = [m.content for m in agent_memories]
        
        return context
    
    async def store_interaction(self, task: Dict[str, Any], result: Dict[str, Any]):
        """Store an interaction in memory"""
        # Store conversation memory
        await self.store_memory(
            MemoryType.CONVERSATION,
            {
                "query": task.get("query", ""),
                "response": result.get("response", ""),
                "organization": task.get("organization"),
                "department": task.get("department"),
                "timestamp": datetime.now().isoformat()
            },
            organization=task.get("organization"),
            department=task.get("department"),
            importance=0.6,
            tags=["interaction", "conversation"]
        )
        
        # Store episodic memory if significant
        if result.get("confidence", 0) > 0.8:
            await self.store_memory(
                MemoryType.EPISODIC,
                {
                    "task": task,
                    "result": result,
                    "success": result.get("success", True),
                    "reasoning_trace": result.get("reasoning_trace", [])
                },
                organization=task.get("organization"),
                department=task.get("department"),
                importance=0.8,
                tags=["episode", "successful_interaction"]
            )
        
        # Store agent-specific memory
        if "agent_id" in task:
            await self.store_memory(
                MemoryType.AGENT_SPECIFIC,
                {
                    "agent_action": task,
                    "agent_result": result,
                    "performance": result.get("confidence", 0.5)
                },
                agent_id=task["agent_id"],
                importance=0.7,
                tags=["agent_experience", task["agent_id"]]
            )
    
    async def search_memory_type(self, memory_type: MemoryType, query: Dict[str, Any],
                                limit: int, agent_id: Optional[str] = None) -> List[MemoryEntry]:
        """Search within a specific memory type"""
        memories = list(self.memory_stores[memory_type].values())
        
        # Filter by agent if specified
        if agent_id:
            memories = [m for m in memories if m.agent_id == agent_id]
        
        # Filter by organization/department if specified
        if query.get("organization"):
            memories = [m for m in memories if m.organization == query["organization"]]
        
        if query.get("department"):
            memories = [m for m in memories if m.department == query["department"]]
        
        # Simple text matching (in production, use vector similarity)
        query_text = query.get("query", "").lower()
        if query_text:
            scored_memories = []
            for memory in memories:
                score = self.calculate_memory_relevance(memory, query_text)
                if score > 0:
                    scored_memories.append((memory, score))
            
            scored_memories.sort(key=lambda x: x[1], reverse=True)
            memories = [m[0] for m in scored_memories[:limit]]
        
        return memories[:limit]
    
    def calculate_memory_relevance(self, memory: MemoryEntry, query_text: str) -> float:
        """Calculate relevance score for a memory"""
        content_str = json.dumps(memory.content).lower()
        
        # Simple keyword matching
        query_words = set(query_text.split())
        content_words = set(content_str.split())
        
        if not query_words:
            return 0.0
        
        intersection = query_words.intersection(content_words)
        relevance = len(intersection) / len(query_words)
        
        # Boost by importance and recency
        importance_boost = memory.importance
        recency_boost = 1.0 / (1 + (datetime.now() - memory.timestamp).days)
        
        return relevance * importance_boost * recency_boost
    
    def update_memory_index(self, memory_entry: MemoryEntry):
        """Update memory index for fast lookup"""
        # Index by tags
        for tag in memory_entry.tags:
            if tag not in self.memory_index:
                self.memory_index[tag] = []
            self.memory_index[tag].append(memory_entry.id)
        
        # Index by organization
        if memory_entry.organization:
            org_key = f"org:{memory_entry.organization}"
            if org_key not in self.memory_index:
                self.memory_index[org_key] = []
            self.memory_index[org_key].append(memory_entry.id)
        
        # Index by agent
        if memory_entry.agent_id:
            agent_key = f"agent:{memory_entry.agent_id}"
            if agent_key not in self.memory_index:
                self.memory_index[agent_key] = []
            self.memory_index[agent_key].append(memory_entry.id)
    
    async def store_in_vector_db(self, memory_entry: MemoryEntry):
        """Store memory in vector database"""
        if not self.vector_store:
            return
        
        # Convert memory content to text for embedding
        text_content = json.dumps(memory_entry.content)
        
        # Store with metadata
        metadata = {
            "memory_id": memory_entry.id,
            "memory_type": memory_entry.memory_type.value,
            "timestamp": memory_entry.timestamp.isoformat(),
            "agent_id": memory_entry.agent_id,
            "organization": memory_entry.organization,
            "department": memory_entry.department,
            "importance": memory_entry.importance,
            "tags": memory_entry.tags
        }
        
        await self.vector_store.add_document(text_content, metadata)
    
    async def store_memory_relationships(self, memory_entry: MemoryEntry):
        """Store memory relationships in graph database"""
        if not self.graph_db:
            return
        
        # Create memory node
        await self.graph_db.create_node(
            "Memory",
            {
                "id": memory_entry.id,
                "type": memory_entry.memory_type.value,
                "timestamp": memory_entry.timestamp.isoformat(),
                "importance": memory_entry.importance
            }
        )
        
        # Create relationships
        if memory_entry.agent_id:
            await self.graph_db.create_relationship(
                memory_entry.id, "CREATED_BY", memory_entry.agent_id
            )
        
        if memory_entry.organization:
            await self.graph_db.create_relationship(
                memory_entry.id, "BELONGS_TO", memory_entry.organization
            )
        
        if memory_entry.department:
            await self.graph_db.create_relationship(
                memory_entry.id, "RELATES_TO", memory_entry.department
            )
    
    async def cleanup_expired_memories(self):
        """Clean up expired memories based on retention policies"""
        current_time = datetime.now()
        
        for memory_type, retention_period in self.retention_policies.items():
            expired_memories = []
            
            for memory_id, memory in self.memory_stores[memory_type].items():
                if current_time - memory.timestamp > retention_period:
                    # Keep important memories longer
                    if memory.importance < 0.8:
                        expired_memories.append(memory_id)
            
            # Remove expired memories
            for memory_id in expired_memories:
                del self.memory_stores[memory_type][memory_id]
                
                # Clean up index
                self.cleanup_memory_index(memory_id)
        
        print(f"Cleaned up expired memories: {sum(len(expired) for expired in expired_memories)}")
    
    def cleanup_memory_index(self, memory_id: str):
        """Clean up memory index entries"""
        for key, memory_list in self.memory_index.items():
            if memory_id in memory_list:
                memory_list.remove(memory_id)
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get memory system statistics"""
        stats = {
            "total_memories": sum(len(store) for store in self.memory_stores.values()),
            "memory_by_type": {
                memory_type.value: len(store) 
                for memory_type, store in self.memory_stores.items()
            },
            "index_size": len(self.memory_index),
            "most_accessed": []
        }
        
        # Find most accessed memories
        all_memories = []
        for store in self.memory_stores.values():
            all_memories.extend(store.values())
        
        all_memories.sort(key=lambda m: m.access_count, reverse=True)
        stats["most_accessed"] = [
            {"id": m.id, "access_count": m.access_count, "type": m.memory_type.value}
            for m in all_memories[:10]
        ]
        
        return stats
