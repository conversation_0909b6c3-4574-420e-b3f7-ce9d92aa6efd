from typing import Dict, List, Any, Tuple
import asyncio
from dataclasses import dataclass

@dataclass
class CounterfactualScenario:
    scenario_id: str
    original_condition: str
    alternative_condition: str
    predicted_outcome: str
    likelihood: float
    impact_level: str
    reasoning: str

class CounterfactualEngine:
    def __init__(self):
        self.scenario_templates = {}
        
    async def generate_counterfactuals(self, original_scenario: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate counterfactual scenarios"""
        conditions = self.extract_conditions(original_scenario)
        
        alternatives = []
        for condition in conditions:
            alt_scenarios = await self.generate_alternatives_for_condition(condition, context)
            alternatives.extend(alt_scenarios)
        
        analyzed_scenarios = []
        for alt in alternatives:
            analysis = await self.analyze_alternative(original_scenario, alt, context)
            analyzed_scenarios.append(analysis)
        
        implications = await self.assess_implications(original_scenario, analyzed_scenarios)
        
        return {
            "original_scenario": original_scenario,
            "conditions_identified": len(conditions),
            "alternatives_generated": len(alternatives),
            "analyzed_scenarios": analyzed_scenarios,
            "implications": implications,
            "counterfactual_insights": self.generate_insights(analyzed_scenarios)
        }
    
    def extract_conditions(self, scenario: str) -> List[str]:
        """Extract key conditions from scenario"""
        conditions = []
        scenario_lower = scenario.lower()
        
        if "policy" in scenario_lower:
            conditions.append("policy_exists")
        if any(time_word in scenario_lower for time_word in ["days", "weeks", "months"]):
            conditions.append("time_constraint")
        if any(person_word in scenario_lower for person_word in ["employee", "manager"]):
            conditions.append("person_involved")
        
        return conditions if conditions else ["general_condition"]
    
    async def generate_alternatives_for_condition(self, condition: str, context: Dict[str, Any]) -> List[CounterfactualScenario]:
        """Generate alternative scenarios for a specific condition"""
        alternatives = []
        
        if condition == "policy_exists":
            alternatives.append(CounterfactualScenario(
                scenario_id="no_policy",
                original_condition="Policy exists",
                alternative_condition="No policy exists",
                predicted_outcome="Decisions made case-by-case",
                likelihood=0.3,
                impact_level="high",
                reasoning="Without policy, inconsistent decisions likely"
            ))
        
        elif condition == "time_constraint":
            alternatives.append(CounterfactualScenario(
                scenario_id="extended_time",
                original_condition="Current time limit",
                alternative_condition="Extended time limit",
                predicted_outcome="More flexibility, higher satisfaction",
                likelihood=0.8,
                impact_level="medium",
                reasoning="Extended time typically improves outcomes"
            ))
        
        return alternatives
    
    async def analyze_alternative(self, original: str, alternative: CounterfactualScenario, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a specific alternative scenario"""
        return {
            "scenario": alternative,
            "feasibility": await self.assess_feasibility(alternative, context),
            "consequences": await self.predict_consequences(alternative, context),
            "overall_assessment": {
                "desirability": 0.6,
                "probability": alternative.likelihood,
                "recommendation": "Worth exploring further"
            }
        }
    
    async def assess_feasibility(self, scenario: CounterfactualScenario, context: Dict[str, Any]) -> Dict[str, Any]:
        """Assess feasibility of alternative scenario"""
        return {
            "technical_feasibility": 0.7,
            "organizational_feasibility": 0.6,
            "barriers": ["Requires policy approval"] if "policy" in scenario.alternative_condition.lower() else []
        }
    
    async def predict_consequences(self, scenario: CounterfactualScenario, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Predict consequences of alternative scenario"""
        return [{
            "type": "direct",
            "description": scenario.predicted_outcome,
            "probability": scenario.likelihood,
            "timeframe": "immediate"
        }]
    
    async def assess_implications(self, original: str, scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Assess overall implications of counterfactual analysis"""
        return {
            "key_insights": [f"Analyzed {len(scenarios)} alternative scenarios"],
            "risk_factors": [],
            "opportunities": [s["scenario"].alternative_condition for s in scenarios if s["overall_assessment"]["desirability"] > 0.6],
            "recommendations": ["Consider implementing feasible alternatives"]
        }
    
    def generate_insights(self, scenarios: List[Dict[str, Any]]) -> List[str]:
        """Generate insights from counterfactual analysis"""
        if not scenarios:
            return ["No alternative scenarios analyzed"]
        
        insights = []
        policy_scenarios = [s for s in scenarios if "policy" in s["scenario"].alternative_condition.lower()]
        if policy_scenarios:
            insights.append("Policy changes have significant impact on outcomes")
        
        return insights