import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from ai.reasoning.self_reflection.verification.reasoning_verifier import ReasoningVerifier
from ai.reasoning.self_reflection.error_detection.error_detector import ErrorDetector
from ai.reasoning.self_reflection.confidence.confidence_estimator import ConfidenceEstimator
from ai.reasoning.self_reflection.improvement.iterative_improver import IterativeImprover
from typing import Dict, List, Any
import asyncio

class SelfReflectionEngine:
    def __init__(self):
        self.verifier = ReasoningVerifier()
        self.error_detector = ErrorDetector()
        self.confidence_estimator = ConfidenceEstimator()
        self.iterative_improver = IterativeImprover()
        self.reflection_history = []
        
    async def complete_self_reflection(self, reasoning_result: Dict[str, Any], 
                                     context: Dict[str, Any]) -> Dict[str, Any]:
        """Complete self-reflection process with all components"""
        reflection_result = {
            "original_reasoning": reasoning_result,
            "verification_results": {},
            "error_analysis": {},
            "confidence_analysis": {},
            "improvement_results": {},
            "final_assessment": {},
            "reflection_summary": {}
        }
        
        try:
            # Stage 1: Reasoning Verification
            verification_results = await self.verifier.verify_reasoning_chain(reasoning_result)
            reflection_result["verification_results"] = verification_results
            
            # Stage 2: Error Detection and Correction
            error_analysis = await self.error_detector.detect_reasoning_errors(reasoning_result)
            reflection_result["error_analysis"] = error_analysis
            
            # Stage 3: Confidence Estimation
            confidence_analysis = await self.confidence_estimator.estimate_confidence(reasoning_result)
            reflection_result["confidence_analysis"] = confidence_analysis.__dict__
            
            # Stage 4: Iterative Improvement
            improvement_results = await self.iterative_improver.iterative_improvement(reasoning_result, context)
            reflection_result["improvement_results"] = improvement_results
            
            # Stage 5: Final Assessment
            final_assessment = await self.generate_final_assessment(reflection_result)
            reflection_result["final_assessment"] = final_assessment
            
            # Stage 6: Reflection Summary
            reflection_summary = self.generate_reflection_summary(reflection_result)
            reflection_result["reflection_summary"] = reflection_summary
            
            # Store in history
            self.reflection_history.append(reflection_result)
            
            return reflection_result
            
        except Exception as e:
            return {
                "error": str(e),
                "original_reasoning": reasoning_result,
                "reflection_failed": True
            }
    
    async def step_by_step_verification(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Detailed step-by-step verification"""
        verification_result = await self.verifier.verify_reasoning_chain(reasoning_result)
        
        # Additional assumption validation
        assumption_validation = await self.verifier.validate_assumptions(reasoning_result)
        verification_result["assumption_validation"] = assumption_validation
        
        # Conclusion checking
        conclusion_check = await self.verifier.check_conclusions(reasoning_result)
        verification_result["conclusion_check"] = conclusion_check
        
        return verification_result
    
    async def comprehensive_error_detection(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive error detection and correction"""
        error_report = await self.error_detector.detect_reasoning_errors(reasoning_result)
        
        # Generate corrections for detected errors
        if error_report["total_errors"] > 0:
            all_errors = (error_report["logical_errors"] + 
                         error_report["factual_errors"] + 
                         error_report["consistency_errors"])
            
            corrections = await self.error_detector.generate_corrections(all_errors)
            error_report["corrections"] = corrections
        
        return error_report
    
    async def advanced_confidence_estimation(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Advanced confidence estimation with calibration"""
        confidence_metrics = await self.confidence_estimator.estimate_confidence(reasoning_result)
        
        # Get calibration metrics
        calibration_metrics = self.confidence_estimator.get_calibration_metrics()
        
        return {
            "confidence_metrics": confidence_metrics.__dict__,
            "calibration_metrics": calibration_metrics,
            "uncertainty_breakdown": {
                "data_uncertainty": confidence_metrics.uncertainty_level * 0.3,
                "model_uncertainty": confidence_metrics.uncertainty_level * 0.2,
                "reasoning_uncertainty": confidence_metrics.uncertainty_level * 0.3,
                "context_uncertainty": confidence_metrics.uncertainty_level * 0.2
            }
        }
    
    async def iterative_reasoning_improvement(self, reasoning_result: Dict[str, Any], 
                                           context: Dict[str, Any]) -> Dict[str, Any]:
        """Iterative improvement with multiple refinement loops"""
        improvement_result = await self.iterative_improver.iterative_improvement(reasoning_result, context)
        
        # Progressive enhancement for specific targets
        enhancement_targets = ["logical_structure", "evidence_quality", "conclusion_strength"]
        progressive_result = await self.iterative_improver.progressive_enhancement(
            improvement_result["final_reasoning"], enhancement_targets
        )
        
        improvement_result["progressive_enhancement"] = progressive_result
        
        return improvement_result
    
    async def generate_final_assessment(self, reflection_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final assessment of reasoning quality"""
        assessment = {
            "overall_quality": "unknown",
            "quality_score": 0.0,
            "strengths": [],
            "weaknesses": [],
            "recommendations": [],
            "confidence_level": "unknown",
            "reliability_assessment": "unknown"
        }
        
        # Analyze verification results
        verification = reflection_result.get("verification_results", {})
        if verification:
            overall_validity = verification.get("overall_validity", 0.0)
            assessment["quality_score"] += overall_validity * 0.3
            
            if overall_validity > 0.8:
                assessment["strengths"].append("High reasoning validity")
            elif overall_validity < 0.5:
                assessment["weaknesses"].append("Low reasoning validity")
        
        # Analyze error results
        error_analysis = reflection_result.get("error_analysis", {})
        if error_analysis:
            total_errors = error_analysis.get("total_errors", 0)
            error_severity = error_analysis.get("error_severity", "none")
            
            if total_errors == 0:
                assessment["strengths"].append("No logical or factual errors detected")
                assessment["quality_score"] += 0.2
            else:
                assessment["weaknesses"].append(f"{total_errors} errors detected ({error_severity} severity)")
                if error_severity == "high":
                    assessment["quality_score"] -= 0.3
                elif error_severity == "medium":
                    assessment["quality_score"] -= 0.1
        
        # Analyze confidence
        confidence_analysis = reflection_result.get("confidence_analysis", {})
        if confidence_analysis:
            overall_confidence = confidence_analysis.get("overall_confidence", 0.5)
            uncertainty_level = confidence_analysis.get("uncertainty_level", 0.5)
            
            assessment["quality_score"] += overall_confidence * 0.3
            
            if overall_confidence > 0.8:
                assessment["confidence_level"] = "high"
                assessment["strengths"].append("High confidence in reasoning")
            elif overall_confidence < 0.5:
                assessment["confidence_level"] = "low"
                assessment["weaknesses"].append("Low confidence in reasoning")
            else:
                assessment["confidence_level"] = "medium"
            
            if uncertainty_level < 0.3:
                assessment["reliability_assessment"] = "high"
            elif uncertainty_level > 0.7:
                assessment["reliability_assessment"] = "low"
            else:
                assessment["reliability_assessment"] = "medium"
        
        # Analyze improvement results
        improvement = reflection_result.get("improvement_results", {})
        if improvement:
            improvement_achieved = improvement.get("improvement_achieved", 0.0)
            if improvement_achieved > 0.2:
                assessment["strengths"].append("Significant improvement through iteration")
                assessment["quality_score"] += 0.2
        
        # Determine overall quality
        quality_score = max(0.0, min(assessment["quality_score"], 1.0))
        assessment["quality_score"] = quality_score
        
        if quality_score > 0.8:
            assessment["overall_quality"] = "excellent"
        elif quality_score > 0.6:
            assessment["overall_quality"] = "good"
        elif quality_score > 0.4:
            assessment["overall_quality"] = "fair"
        else:
            assessment["overall_quality"] = "poor"
        
        # Generate recommendations
        assessment["recommendations"] = self.generate_recommendations(reflection_result)
        
        return assessment
    
    def generate_recommendations(self, reflection_result: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on reflection results"""
        recommendations = []
        
        # Based on verification results
        verification = reflection_result.get("verification_results", {})
        if verification.get("overall_validity", 1.0) < 0.6:
            recommendations.append("Strengthen logical reasoning chain")
        
        # Based on error analysis
        error_analysis = reflection_result.get("error_analysis", {})
        if error_analysis.get("total_errors", 0) > 0:
            recommendations.append("Address detected logical and factual errors")
        
        # Based on confidence analysis
        confidence_analysis = reflection_result.get("confidence_analysis", {})
        if confidence_analysis.get("overall_confidence", 1.0) < 0.6:
            recommendations.append("Increase confidence through additional evidence")
        
        if confidence_analysis.get("uncertainty_level", 0.0) > 0.6:
            recommendations.append("Reduce uncertainty through better data quality")
        
        # Based on improvement results
        improvement = reflection_result.get("improvement_results", {})
        if improvement.get("improvement_achieved", 0.0) < 0.1:
            recommendations.append("Consider alternative reasoning approaches")
        
        return recommendations if recommendations else ["Reasoning quality is satisfactory"]
    
    def generate_reflection_summary(self, reflection_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive reflection summary"""
        summary = {
            "reflection_overview": "",
            "key_findings": [],
            "quality_metrics": {},
            "improvement_summary": "",
            "final_recommendation": ""
        }
        
        # Overview
        final_assessment = reflection_result.get("final_assessment", {})
        overall_quality = final_assessment.get("overall_quality", "unknown")
        quality_score = final_assessment.get("quality_score", 0.0)
        
        summary["reflection_overview"] = f"Reasoning quality assessed as {overall_quality} with score {quality_score:.2f}"
        
        # Key findings
        verification = reflection_result.get("verification_results", {})
        error_analysis = reflection_result.get("error_analysis", {})
        confidence_analysis = reflection_result.get("confidence_analysis", {})
        
        if verification:
            summary["key_findings"].append(f"Verification: {verification.get('verified_steps', 0)}/{verification.get('total_steps', 0)} steps verified")
        
        if error_analysis:
            total_errors = error_analysis.get("total_errors", 0)
            summary["key_findings"].append(f"Error Analysis: {total_errors} errors detected")
        
        if confidence_analysis:
            confidence = confidence_analysis.get("overall_confidence", 0.0)
            summary["key_findings"].append(f"Confidence: {confidence:.2f}")
        
        # Quality metrics
        summary["quality_metrics"] = {
            "overall_score": quality_score,
            "verification_rate": verification.get("overall_validity", 0.0) if verification else 0.0,
            "error_count": error_analysis.get("total_errors", 0) if error_analysis else 0,
            "confidence_level": confidence_analysis.get("overall_confidence", 0.0) if confidence_analysis else 0.0
        }
        
        # Improvement summary
        improvement = reflection_result.get("improvement_results", {})
        if improvement:
            total_iterations = improvement.get("total_iterations", 0)
            improvement_achieved = improvement.get("improvement_achieved", 0.0)
            summary["improvement_summary"] = f"Completed {total_iterations} improvement iterations with {improvement_achieved:.2f} total improvement"
        
        # Final recommendation
        recommendations = final_assessment.get("recommendations", [])
        if recommendations:
            summary["final_recommendation"] = recommendations[0]  # Primary recommendation
        else:
            summary["final_recommendation"] = "No specific recommendations needed"
        
        return summary
    
    def get_reflection_statistics(self) -> Dict[str, Any]:
        """Get statistics from reflection history"""
        if not self.reflection_history:
            return {"no_data": True}
        
        stats = {
            "total_reflections": len(self.reflection_history),
            "average_quality_score": 0.0,
            "common_issues": [],
            "improvement_trends": {}
        }
        
        # Calculate averages
        quality_scores = []
        for reflection in self.reflection_history:
            final_assessment = reflection.get("final_assessment", {})
            quality_score = final_assessment.get("quality_score", 0.0)
            quality_scores.append(quality_score)
        
        if quality_scores:
            stats["average_quality_score"] = sum(quality_scores) / len(quality_scores)
        
        return stats