from typing import Dict, List, Any, <PERSON>ple
from dataclasses import dataclass
import re

@dataclass
class ReasoningError:
    error_id: str
    error_type: str
    description: str
    location: str
    severity: str
    suggested_correction: str
    confidence: float

class ErrorDetector:
    def __init__(self):
        self.logical_patterns = {
            "contradiction": [r"not.*but", r"however.*opposite", r"although.*contrary"],
            "circular_reasoning": [r"because.*because", r"since.*since"],
            "false_dichotomy": [r"either.*or.*only", r"must.*or.*nothing"]
        }
        self.factual_validators = {}
        
    async def detect_reasoning_errors(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive error detection in reasoning"""
        error_report = {
            "logical_errors": await self.detect_logical_errors(reasoning_result),
            "factual_errors": await self.detect_factual_errors(reasoning_result),
            "consistency_errors": await self.detect_consistency_errors(reasoning_result),
            "total_errors": 0,
            "error_severity": "none",
            "correction_suggestions": []
        }
        
        # Count total errors
        all_errors = (error_report["logical_errors"] + 
                     error_report["factual_errors"] + 
                     error_report["consistency_errors"])
        
        error_report["total_errors"] = len(all_errors)
        error_report["error_severity"] = self.assess_overall_severity(all_errors)
        error_report["correction_suggestions"] = [error.suggested_correction for error in all_errors]
        
        return error_report
    
    async def detect_logical_errors(self, reasoning_result: Dict[str, Any]) -> List[ReasoningError]:
        """Detect logical errors in reasoning"""
        logical_errors = []
        reasoning_text = self.extract_reasoning_text(reasoning_result)
        
        # Check for contradictions
        contradictions = self.find_contradictions(reasoning_text)
        logical_errors.extend(contradictions)
        
        # Check for circular reasoning
        circular_errors = self.find_circular_reasoning(reasoning_text)
        logical_errors.extend(circular_errors)
        
        # Check for logical fallacies
        fallacy_errors = self.find_logical_fallacies(reasoning_text)
        logical_errors.extend(fallacy_errors)
        
        return logical_errors
    
    def find_contradictions(self, reasoning_text: str) -> List[ReasoningError]:
        """Find contradictory statements"""
        contradictions = []
        text_lower = reasoning_text.lower()
        
        # Check for explicit contradictions
        contradiction_pairs = [
            ("allow", "prohibit"), ("can", "cannot"), ("yes", "no"),
            ("required", "optional"), ("must", "may not"), ("always", "never")
        ]
        
        for pos, neg in contradiction_pairs:
            if pos in text_lower and neg in text_lower:
                contradictions.append(ReasoningError(
                    error_id=f"contradiction_{pos}_{neg}",
                    error_type="logical_contradiction",
                    description=f"Contradictory statements: '{pos}' and '{neg}' both present",
                    location="reasoning_text",
                    severity="high",
                    suggested_correction=f"Resolve contradiction between '{pos}' and '{neg}'",
                    confidence=0.8
                ))
        
        return contradictions
    
    def find_circular_reasoning(self, reasoning_text: str) -> List[ReasoningError]:
        """Find circular reasoning patterns"""
        circular_errors = []
        
        # Simple circular reasoning detection
        sentences = reasoning_text.split('.')
        for i, sentence in enumerate(sentences):
            sentence_lower = sentence.lower().strip()
            
            # Check if sentence repeats key phrases
            words = sentence_lower.split()
            if len(words) > 5:
                word_counts = {}
                for word in words:
                    if len(word) > 3:  # Skip short words
                        word_counts[word] = word_counts.get(word, 0) + 1
                
                # If any significant word appears multiple times
                repeated_words = [word for word, count in word_counts.items() if count > 1]
                if len(repeated_words) > 2:
                    circular_errors.append(ReasoningError(
                        error_id=f"circular_{i}",
                        error_type="circular_reasoning",
                        description=f"Potential circular reasoning in sentence {i+1}",
                        location=f"sentence_{i+1}",
                        severity="medium",
                        suggested_correction="Provide independent evidence or reasoning",
                        confidence=0.6
                    ))
        
        return circular_errors
    
    def find_logical_fallacies(self, reasoning_text: str) -> List[ReasoningError]:
        """Find common logical fallacies"""
        fallacies = []
        text_lower = reasoning_text.lower()
        
        # Ad hominem
        if any(word in text_lower for word in ["stupid", "ignorant", "foolish", "incompetent"]):
            fallacies.append(ReasoningError(
                error_id="ad_hominem",
                error_type="ad_hominem",
                description="Personal attack detected instead of addressing argument",
                location="reasoning_text",
                severity="medium",
                suggested_correction="Focus on the argument, not personal characteristics",
                confidence=0.7
            ))
        
        # False dichotomy
        if re.search(r"either.*or.*only|must.*or.*nothing", text_lower):
            fallacies.append(ReasoningError(
                error_id="false_dichotomy",
                error_type="false_dichotomy",
                description="False either/or choice presented",
                location="reasoning_text",
                severity="medium",
                suggested_correction="Consider additional alternatives",
                confidence=0.6
            ))
        
        # Appeal to authority without justification
        if "expert says" in text_lower or "authority states" in text_lower:
            if "because" not in text_lower and "evidence" not in text_lower:
                fallacies.append(ReasoningError(
                    error_id="appeal_to_authority",
                    error_type="appeal_to_authority",
                    description="Appeal to authority without supporting evidence",
                    location="reasoning_text",
                    severity="low",
                    suggested_correction="Provide evidence or reasoning behind the authority's position",
                    confidence=0.5
                ))
        
        return fallacies
    
    async def detect_factual_errors(self, reasoning_result: Dict[str, Any]) -> List[ReasoningError]:
        """Detect factual errors in reasoning"""
        factual_errors = []
        
        # Extract factual claims
        factual_claims = self.extract_factual_claims(reasoning_result)
        
        for claim in factual_claims:
            error = await self.verify_factual_claim(claim)
            if error:
                factual_errors.append(error)
        
        return factual_errors
    
    def extract_factual_claims(self, reasoning_result: Dict[str, Any]) -> List[str]:
        """Extract factual claims from reasoning"""
        claims = []
        reasoning_text = self.extract_reasoning_text(reasoning_result)
        
        # Look for numerical claims
        numerical_claims = re.findall(r'\d+\s*(?:days?|weeks?|months?|years?)', reasoning_text)
        claims.extend(numerical_claims)
        
        # Look for policy claims
        sentences = reasoning_text.split('.')
        for sentence in sentences:
            if any(word in sentence.lower() for word in ["policy", "rule", "regulation", "states", "requires"]):
                claims.append(sentence.strip())
        
        return claims[:5]  # Limit to avoid too many claims
    
    async def verify_factual_claim(self, claim: str) -> ReasoningError:
        """Verify a factual claim and return error if invalid"""
        claim_lower = claim.lower()
        
        # Check against known facts
        if "30 days" in claim_lower and "privilege leave" in claim_lower:
            return None  # Correct fact
        elif "26 weeks" in claim_lower and "maternity" in claim_lower:
            return None  # Correct fact
        elif re.search(r'(\d+)\s*days.*privilege', claim_lower):
            # Extract number
            match = re.search(r'(\d+)', claim_lower)
            if match:
                days = int(match.group(1))
                if days != 30:
                    return ReasoningError(
                        error_id=f"factual_error_days_{days}",
                        error_type="factual_error",
                        description=f"Incorrect privilege leave days: {days} (should be 30)",
                        location="factual_claim",
                        severity="high",
                        suggested_correction="Correct to 30 days privilege leave",
                        confidence=0.9
                    )
        
        return None  # No error detected
    
    async def detect_consistency_errors(self, reasoning_result: Dict[str, Any]) -> List[ReasoningError]:
        """Detect consistency errors in reasoning"""
        consistency_errors = []
        
        # Check for internal consistency
        reasoning_steps = self.extract_reasoning_steps(reasoning_result)
        
        for i in range(len(reasoning_steps) - 1):
            current_step = reasoning_steps[i]
            next_step = reasoning_steps[i + 1]
            
            inconsistency = self.check_step_consistency(current_step, next_step, i)
            if inconsistency:
                consistency_errors.append(inconsistency)
        
        return consistency_errors
    
    def check_step_consistency(self, step1: Dict[str, Any], step2: Dict[str, Any], step_index: int) -> ReasoningError:
        """Check consistency between two reasoning steps"""
        step1_text = str(step1).lower()
        step2_text = str(step2).lower()
        
        # Check for confidence inconsistency
        conf1 = step1.get("confidence", 0.5)
        conf2 = step2.get("confidence", 0.5)
        
        if abs(conf1 - conf2) > 0.5:
            return ReasoningError(
                error_id=f"confidence_inconsistency_{step_index}",
                error_type="consistency_error",
                description=f"Large confidence drop between steps {step_index+1} and {step_index+2}",
                location=f"steps_{step_index}_{step_index+1}",
                severity="medium",
                suggested_correction="Review reasoning chain for logical gaps",
                confidence=0.6
            )
        
        # Check for topic inconsistency
        if not self.has_topic_continuity(step1_text, step2_text):
            return ReasoningError(
                error_id=f"topic_inconsistency_{step_index}",
                error_type="consistency_error",
                description=f"Abrupt topic change between steps {step_index+1} and {step_index+2}",
                location=f"steps_{step_index}_{step_index+1}",
                severity="low",
                suggested_correction="Add transitional reasoning or explanation",
                confidence=0.5
            )
        
        return None
    
    def has_topic_continuity(self, text1: str, text2: str) -> bool:
        """Check if two texts have topic continuity"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        # Remove common words
        common_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        words1 = words1 - common_words
        words2 = words2 - common_words
        
        # Check for overlap
        overlap = words1 & words2
        return len(overlap) >= 2 or len(words1) < 3 or len(words2) < 3
    
    def extract_reasoning_text(self, reasoning_result: Dict[str, Any]) -> str:
        """Extract all reasoning text from result"""
        text_parts = []
        
        # From final reasoning
        if "final_reasoning" in reasoning_result:
            final_reasoning = reasoning_result["final_reasoning"]
            if "conclusion" in final_reasoning:
                text_parts.append(str(final_reasoning["conclusion"]))
            if "reasoning_chain" in final_reasoning:
                for step in final_reasoning["reasoning_chain"]:
                    if "reasoning" in step:
                        text_parts.append(str(step["reasoning"]))
        
        # From response
        if "response" in reasoning_result:
            text_parts.append(str(reasoning_result["response"]))
        
        return " ".join(text_parts)
    
    def extract_reasoning_steps(self, reasoning_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract reasoning steps from result"""
        steps = []
        
        # From reasoning chain
        if "final_reasoning" in reasoning_result:
            final_reasoning = reasoning_result["final_reasoning"]
            if "reasoning_chain" in final_reasoning:
                steps.extend(final_reasoning["reasoning_chain"])
        
        # From reasoning steps
        if "reasoning_steps" in reasoning_result:
            for step in reasoning_result["reasoning_steps"]:
                if hasattr(step, 'description'):
                    steps.append({
                        "description": step.description,
                        "confidence": step.confidence,
                        "reasoning_type": step.reasoning_type
                    })
        
        return steps
    
    def assess_overall_severity(self, errors: List[ReasoningError]) -> str:
        """Assess overall severity of all errors"""
        if not errors:
            return "none"
        
        severity_counts = {"high": 0, "medium": 0, "low": 0}
        for error in errors:
            severity_counts[error.severity] += 1
        
        if severity_counts["high"] > 0:
            return "high"
        elif severity_counts["medium"] > 1:
            return "high"
        elif severity_counts["medium"] > 0:
            return "medium"
        else:
            return "low"
    
    async def generate_corrections(self, errors: List[ReasoningError]) -> Dict[str, Any]:
        """Generate correction suggestions for detected errors"""
        corrections = {
            "immediate_fixes": [],
            "structural_improvements": [],
            "verification_needed": [],
            "priority_order": []
        }
        
        # Sort errors by severity and confidence
        sorted_errors = sorted(errors, key=lambda e: (
            {"high": 3, "medium": 2, "low": 1}[e.severity], e.confidence
        ), reverse=True)
        
        for error in sorted_errors:
            correction = {
                "error_id": error.error_id,
                "correction": error.suggested_correction,
                "priority": error.severity,
                "confidence": error.confidence
            }
            
            if error.severity == "high":
                corrections["immediate_fixes"].append(correction)
            elif error.error_type in ["circular_reasoning", "consistency_error"]:
                corrections["structural_improvements"].append(correction)
            else:
                corrections["verification_needed"].append(correction)
            
            corrections["priority_order"].append(error.error_id)
        
        return corrections