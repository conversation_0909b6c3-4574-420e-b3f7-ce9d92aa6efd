from typing import Dict, List, Any, <PERSON><PERSON>
import math
from dataclasses import dataclass

@dataclass
class ConfidenceMetrics:
    overall_confidence: float
    uncertainty_level: float
    reliability_score: float
    calibration_score: float
    confidence_intervals: Dict[str, Tuple[float, float]]

class ConfidenceEstimator:
    def __init__(self):
        self.calibration_data = []
        self.uncertainty_factors = {
            "data_quality": 0.2,
            "reasoning_complexity": 0.3,
            "evidence_strength": 0.25,
            "consistency": 0.25
        }
        
    async def estimate_confidence(self, reasoning_result: Dict[str, Any]) -> ConfidenceMetrics:
        """Comprehensive confidence estimation"""
        # Calculate individual confidence components
        uncertainty = await self.quantify_uncertainty(reasoning_result)
        reliability = await self.assess_reliability(reasoning_result)
        calibration = await self.calibrate_confidence(reasoning_result)
        
        # Calculate overall confidence
        base_confidence = reasoning_result.get("confidence", 0.5)
        adjusted_confidence = self.adjust_confidence(base_confidence, uncertainty, reliability)
        
        # Calculate confidence intervals
        intervals = self.calculate_confidence_intervals(adjusted_confidence, uncertainty)
        
        return ConfidenceMetrics(
            overall_confidence=adjusted_confidence,
            uncertainty_level=uncertainty,
            reliability_score=reliability,
            calibration_score=calibration,
            confidence_intervals=intervals
        )
    
    async def quantify_uncertainty(self, reasoning_result: Dict[str, Any]) -> float:
        """Quantify uncertainty in reasoning"""
        uncertainty_components = {
            "data_uncertainty": await self.assess_data_uncertainty(reasoning_result),
            "model_uncertainty": await self.assess_model_uncertainty(reasoning_result),
            "reasoning_uncertainty": await self.assess_reasoning_uncertainty(reasoning_result),
            "context_uncertainty": await self.assess_context_uncertainty(reasoning_result)
        }
        
        # Weighted combination of uncertainties
        total_uncertainty = 0.0
        weights = {"data_uncertainty": 0.3, "model_uncertainty": 0.2, 
                  "reasoning_uncertainty": 0.3, "context_uncertainty": 0.2}
        
        for component, weight in weights.items():
            total_uncertainty += uncertainty_components[component] * weight
        
        return min(total_uncertainty, 1.0)
    
    async def assess_data_uncertainty(self, reasoning_result: Dict[str, Any]) -> float:
        """Assess uncertainty from data quality"""
        # Check for data quality indicators
        reasoning_text = str(reasoning_result).lower()
        
        uncertainty = 0.3  # Base uncertainty
        
        # Reduce uncertainty if explicit data mentioned
        if any(word in reasoning_text for word in ["data", "evidence", "study", "research"]):
            uncertainty -= 0.1
        
        # Increase uncertainty if vague language
        if any(word in reasoning_text for word in ["might", "could", "possibly", "perhaps"]):
            uncertainty += 0.2
        
        # Check for specific numbers/facts
        import re
        if re.search(r'\d+', reasoning_text):
            uncertainty -= 0.1
        
        return max(0.0, min(uncertainty, 1.0))
    
    async def assess_model_uncertainty(self, reasoning_result: Dict[str, Any]) -> float:
        """Assess uncertainty from model limitations"""
        # Check reasoning complexity
        reasoning_paths = reasoning_result.get("reasoning_paths", [])
        
        if len(reasoning_paths) > 3:
            return 0.2  # Lower uncertainty with multiple paths
        elif len(reasoning_paths) > 1:
            return 0.3
        else:
            return 0.5  # Higher uncertainty with single path
    
    async def assess_reasoning_uncertainty(self, reasoning_result: Dict[str, Any]) -> float:
        """Assess uncertainty from reasoning process"""
        uncertainty = 0.4  # Base reasoning uncertainty
        
        # Check for logical structure
        reasoning_text = str(reasoning_result).lower()
        
        # Reduce uncertainty for structured reasoning
        if any(word in reasoning_text for word in ["because", "therefore", "since", "thus"]):
            uncertainty -= 0.1
        
        # Check for multiple reasoning steps
        if "reasoning_chain" in str(reasoning_result):
            chain = reasoning_result.get("final_reasoning", {}).get("reasoning_chain", [])
            if len(chain) > 2:
                uncertainty -= 0.1
        
        # Increase uncertainty for contradictions
        if any(word in reasoning_text for word in ["however", "but", "although"]):
            uncertainty += 0.1
        
        return max(0.0, min(uncertainty, 1.0))
    
    async def assess_context_uncertainty(self, reasoning_result: Dict[str, Any]) -> float:
        """Assess uncertainty from context limitations"""
        # Check if context is well-defined
        context_indicators = ["organization", "policy", "specific", "clear"]
        reasoning_text = str(reasoning_result).lower()
        
        context_clarity = sum(1 for indicator in context_indicators if indicator in reasoning_text)
        
        # More context indicators = less uncertainty
        uncertainty = 0.5 - (context_clarity * 0.1)
        return max(0.1, min(uncertainty, 0.8))
    
    async def assess_reliability(self, reasoning_result: Dict[str, Any]) -> float:
        """Assess reliability of reasoning"""
        reliability_factors = {
            "consistency": await self.check_internal_consistency(reasoning_result),
            "evidence_quality": await self.assess_evidence_quality(reasoning_result),
            "logical_structure": await self.assess_logical_structure(reasoning_result),
            "completeness": await self.assess_completeness(reasoning_result)
        }
        
        # Weighted average
        weights = {"consistency": 0.3, "evidence_quality": 0.3, 
                  "logical_structure": 0.2, "completeness": 0.2}
        
        reliability = sum(reliability_factors[factor] * weight 
                         for factor, weight in weights.items())
        
        return reliability
    
    async def check_internal_consistency(self, reasoning_result: Dict[str, Any]) -> float:
        """Check internal consistency of reasoning"""
        # Simple consistency check
        reasoning_text = str(reasoning_result).lower()
        
        # Check for contradictory terms
        contradictions = [
            ("allow", "prohibit"), ("can", "cannot"), ("yes", "no"),
            ("required", "optional"), ("must", "may not")
        ]
        
        consistency_score = 1.0
        for pos, neg in contradictions:
            if pos in reasoning_text and neg in reasoning_text:
                consistency_score -= 0.2
        
        return max(0.0, consistency_score)
    
    async def assess_evidence_quality(self, reasoning_result: Dict[str, Any]) -> float:
        """Assess quality of evidence used"""
        reasoning_text = str(reasoning_result).lower()
        
        evidence_score = 0.5  # Base score
        
        # High-quality evidence indicators
        if any(word in reasoning_text for word in ["policy", "regulation", "official", "documented"]):
            evidence_score += 0.2
        
        # Specific data indicators
        if any(word in reasoning_text for word in ["30 days", "26 weeks", "specific number"]):
            evidence_score += 0.2
        
        # Vague evidence indicators
        if any(word in reasoning_text for word in ["generally", "usually", "typically"]):
            evidence_score -= 0.1
        
        return max(0.0, min(evidence_score, 1.0))
    
    async def assess_logical_structure(self, reasoning_result: Dict[str, Any]) -> float:
        """Assess logical structure quality"""
        reasoning_text = str(reasoning_result).lower()
        
        structure_score = 0.5  # Base score
        
        # Logical connectors
        logical_connectors = ["because", "therefore", "since", "thus", "hence", "consequently"]
        connector_count = sum(1 for connector in logical_connectors if connector in reasoning_text)
        
        structure_score += min(connector_count * 0.1, 0.3)
        
        # Check for premise-conclusion structure
        if "because" in reasoning_text and "therefore" in reasoning_text:
            structure_score += 0.2
        
        return min(structure_score, 1.0)
    
    async def assess_completeness(self, reasoning_result: Dict[str, Any]) -> float:
        """Assess completeness of reasoning"""
        completeness_score = 0.5  # Base score
        
        # Check for reasoning chain
        if "reasoning_chain" in str(reasoning_result):
            chain = reasoning_result.get("final_reasoning", {}).get("reasoning_chain", [])
            completeness_score += min(len(chain) * 0.1, 0.3)
        
        # Check for multiple reasoning paths
        reasoning_paths = reasoning_result.get("reasoning_paths", [])
        if len(reasoning_paths) > 1:
            completeness_score += 0.2
        
        return min(completeness_score, 1.0)
    
    async def calibrate_confidence(self, reasoning_result: Dict[str, Any]) -> float:
        """Calibrate confidence based on historical performance"""
        base_confidence = reasoning_result.get("confidence", 0.5)
        
        # Simple calibration - in production would use historical data
        calibration_adjustment = 0.0
        
        # Adjust based on reasoning type
        reasoning_type = reasoning_result.get("reasoning_type", "unknown")
        
        if reasoning_type == "tree_of_thoughts":
            calibration_adjustment = 0.1  # Generally more reliable
        elif reasoning_type == "chain_of_thought":
            calibration_adjustment = 0.05
        elif reasoning_type == "analogical":
            calibration_adjustment = -0.05  # Generally less reliable
        
        # Adjust based on validation results
        if "quality_evaluation" in reasoning_result:
            quality_score = reasoning_result["quality_evaluation"].get("overall_score", 0.5)
            calibration_adjustment += (quality_score - 0.5) * 0.2
        
        calibrated_confidence = base_confidence + calibration_adjustment
        return max(0.0, min(calibrated_confidence, 1.0))
    
    def adjust_confidence(self, base_confidence: float, uncertainty: float, reliability: float) -> float:
        """Adjust confidence based on uncertainty and reliability"""
        # Confidence adjustment formula
        uncertainty_penalty = uncertainty * 0.3
        reliability_bonus = (reliability - 0.5) * 0.2
        
        adjusted_confidence = base_confidence - uncertainty_penalty + reliability_bonus
        return max(0.0, min(adjusted_confidence, 1.0))
    
    def calculate_confidence_intervals(self, confidence: float, uncertainty: float) -> Dict[str, Tuple[float, float]]:
        """Calculate confidence intervals"""
        # Standard error estimation
        std_error = uncertainty * 0.5
        
        # Different confidence levels
        intervals = {}
        
        # 68% confidence interval (1 standard deviation)
        intervals["68%"] = (
            max(0.0, confidence - std_error),
            min(1.0, confidence + std_error)
        )
        
        # 95% confidence interval (1.96 standard deviations)
        intervals["95%"] = (
            max(0.0, confidence - 1.96 * std_error),
            min(1.0, confidence + 1.96 * std_error)
        )
        
        # 99% confidence interval (2.58 standard deviations)
        intervals["99%"] = (
            max(0.0, confidence - 2.58 * std_error),
            min(1.0, confidence + 2.58 * std_error)
        )
        
        return intervals
    
    async def update_calibration(self, predicted_confidence: float, actual_outcome: bool):
        """Update calibration data with new observation"""
        self.calibration_data.append({
            "predicted_confidence": predicted_confidence,
            "actual_outcome": 1.0 if actual_outcome else 0.0,
            "timestamp": "now"  # Would use actual timestamp in production
        })
        
        # Keep only recent data
        if len(self.calibration_data) > 1000:
            self.calibration_data = self.calibration_data[-1000:]
    
    def get_calibration_metrics(self) -> Dict[str, float]:
        """Get calibration performance metrics"""
        if len(self.calibration_data) < 10:
            return {"insufficient_data": True}
        
        # Calculate calibration error
        calibration_error = 0.0
        bin_size = 0.1
        
        for i in range(10):  # 10 bins from 0.0 to 1.0
            bin_start = i * bin_size
            bin_end = (i + 1) * bin_size
            
            # Get predictions in this bin
            bin_predictions = [
                d for d in self.calibration_data 
                if bin_start <= d["predicted_confidence"] < bin_end
            ]
            
            if len(bin_predictions) > 0:
                avg_prediction = sum(d["predicted_confidence"] for d in bin_predictions) / len(bin_predictions)
                avg_outcome = sum(d["actual_outcome"] for d in bin_predictions) / len(bin_predictions)
                
                calibration_error += abs(avg_prediction - avg_outcome) * len(bin_predictions)
        
        calibration_error /= len(self.calibration_data)
        
        return {
            "calibration_error": calibration_error,
            "total_samples": len(self.calibration_data),
            "well_calibrated": calibration_error < 0.1
        }