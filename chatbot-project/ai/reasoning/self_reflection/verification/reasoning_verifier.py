from typing import Dict, List, Any, Tuple
from dataclasses import dataclass

@dataclass
class VerificationStep:
    step_id: str
    description: str
    verification_type: str
    input_data: Any
    expected_output: Any
    actual_output: Any
    is_valid: bool
    confidence: float

class ReasoningVerifier:
    def __init__(self):
        self.verification_rules = {}
        self.assumption_validators = {}
        
    async def verify_reasoning_chain(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Step-by-step verification of reasoning chain"""
        verification_result = {
            "total_steps": 0,
            "verified_steps": 0,
            "failed_steps": 0,
            "verification_details": [],
            "overall_validity": 0.0,
            "critical_issues": []
        }
        
        # Extract reasoning steps
        reasoning_steps = self.extract_reasoning_steps(reasoning_result)
        verification_result["total_steps"] = len(reasoning_steps)
        
        # Verify each step
        for i, step in enumerate(reasoning_steps):
            step_verification = await self.verify_single_step(step, i)
            verification_result["verification_details"].append(step_verification)
            
            if step_verification.is_valid:
                verification_result["verified_steps"] += 1
            else:
                verification_result["failed_steps"] += 1
                if step_verification.confidence < 0.3:
                    verification_result["critical_issues"].append(step_verification.description)
        
        # Calculate overall validity
        if verification_result["total_steps"] > 0:
            verification_result["overall_validity"] = verification_result["verified_steps"] / verification_result["total_steps"]
        
        return verification_result
    
    async def verify_single_step(self, step: Dict[str, Any], step_index: int) -> VerificationStep:
        """Verify a single reasoning step"""
        step_id = f"step_{step_index}"
        description = step.get("description", f"Reasoning step {step_index}")
        
        # Determine verification type
        verification_type = self.classify_step_type(step)
        
        # Perform verification based on type
        if verification_type == "logical_inference":
            is_valid, confidence = await self.verify_logical_inference(step)
        elif verification_type == "factual_claim":
            is_valid, confidence = await self.verify_factual_claim(step)
        elif verification_type == "assumption":
            is_valid, confidence = await self.verify_assumption(step)
        else:
            is_valid, confidence = await self.verify_general_step(step)
        
        return VerificationStep(
            step_id=step_id,
            description=description,
            verification_type=verification_type,
            input_data=step.get("input_data"),
            expected_output=step.get("expected_output"),
            actual_output=step.get("output_data"),
            is_valid=is_valid,
            confidence=confidence
        )
    
    def classify_step_type(self, step: Dict[str, Any]) -> str:
        """Classify the type of reasoning step"""
        description = step.get("description", "").lower()
        reasoning = step.get("reasoning", "").lower()
        
        if any(word in description for word in ["therefore", "thus", "hence", "conclude"]):
            return "logical_inference"
        elif any(word in description for word in ["fact", "data", "evidence", "policy"]):
            return "factual_claim"
        elif any(word in description for word in ["assume", "suppose", "given"]):
            return "assumption"
        else:
            return "general_reasoning"
    
    async def verify_logical_inference(self, step: Dict[str, Any]) -> Tuple[bool, float]:
        """Verify logical inference step"""
        reasoning = step.get("reasoning", "").lower()
        
        # Check for logical connectors
        has_premise = any(word in reasoning for word in ["because", "since", "given"])
        has_conclusion = any(word in reasoning for word in ["therefore", "thus", "hence"])
        
        if has_premise and has_conclusion:
            return True, 0.8
        elif has_premise or has_conclusion:
            return True, 0.6
        else:
            return False, 0.3
    
    async def verify_factual_claim(self, step: Dict[str, Any]) -> Tuple[bool, float]:
        """Verify factual claim"""
        description = step.get("description", "").lower()
        
        # Simple factual verification
        if "30 days" in description and "privilege leave" in description:
            return True, 0.9
        elif "26 weeks" in description and "maternity" in description:
            return True, 0.9
        elif "policy" in description:
            return True, 0.7
        else:
            return True, 0.5  # Neutral for unverifiable claims
    
    async def verify_assumption(self, step: Dict[str, Any]) -> Tuple[bool, float]:
        """Verify assumption validity"""
        description = step.get("description", "").lower()
        
        # Check if assumption is reasonable
        if any(word in description for word in ["reasonable", "standard", "typical"]):
            return True, 0.7
        elif any(word in description for word in ["extreme", "unusual", "unlikely"]):
            return False, 0.3
        else:
            return True, 0.6  # Neutral assumption
    
    async def verify_general_step(self, step: Dict[str, Any]) -> Tuple[bool, float]:
        """Verify general reasoning step"""
        confidence = step.get("confidence", 0.5)
        
        # Use step's own confidence as verification basis
        if confidence > 0.7:
            return True, confidence
        elif confidence > 0.4:
            return True, confidence * 0.8
        else:
            return False, confidence
    
    def extract_reasoning_steps(self, reasoning_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract reasoning steps from result"""
        steps = []
        
        # From reasoning chain
        if "final_reasoning" in reasoning_result:
            final_reasoning = reasoning_result["final_reasoning"]
            if "reasoning_chain" in final_reasoning:
                for step in final_reasoning["reasoning_chain"]:
                    steps.append({
                        "description": step.get("action", ""),
                        "reasoning": step.get("reasoning", ""),
                        "output_data": step.get("result", ""),
                        "confidence": 0.7
                    })
        
        # From reasoning steps
        if "reasoning_steps" in reasoning_result:
            for step in reasoning_result["reasoning_steps"]:
                if hasattr(step, 'description'):
                    steps.append({
                        "description": step.description,
                        "reasoning": f"{step.reasoning_type}: {step.output_data}",
                        "output_data": step.output_data,
                        "confidence": step.confidence
                    })
        
        return steps
    
    async def validate_assumptions(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate assumptions in reasoning"""
        assumptions = self.extract_assumptions(reasoning_result)
        
        validation_result = {
            "total_assumptions": len(assumptions),
            "valid_assumptions": 0,
            "questionable_assumptions": 0,
            "assumption_details": []
        }
        
        for assumption in assumptions:
            validity = await self.assess_assumption_validity(assumption)
            validation_result["assumption_details"].append(validity)
            
            if validity["is_valid"]:
                validation_result["valid_assumptions"] += 1
            else:
                validation_result["questionable_assumptions"] += 1
        
        return validation_result
    
    def extract_assumptions(self, reasoning_result: Dict[str, Any]) -> List[str]:
        """Extract assumptions from reasoning"""
        assumptions = []
        
        # Look for assumption indicators
        content = str(reasoning_result).lower()
        assumption_indicators = ["assume", "suppose", "given that", "if we consider"]
        
        for indicator in assumption_indicators:
            if indicator in content:
                assumptions.append(f"Assumption involving '{indicator}'")
        
        # Default assumptions
        if not assumptions:
            assumptions.append("Standard organizational policies apply")
        
        return assumptions
    
    async def assess_assumption_validity(self, assumption: str) -> Dict[str, Any]:
        """Assess validity of a single assumption"""
        assumption_lower = assumption.lower()
        
        validity = {
            "assumption": assumption,
            "is_valid": True,
            "confidence": 0.7,
            "reasoning": "Standard assumption"
        }
        
        # Check for problematic assumptions
        if any(word in assumption_lower for word in ["always", "never", "all", "none"]):
            validity["is_valid"] = False
            validity["confidence"] = 0.3
            validity["reasoning"] = "Overly broad assumption"
        elif "standard" in assumption_lower or "typical" in assumption_lower:
            validity["confidence"] = 0.8
            validity["reasoning"] = "Reasonable standard assumption"
        
        return validity
    
    async def check_conclusions(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Check validity of conclusions"""
        conclusions = self.extract_conclusions(reasoning_result)
        
        conclusion_check = {
            "total_conclusions": len(conclusions),
            "supported_conclusions": 0,
            "unsupported_conclusions": 0,
            "conclusion_details": []
        }
        
        for conclusion in conclusions:
            support_check = await self.assess_conclusion_support(conclusion, reasoning_result)
            conclusion_check["conclusion_details"].append(support_check)
            
            if support_check["is_supported"]:
                conclusion_check["supported_conclusions"] += 1
            else:
                conclusion_check["unsupported_conclusions"] += 1
        
        return conclusion_check
    
    def extract_conclusions(self, reasoning_result: Dict[str, Any]) -> List[str]:
        """Extract conclusions from reasoning"""
        conclusions = []
        
        # From final reasoning
        if "final_reasoning" in reasoning_result:
            final_reasoning = reasoning_result["final_reasoning"]
            if "conclusion" in final_reasoning:
                conclusions.append(final_reasoning["conclusion"])
        
        # From response
        if "response" in reasoning_result:
            conclusions.append(reasoning_result["response"])
        
        return conclusions if conclusions else ["No explicit conclusion found"]
    
    async def assess_conclusion_support(self, conclusion: str, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Assess if conclusion is supported by reasoning"""
        support_assessment = {
            "conclusion": conclusion,
            "is_supported": True,
            "support_strength": 0.7,
            "supporting_evidence": [],
            "gaps": []
        }
        
        # Check for supporting evidence
        reasoning_content = str(reasoning_result).lower()
        conclusion_lower = conclusion.lower()
        
        # Look for evidence keywords
        evidence_keywords = ["because", "since", "data shows", "policy states", "evidence"]
        supporting_evidence = [kw for kw in evidence_keywords if kw in reasoning_content]
        
        support_assessment["supporting_evidence"] = supporting_evidence
        
        if len(supporting_evidence) >= 2:
            support_assessment["support_strength"] = 0.8
        elif len(supporting_evidence) == 1:
            support_assessment["support_strength"] = 0.6
        else:
            support_assessment["support_strength"] = 0.4
            support_assessment["gaps"].append("Limited supporting evidence")
        
        # Check logical connection
        if "therefore" in reasoning_content or "thus" in reasoning_content:
            support_assessment["support_strength"] += 0.1
        
        support_assessment["is_supported"] = support_assessment["support_strength"] > 0.5
        
        return support_assessment