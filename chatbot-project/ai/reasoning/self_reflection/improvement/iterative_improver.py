from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import asyncio

@dataclass
class ImprovementIteration:
    iteration_id: int
    original_result: Dict[str, Any]
    improved_result: Dict[str, Any]
    improvement_score: float
    changes_made: List[str]
    stopping_reason: str

class IterativeImprover:
    def __init__(self, max_iterations: int = 5, improvement_threshold: float = 0.05):
        self.max_iterations = max_iterations
        self.improvement_threshold = improvement_threshold
        self.improvement_history = []
        
    async def iterative_improvement(self, initial_reasoning: Dict[str, Any], 
                                  context: Dict[str, Any]) -> Dict[str, Any]:
        """Iteratively improve reasoning through refinement loops"""
        improvement_result = {
            "initial_reasoning": initial_reasoning,
            "iterations": [],
            "final_reasoning": initial_reasoning,
            "total_iterations": 0,
            "improvement_achieved": 0.0,
            "stopping_reason": "not_started"
        }
        
        current_reasoning = initial_reasoning.copy()
        
        for iteration in range(self.max_iterations):
            # Analyze current reasoning for improvement opportunities
            improvement_opportunities = await self.identify_improvement_opportunities(current_reasoning)
            
            if not improvement_opportunities:
                improvement_result["stopping_reason"] = "no_improvements_found"
                break
            
            # Apply improvements
            improved_reasoning = await self.apply_improvements(current_reasoning, improvement_opportunities, context)
            
            # Evaluate improvement
            improvement_score = await self.evaluate_improvement(current_reasoning, improved_reasoning)
            
            # Create iteration record
            iteration_record = ImprovementIteration(
                iteration_id=iteration + 1,
                original_result=current_reasoning.copy(),
                improved_result=improved_reasoning.copy(),
                improvement_score=improvement_score,
                changes_made=[opp["improvement_type"] for opp in improvement_opportunities],
                stopping_reason=""
            )
            
            improvement_result["iterations"].append(iteration_record)
            improvement_result["total_iterations"] += 1
            
            # Check stopping criteria
            if improvement_score < self.improvement_threshold:
                improvement_result["stopping_reason"] = "minimal_improvement"
                break
            
            # Check if reasoning quality is degrading
            if improvement_score < 0:
                improvement_result["stopping_reason"] = "quality_degradation"
                break
            
            current_reasoning = improved_reasoning
        
        # Set final results
        if improvement_result["total_iterations"] == self.max_iterations:
            improvement_result["stopping_reason"] = "max_iterations_reached"
        
        improvement_result["final_reasoning"] = current_reasoning
        improvement_result["improvement_achieved"] = self.calculate_total_improvement(improvement_result["iterations"])
        
        return improvement_result
    
    async def identify_improvement_opportunities(self, reasoning_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify opportunities for improving reasoning"""
        opportunities = []
        
        # Check confidence levels
        confidence = reasoning_result.get("confidence", 0.5)
        if confidence < 0.7:
            opportunities.append({
                "improvement_type": "confidence_enhancement",
                "description": "Low confidence detected, enhance reasoning strength",
                "priority": "high",
                "target_component": "overall_reasoning"
            })
        
        # Check reasoning completeness
        reasoning_paths = reasoning_result.get("reasoning_paths", [])
        if len(reasoning_paths) < 2:
            opportunities.append({
                "improvement_type": "path_diversification",
                "description": "Limited reasoning paths, add alternative approaches",
                "priority": "medium",
                "target_component": "reasoning_paths"
            })
        
        # Check for validation issues
        if "quality_evaluation" in reasoning_result:
            quality_eval = reasoning_result["quality_evaluation"]
            if quality_eval.get("overall_score", 0.5) < 0.6:
                opportunities.append({
                    "improvement_type": "quality_enhancement",
                    "description": "Low quality score, improve reasoning quality",
                    "priority": "high",
                    "target_component": "reasoning_quality"
                })
        
        # Check for missing evidence
        reasoning_text = str(reasoning_result).lower()
        if not any(word in reasoning_text for word in ["because", "evidence", "data", "policy"]):
            opportunities.append({
                "improvement_type": "evidence_addition",
                "description": "Lack of supporting evidence, add factual support",
                "priority": "medium",
                "target_component": "evidence_support"
            })
        
        # Check for logical gaps
        if "reasoning_chain" in str(reasoning_result):
            chain = reasoning_result.get("final_reasoning", {}).get("reasoning_chain", [])
            if len(chain) < 3:
                opportunities.append({
                    "improvement_type": "logical_expansion",
                    "description": "Short reasoning chain, expand logical steps",
                    "priority": "medium",
                    "target_component": "reasoning_chain"
                })
        
        return opportunities
    
    async def apply_improvements(self, reasoning_result: Dict[str, Any], 
                               opportunities: List[Dict[str, Any]], 
                               context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply identified improvements to reasoning"""
        improved_reasoning = reasoning_result.copy()
        
        for opportunity in opportunities:
            improvement_type = opportunity["improvement_type"]
            
            if improvement_type == "confidence_enhancement":
                improved_reasoning = await self.enhance_confidence(improved_reasoning, context)
            elif improvement_type == "path_diversification":
                improved_reasoning = await self.diversify_paths(improved_reasoning, context)
            elif improvement_type == "quality_enhancement":
                improved_reasoning = await self.enhance_quality(improved_reasoning, context)
            elif improvement_type == "evidence_addition":
                improved_reasoning = await self.add_evidence(improved_reasoning, context)
            elif improvement_type == "logical_expansion":
                improved_reasoning = await self.expand_logical_chain(improved_reasoning, context)
        
        return improved_reasoning
    
    async def enhance_confidence(self, reasoning_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance confidence in reasoning"""
        enhanced_result = reasoning_result.copy()
        
        # Boost confidence through additional validation
        current_confidence = enhanced_result.get("confidence", 0.5)
        
        # Add confidence boost based on context
        if context.get("organization"):
            current_confidence += 0.1  # Organization context adds confidence
        
        # Add confidence boost for specific policies
        reasoning_text = str(reasoning_result).lower()
        if "policy" in reasoning_text:
            current_confidence += 0.1
        
        enhanced_result["confidence"] = min(current_confidence, 1.0)
        
        # Add confidence explanation
        if "final_reasoning" not in enhanced_result:
            enhanced_result["final_reasoning"] = {}
        
        enhanced_result["final_reasoning"]["confidence_enhancement"] = "Added contextual validation"
        
        return enhanced_result
    
    async def diversify_paths(self, reasoning_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Add diverse reasoning paths"""
        enhanced_result = reasoning_result.copy()
        
        # Add alternative reasoning path
        existing_paths = enhanced_result.get("reasoning_paths", [])
        
        # Create new alternative path
        alternative_path = {
            "path_id": len(existing_paths),
            "approach": "alternative_analysis",
            "steps": [
                {"step": 1, "description": "Consider alternative perspective"},
                {"step": 2, "description": "Evaluate from different angle"},
                {"step": 3, "description": "Synthesize alternative conclusion"}
            ],
            "confidence": 0.6
        }
        
        existing_paths.append(alternative_path)
        enhanced_result["reasoning_paths"] = existing_paths
        
        return enhanced_result
    
    async def enhance_quality(self, reasoning_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance overall reasoning quality"""
        enhanced_result = reasoning_result.copy()
        
        # Improve reasoning structure
        if "final_reasoning" not in enhanced_result:
            enhanced_result["final_reasoning"] = {}
        
        final_reasoning = enhanced_result["final_reasoning"]
        
        # Add structured reasoning if missing
        if "reasoning_chain" not in final_reasoning:
            final_reasoning["reasoning_chain"] = [
                {"step": 1, "action": "Analyze problem", "reasoning": "Systematic analysis"},
                {"step": 2, "action": "Apply knowledge", "reasoning": "Domain expertise applied"},
                {"step": 3, "action": "Draw conclusion", "reasoning": "Logical conclusion"}
            ]
        
        # Enhance conclusion if present
        if "conclusion" in final_reasoning:
            original_conclusion = final_reasoning["conclusion"]
            enhanced_conclusion = f"Based on systematic analysis, {original_conclusion.lower()}"
            final_reasoning["conclusion"] = enhanced_conclusion
        
        return enhanced_result
    
    async def add_evidence(self, reasoning_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Add supporting evidence to reasoning"""
        enhanced_result = reasoning_result.copy()
        
        # Add evidence based on context
        evidence_added = []
        
        if context.get("organization"):
            evidence_added.append(f"Organization policy for {context['organization']}")
        
        if "policy" in str(reasoning_result).lower():
            evidence_added.append("Documented policy guidelines")
        
        # Add evidence to reasoning
        if "final_reasoning" not in enhanced_result:
            enhanced_result["final_reasoning"] = {}
        
        enhanced_result["final_reasoning"]["supporting_evidence"] = evidence_added
        
        return enhanced_result
    
    async def expand_logical_chain(self, reasoning_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Expand logical reasoning chain"""
        enhanced_result = reasoning_result.copy()
        
        if "final_reasoning" not in enhanced_result:
            enhanced_result["final_reasoning"] = {}
        
        final_reasoning = enhanced_result["final_reasoning"]
        
        # Expand existing chain or create new one
        existing_chain = final_reasoning.get("reasoning_chain", [])
        
        if len(existing_chain) < 3:
            # Add intermediate steps
            expanded_chain = [
                {"step": 1, "action": "Problem identification", "reasoning": "Identify key components"},
                {"step": 2, "action": "Context analysis", "reasoning": "Analyze relevant context"},
                {"step": 3, "action": "Knowledge application", "reasoning": "Apply domain knowledge"},
                {"step": 4, "action": "Solution synthesis", "reasoning": "Synthesize solution"},
                {"step": 5, "action": "Conclusion validation", "reasoning": "Validate final conclusion"}
            ]
            
            final_reasoning["reasoning_chain"] = expanded_chain
        
        return enhanced_result
    
    async def evaluate_improvement(self, original: Dict[str, Any], improved: Dict[str, Any]) -> float:
        """Evaluate the improvement between original and improved reasoning"""
        improvement_score = 0.0
        
        # Compare confidence levels
        original_confidence = original.get("confidence", 0.5)
        improved_confidence = improved.get("confidence", 0.5)
        confidence_improvement = improved_confidence - original_confidence
        improvement_score += confidence_improvement * 0.3
        
        # Compare reasoning path count
        original_paths = len(original.get("reasoning_paths", []))
        improved_paths = len(improved.get("reasoning_paths", []))
        if improved_paths > original_paths:
            improvement_score += 0.2
        
        # Compare reasoning chain length
        original_chain_length = len(str(original.get("final_reasoning", {})).split())
        improved_chain_length = len(str(improved.get("final_reasoning", {})).split())
        if improved_chain_length > original_chain_length:
            improvement_score += 0.1
        
        # Compare evidence presence
        original_evidence = "evidence" in str(original).lower()
        improved_evidence = "evidence" in str(improved).lower()
        if improved_evidence and not original_evidence:
            improvement_score += 0.15
        
        return improvement_score
    
    def calculate_total_improvement(self, iterations: List[ImprovementIteration]) -> float:
        """Calculate total improvement across all iterations"""
        if not iterations:
            return 0.0
        
        return sum(iteration.improvement_score for iteration in iterations)
    
    async def progressive_enhancement(self, reasoning_result: Dict[str, Any], 
                                   enhancement_targets: List[str]) -> Dict[str, Any]:
        """Progressive enhancement focusing on specific targets"""
        enhanced_result = reasoning_result.copy()
        
        for target in enhancement_targets:
            if target == "logical_structure":
                enhanced_result = await self.enhance_logical_structure(enhanced_result)
            elif target == "evidence_quality":
                enhanced_result = await self.enhance_evidence_quality(enhanced_result)
            elif target == "conclusion_strength":
                enhanced_result = await self.enhance_conclusion_strength(enhanced_result)
        
        return enhanced_result
    
    async def enhance_logical_structure(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance logical structure of reasoning"""
        enhanced_result = reasoning_result.copy()
        
        # Add logical connectors to reasoning
        if "final_reasoning" in enhanced_result:
            final_reasoning = enhanced_result["final_reasoning"]
            if "conclusion" in final_reasoning:
                conclusion = final_reasoning["conclusion"]
                if not any(word in conclusion.lower() for word in ["therefore", "thus", "hence"]):
                    enhanced_conclusion = f"Therefore, {conclusion.lower()}"
                    final_reasoning["conclusion"] = enhanced_conclusion
        
        return enhanced_result
    
    async def enhance_evidence_quality(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance quality of evidence"""
        enhanced_result = reasoning_result.copy()
        
        # Add quality indicators to evidence
        if "final_reasoning" in enhanced_result:
            final_reasoning = enhanced_result["final_reasoning"]
            if "supporting_evidence" in final_reasoning:
                evidence_list = final_reasoning["supporting_evidence"]
                enhanced_evidence = [f"Verified: {evidence}" for evidence in evidence_list]
                final_reasoning["supporting_evidence"] = enhanced_evidence
        
        return enhanced_result
    
    async def enhance_conclusion_strength(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance strength of conclusions"""
        enhanced_result = reasoning_result.copy()
        
        if "final_reasoning" in enhanced_result:
            final_reasoning = enhanced_result["final_reasoning"]
            if "conclusion" in final_reasoning:
                conclusion = final_reasoning["conclusion"]
                # Add confidence language
                if enhanced_result.get("confidence", 0.5) > 0.7:
                    enhanced_conclusion = f"With high confidence, {conclusion.lower()}"
                else:
                    enhanced_conclusion = f"Based on available evidence, {conclusion.lower()}"
                
                final_reasoning["conclusion"] = enhanced_conclusion
        
        return enhanced_result
    
    def check_stopping_criteria(self, iteration: int, improvement_score: float, 
                               reasoning_result: Dict[str, Any]) -> Tuple[bool, str]:
        """Check if improvement should stop"""
        # Maximum iterations reached
        if iteration >= self.max_iterations:
            return True, "max_iterations_reached"
        
        # Minimal improvement
        if improvement_score < self.improvement_threshold:
            return True, "minimal_improvement"
        
        # Quality degradation
        if improvement_score < 0:
            return True, "quality_degradation"
        
        # High confidence achieved
        if reasoning_result.get("confidence", 0.5) > 0.9:
            return True, "high_confidence_achieved"
        
        return False, "continue"