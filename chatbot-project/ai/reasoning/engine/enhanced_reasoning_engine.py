import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from ai.reasoning.engine.advanced_reasoning import AdvancedReasoningEngine, ReasoningStep
from ai.reasoning.engine.reasoning_evaluator import ReasoningEvaluator
from ai.reasoning.engine.reasoning_trace_recorder import ReasoningTraceRecorder
from typing import Dict, List, Any, Optional
import asyncio
import time

class EnhancedReasoningEngine(AdvancedReasoningEngine):
    def __init__(self):
        super().__init__()
        self.evaluator = ReasoningEvaluator()
        self.trace_recorder = ReasoningTraceRecorder()
        self.reasoning_cache = {}
        self.performance_metrics = {
            "total_reasoning_tasks": 0,
            "successful_tasks": 0,
            "avg_confidence": 0.0,
            "avg_execution_time": 0.0
        }
    
    async def enhanced_reasoning(self, problem: str, context: Dict[str, Any], 
                               reasoning_type: str = "tree_of_thoughts") -> Dict[str, Any]:
        """Enhanced reasoning with evaluation and tracing"""
        start_time = time.time()
        
        # Start trace recording
        trace_id = self.trace_recorder.start_trace(problem, reasoning_type, context)
        
        try:
            # Check cache first
            cache_key = self.generate_cache_key(problem, context, reasoning_type)
            if cache_key in self.reasoning_cache:
                cached_result = self.reasoning_cache[cache_key]
                self.trace_recorder.record_step(trace_id, {
                    "step_type": "cache_hit",
                    "description": "Retrieved from cache",
                    "confidence": 0.9
                })
                
                # Complete trace
                execution_time = time.time() - start_time
                self.trace_recorder.complete_trace(trace_id, cached_result, cached_result.get("confidence", 0.5))
                
                return cached_result
            
            # Perform reasoning based on type
            if reasoning_type == "tree_of_thoughts":
                reasoning_result = await self.enhanced_tree_of_thoughts(problem, context, trace_id)
            elif reasoning_type == "chain_of_thought":
                reasoning_result = await self.chain_of_thought_reasoning(problem, context, trace_id)
            elif reasoning_type == "analogical":
                reasoning_result = await self.analogical_reasoning(problem, context, trace_id)
            else:
                reasoning_result = await self.tree_of_thoughts_reasoning(problem, context)
            
            # Evaluate reasoning quality
            evaluation = await self.evaluator.evaluate_reasoning_quality(reasoning_result)
            reasoning_result["quality_evaluation"] = evaluation
            
            # Cache result if high quality
            if evaluation["overall_score"] > 0.7:
                self.reasoning_cache[cache_key] = reasoning_result
            
            # Complete trace
            execution_time = time.time() - start_time
            trace = self.trace_recorder.complete_trace(trace_id, reasoning_result, reasoning_result.get("confidence", 0.5))
            
            # Update performance metrics
            self.update_performance_metrics(reasoning_result, execution_time, True)
            
            # Add trace info to result
            reasoning_result["trace_id"] = trace_id
            reasoning_result["execution_time"] = execution_time
            
            return reasoning_result
            
        except Exception as e:
            # Record error and complete trace
            error_result = {"error": str(e), "confidence": 0.0}
            execution_time = time.time() - start_time
            self.trace_recorder.complete_trace(trace_id, error_result, 0.0)
            self.update_performance_metrics(error_result, execution_time, False)
            
            return error_result
    
    async def enhanced_tree_of_thoughts(self, problem: str, context: Dict[str, Any], trace_id: str) -> Dict[str, Any]:
        """Enhanced Tree of Thoughts with detailed tracing"""
        self.trace_recorder.record_step(trace_id, {
            "step_type": "initialization",
            "description": "Starting Tree of Thoughts reasoning",
            "input_data": {"problem": problem, "context": context},
            "confidence": 0.8
        })
        
        # Generate multiple reasoning paths with tracing
        paths = await self.generate_traced_reasoning_paths(problem, context, trace_id)
        
        # Record decision point for path selection
        path_options = [{"path_id": i, "confidence": path.get("confidence", 0.5)} for i, path in enumerate(paths)]
        
        # Evaluate each path with tracing
        evaluated_paths = []
        for i, path in enumerate(paths):
            self.trace_recorder.record_step(trace_id, {
                "step_type": "path_evaluation",
                "description": f"Evaluating reasoning path {i+1}",
                "input_data": path,
                "confidence": 0.7
            })
            
            evaluation = await self.evaluate_reasoning_path(path, problem)
            evaluated_paths.append({
                "path_id": i,
                "steps": path,
                "evaluation": evaluation,
                "confidence": evaluation.get("confidence", 0.5)
            })
        
        # Select best path with decision recording
        best_path = max(evaluated_paths, key=lambda x: x["confidence"])
        
        self.trace_recorder.record_decision_point(trace_id, {
            "decision_type": "path_selection",
            "options": path_options,
            "selected_option": best_path["path_id"],
            "selection_criteria": "highest_confidence",
            "confidence": best_path["confidence"],
            "reasoning": f"Selected path {best_path['path_id']} with confidence {best_path['confidence']:.3f}"
        })
        
        # Generate final reasoning
        final_reasoning = await self.synthesize_reasoning(best_path, problem)
        
        self.trace_recorder.record_step(trace_id, {
            "step_type": "synthesis",
            "description": "Synthesizing final reasoning",
            "input_data": best_path,
            "output_data": final_reasoning,
            "confidence": best_path["confidence"]
        })
        
        return {
            "problem": problem,
            "reasoning_paths": evaluated_paths,
            "selected_path": best_path,
            "final_reasoning": final_reasoning,
            "confidence": best_path["confidence"],
            "reasoning_type": "enhanced_tree_of_thoughts"
        }
    
    async def chain_of_thought_reasoning(self, problem: str, context: Dict[str, Any], trace_id: str) -> Dict[str, Any]:
        """Chain of Thought reasoning with step-by-step tracing"""
        self.trace_recorder.record_step(trace_id, {
            "step_type": "initialization",
            "description": "Starting Chain of Thought reasoning",
            "confidence": 0.8
        })
        
        # Break down problem into steps
        reasoning_steps = []
        
        # Step 1: Problem analysis
        analysis = self.analyze_problem_components(problem)
        reasoning_steps.append(ReasoningStep(
            1, "Analyze problem components", problem, analysis, 0.8, "analysis"
        ))
        
        self.trace_recorder.record_step(trace_id, {
            "step_type": "analysis",
            "description": "Problem component analysis",
            "output_data": analysis,
            "confidence": 0.8
        })
        
        # Step 2: Apply context knowledge
        knowledge_application = self.apply_contextual_knowledge(analysis, context)
        reasoning_steps.append(ReasoningStep(
            2, "Apply contextual knowledge", analysis, knowledge_application, 0.7, "application"
        ))
        
        self.trace_recorder.record_step(trace_id, {
            "step_type": "knowledge_application",
            "description": "Applying contextual knowledge",
            "output_data": knowledge_application,
            "confidence": 0.7
        })
        
        # Step 3: Generate solution
        solution = self.generate_solution(knowledge_application, problem)
        reasoning_steps.append(ReasoningStep(
            3, "Generate solution", knowledge_application, solution, 0.75, "synthesis"
        ))
        
        self.trace_recorder.record_step(trace_id, {
            "step_type": "solution_generation",
            "description": "Generating final solution",
            "output_data": solution,
            "confidence": 0.75
        })
        
        # Calculate overall confidence
        overall_confidence = sum(step.confidence for step in reasoning_steps) / len(reasoning_steps)
        
        return {
            "problem": problem,
            "reasoning_steps": reasoning_steps,
            "final_solution": solution,
            "confidence": overall_confidence,
            "reasoning_type": "chain_of_thought"
        }
    
    async def analogical_reasoning(self, problem: str, context: Dict[str, Any], trace_id: str) -> Dict[str, Any]:
        """Analogical reasoning using similar cases"""
        self.trace_recorder.record_step(trace_id, {
            "step_type": "initialization",
            "description": "Starting analogical reasoning",
            "confidence": 0.8
        })
        
        # Find similar problems/cases
        similar_cases = self.find_similar_cases(problem, context)
        
        self.trace_recorder.record_step(trace_id, {
            "step_type": "case_retrieval",
            "description": "Finding similar cases",
            "output_data": {"similar_cases_count": len(similar_cases)},
            "confidence": 0.7
        })
        
        if not similar_cases:
            # Fallback to direct reasoning
            self.trace_recorder.record_backtrack(trace_id, {
                "from_step": "case_retrieval",
                "to_step": "direct_reasoning",
                "reason": "No similar cases found",
                "alternative_explored": True
            })
            
            return await self.chain_of_thought_reasoning(problem, context, trace_id)
        
        # Analyze analogies
        analogies = []
        for case in similar_cases:
            analogy = self.create_analogy(problem, case)
            analogies.append(analogy)
        
        self.trace_recorder.record_step(trace_id, {
            "step_type": "analogy_creation",
            "description": "Creating analogies from similar cases",
            "output_data": {"analogies_count": len(analogies)},
            "confidence": 0.8
        })
        
        # Apply analogical solution
        solution = self.apply_analogical_solution(analogies, problem)
        
        self.trace_recorder.record_step(trace_id, {
            "step_type": "analogical_solution",
            "description": "Applying analogical solution",
            "output_data": solution,
            "confidence": 0.75
        })
        
        return {
            "problem": problem,
            "similar_cases": similar_cases,
            "analogies": analogies,
            "final_solution": solution,
            "confidence": 0.75,
            "reasoning_type": "analogical"
        }
    
    def analyze_problem_components(self, problem: str) -> Dict[str, Any]:
        """Analyze problem into components"""
        components = {
            "entities": [],
            "actions": [],
            "constraints": [],
            "goals": []
        }
        
        problem_lower = problem.lower()
        
        # Simple entity extraction
        if "policy" in problem_lower:
            components["entities"].append("policy")
        if "leave" in problem_lower:
            components["entities"].append("leave")
        if "employee" in problem_lower:
            components["entities"].append("employee")
        
        # Action identification
        if "compare" in problem_lower:
            components["actions"].append("comparison")
        if "explain" in problem_lower:
            components["actions"].append("explanation")
        if "calculate" in problem_lower:
            components["actions"].append("calculation")
        
        return components
    
    def apply_contextual_knowledge(self, analysis: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply contextual knowledge to analysis"""
        knowledge_applied = {
            "domain_knowledge": [],
            "contextual_facts": [],
            "relevant_rules": []
        }
        
        # Apply organization-specific knowledge
        if context.get("organization"):
            knowledge_applied["domain_knowledge"].append(f"Organization: {context['organization']}")
        
        # Apply entity-specific knowledge
        for entity in analysis.get("entities", []):
            if entity == "policy":
                knowledge_applied["relevant_rules"].append("Policy rules apply")
            elif entity == "leave":
                knowledge_applied["relevant_rules"].append("Leave regulations apply")
        
        return knowledge_applied
    
    def generate_solution(self, knowledge: Dict[str, Any], problem: str) -> str:
        """Generate solution based on applied knowledge"""
        solution_parts = []
        
        if knowledge.get("domain_knowledge"):
            solution_parts.append("Based on organizational context")
        
        if knowledge.get("relevant_rules"):
            solution_parts.append("applying relevant policies")
        
        solution_parts.append("the recommended approach is to follow established procedures")
        
        return " ".join(solution_parts) + "."
    
    def find_similar_cases(self, problem: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find similar cases for analogical reasoning"""
        # Mock similar cases - in production, would use case database
        similar_cases = []
        
        if "leave" in problem.lower():
            similar_cases.append({
                "case_id": "leave_case_1",
                "problem": "Employee requesting extended leave",
                "solution": "Apply leave policy guidelines",
                "similarity": 0.8
            })
        
        if "policy" in problem.lower():
            similar_cases.append({
                "case_id": "policy_case_1", 
                "problem": "Policy interpretation question",
                "solution": "Refer to policy documentation",
                "similarity": 0.7
            })
        
        return similar_cases
    
    def create_analogy(self, current_problem: str, similar_case: Dict[str, Any]) -> Dict[str, Any]:
        """Create analogy between current problem and similar case"""
        return {
            "source_case": similar_case["case_id"],
            "similarity_score": similar_case["similarity"],
            "mapping": {
                "problem_structure": "similar",
                "solution_approach": "applicable"
            },
            "confidence": similar_case["similarity"]
        }
    
    def apply_analogical_solution(self, analogies: List[Dict[str, Any]], problem: str) -> str:
        """Apply solution based on analogies"""
        if not analogies:
            return "No analogical solution available"
        
        best_analogy = max(analogies, key=lambda x: x["confidence"])
        
        return f"Based on similar case analysis, the solution approach is to apply established procedures with {best_analogy['confidence']:.2f} confidence."
    
    async def generate_traced_reasoning_paths(self, problem: str, context: Dict[str, Any], trace_id: str) -> List[List[ReasoningStep]]:
        """Generate reasoning paths with tracing"""
        self.trace_recorder.record_step(trace_id, {
            "step_type": "path_generation",
            "description": "Generating multiple reasoning paths",
            "confidence": 0.8
        })
        
        return await self.generate_reasoning_paths(problem, context)
    
    def generate_cache_key(self, problem: str, context: Dict[str, Any], reasoning_type: str) -> str:
        """Generate cache key for reasoning result"""
        import hashlib
        
        key_data = f"{problem}_{str(context)}_{reasoning_type}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def update_performance_metrics(self, result: Dict[str, Any], execution_time: float, success: bool):
        """Update performance metrics"""
        self.performance_metrics["total_reasoning_tasks"] += 1
        
        if success:
            self.performance_metrics["successful_tasks"] += 1
            confidence = result.get("confidence", 0.0)
            
            # Update running averages
            total_tasks = self.performance_metrics["total_reasoning_tasks"]
            current_avg_conf = self.performance_metrics["avg_confidence"]
            self.performance_metrics["avg_confidence"] = (current_avg_conf * (total_tasks - 1) + confidence) / total_tasks
        
        # Update execution time average
        total_tasks = self.performance_metrics["total_reasoning_tasks"]
        current_avg_time = self.performance_metrics["avg_execution_time"]
        self.performance_metrics["avg_execution_time"] = (current_avg_time * (total_tasks - 1) + execution_time) / total_tasks
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        success_rate = (self.performance_metrics["successful_tasks"] / 
                       self.performance_metrics["total_reasoning_tasks"]) if self.performance_metrics["total_reasoning_tasks"] > 0 else 0
        
        return {
            **self.performance_metrics,
            "success_rate": success_rate,
            "cache_size": len(self.reasoning_cache),
            "evaluation_trends": self.evaluator.get_evaluation_trends(),
            "trace_analysis": self.trace_recorder.analyze_reasoning_patterns()
        }