from typing import Dict, List, Any, Tuple
import asyncio
from datetime import datetime

class ReasoningEvaluator:
    def __init__(self):
        self.evaluation_criteria = {
            "logical_consistency": 0.25,
            "evidence_support": 0.25,
            "completeness": 0.20,
            "clarity": 0.15,
            "novelty": 0.15
        }
        self.evaluation_history = []
    
    async def evaluate_reasoning_quality(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate overall reasoning quality"""
        evaluation = {
            "logical_consistency": await self.evaluate_logical_consistency(reasoning_result),
            "evidence_support": await self.evaluate_evidence_support(reasoning_result),
            "completeness": await self.evaluate_completeness(reasoning_result),
            "clarity": await self.evaluate_clarity(reasoning_result),
            "novelty": await self.evaluate_novelty(reasoning_result)
        }
        
        # Calculate weighted score
        overall_score = sum(
            evaluation[criterion] * weight 
            for criterion, weight in self.evaluation_criteria.items()
        )
        
        evaluation_result = {
            "overall_score": overall_score,
            "detailed_scores": evaluation,
            "quality_level": self.determine_quality_level(overall_score),
            "recommendations": self.generate_recommendations(evaluation),
            "timestamp": datetime.now().isoformat()
        }
        
        self.evaluation_history.append(evaluation_result)
        return evaluation_result
    
    async def evaluate_logical_consistency(self, reasoning_result: Dict[str, Any]) -> float:
        """Evaluate logical consistency of reasoning"""
        reasoning_paths = reasoning_result.get("reasoning_paths", [])
        if not reasoning_paths:
            return 0.0
        
        consistency_scores = []
        
        for path in reasoning_paths:
            steps = path.get("steps", [])
            if len(steps) < 2:
                consistency_scores.append(0.5)
                continue
            
            # Check step-to-step logical flow
            step_consistency = 0.0
            for i in range(1, len(steps)):
                prev_step = steps[i-1]
                curr_step = steps[i]
                
                # Simple consistency check
                if curr_step.reasoning_type in ["application", "synthesis"] and prev_step.reasoning_type in ["analysis", "comparison"]:
                    step_consistency += 0.3
                
                if curr_step.confidence >= prev_step.confidence * 0.8:
                    step_consistency += 0.2
            
            consistency_scores.append(min(step_consistency, 1.0))
        
        return sum(consistency_scores) / len(consistency_scores)
    
    async def evaluate_evidence_support(self, reasoning_result: Dict[str, Any]) -> float:
        """Evaluate evidence support for reasoning"""
        final_reasoning = reasoning_result.get("final_reasoning", {})
        reasoning_chain = final_reasoning.get("reasoning_chain", [])
        
        if not reasoning_chain:
            return 0.0
        
        evidence_score = 0.0
        
        # Check for evidence-based steps
        for step in reasoning_chain:
            reasoning_text = step.get("reasoning", "").lower()
            
            if any(word in reasoning_text for word in ["based on", "according to", "evidence shows"]):
                evidence_score += 0.3
            
            if any(word in reasoning_text for word in ["data", "information", "knowledge"]):
                evidence_score += 0.2
            
            if step.get("result") and step.get("result") != "None":
                evidence_score += 0.1
        
        return min(evidence_score, 1.0)
    
    async def evaluate_completeness(self, reasoning_result: Dict[str, Any]) -> float:
        """Evaluate completeness of reasoning"""
        reasoning_paths = reasoning_result.get("reasoning_paths", [])
        
        if not reasoning_paths:
            return 0.0
        
        # Check for diverse reasoning approaches
        reasoning_types = set()
        for path in reasoning_paths:
            for step in path.get("steps", []):
                reasoning_types.add(step.reasoning_type)
        
        # Expected reasoning types
        expected_types = {"analysis", "application", "synthesis", "comparison"}
        completeness = len(reasoning_types & expected_types) / len(expected_types)
        
        # Bonus for multiple paths
        path_bonus = min(len(reasoning_paths) / 3.0, 0.3)
        
        return min(completeness + path_bonus, 1.0)
    
    async def evaluate_clarity(self, reasoning_result: Dict[str, Any]) -> float:
        """Evaluate clarity of reasoning"""
        final_reasoning = reasoning_result.get("final_reasoning", {})
        conclusion = final_reasoning.get("conclusion", "")
        
        if not conclusion:
            return 0.0
        
        clarity_score = 0.5  # Base score
        
        # Check for clear structure
        if "based on" in conclusion.lower():
            clarity_score += 0.2
        
        if any(word in conclusion.lower() for word in ["first", "second", "therefore", "conclusion"]):
            clarity_score += 0.2
        
        # Check length appropriateness
        if 50 <= len(conclusion) <= 200:
            clarity_score += 0.1
        
        return min(clarity_score, 1.0)
    
    async def evaluate_novelty(self, reasoning_result: Dict[str, Any]) -> float:
        """Evaluate novelty of reasoning approach"""
        reasoning_paths = reasoning_result.get("reasoning_paths", [])
        
        if not reasoning_paths:
            return 0.0
        
        # Check for diverse approaches
        approach_types = set()
        for path in reasoning_paths:
            path_type = self.classify_reasoning_approach(path)
            approach_types.add(path_type)
        
        # More diverse approaches = higher novelty
        novelty_score = len(approach_types) / 5.0  # Assume max 5 approach types
        
        return min(novelty_score, 1.0)
    
    def classify_reasoning_approach(self, path: Dict[str, Any]) -> str:
        """Classify reasoning approach type"""
        steps = path.get("steps", [])
        if not steps:
            return "unknown"
        
        reasoning_types = [step.reasoning_type for step in steps]
        
        if "comparison" in reasoning_types:
            return "comparative"
        elif "decomposition" in reasoning_types:
            return "systematic"
        elif "pattern_recognition" in reasoning_types:
            return "pattern_based"
        else:
            return "direct"
    
    def determine_quality_level(self, score: float) -> str:
        """Determine quality level from score"""
        if score >= 0.8:
            return "excellent"
        elif score >= 0.6:
            return "good"
        elif score >= 0.4:
            return "fair"
        else:
            return "poor"
    
    def generate_recommendations(self, evaluation: Dict[str, float]) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []
        
        for criterion, score in evaluation.items():
            if score < 0.5:
                if criterion == "logical_consistency":
                    recommendations.append("Improve logical flow between reasoning steps")
                elif criterion == "evidence_support":
                    recommendations.append("Provide more evidence-based reasoning")
                elif criterion == "completeness":
                    recommendations.append("Explore more diverse reasoning approaches")
                elif criterion == "clarity":
                    recommendations.append("Improve clarity and structure of conclusions")
                elif criterion == "novelty":
                    recommendations.append("Consider more creative reasoning approaches")
        
        return recommendations
    
    def get_evaluation_trends(self, limit: int = 10) -> Dict[str, Any]:
        """Get evaluation trends over time"""
        if not self.evaluation_history:
            return {"error": "No evaluation history available"}
        
        recent_evaluations = self.evaluation_history[-limit:]
        
        # Calculate averages
        avg_scores = {}
        for criterion in self.evaluation_criteria.keys():
            scores = [eval_result["detailed_scores"][criterion] for eval_result in recent_evaluations]
            avg_scores[criterion] = sum(scores) / len(scores)
        
        overall_scores = [eval_result["overall_score"] for eval_result in recent_evaluations]
        
        return {
            "evaluation_count": len(recent_evaluations),
            "average_scores": avg_scores,
            "overall_average": sum(overall_scores) / len(overall_scores),
            "trend": "improving" if len(overall_scores) > 1 and overall_scores[-1] > overall_scores[0] else "stable",
            "quality_distribution": self.get_quality_distribution(recent_evaluations)
        }
    
    def get_quality_distribution(self, evaluations: List[Dict[str, Any]]) -> Dict[str, int]:
        """Get distribution of quality levels"""
        distribution = {"excellent": 0, "good": 0, "fair": 0, "poor": 0}
        
        for evaluation in evaluations:
            quality_level = evaluation["quality_level"]
            distribution[quality_level] += 1
        
        return distribution