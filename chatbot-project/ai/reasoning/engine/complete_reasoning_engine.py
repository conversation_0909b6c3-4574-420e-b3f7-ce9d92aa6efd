import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from ai.reasoning.engine.enhanced_reasoning_engine import EnhancedReasoningEngine
from ai.reasoning.tree_of_thoughts.reasoning_tree import ReasoningTree
from ai.reasoning.verification.reasoning_validator import ReasoningValidator
from ai.reasoning.counterfactual.counterfactual_engine import CounterfactualEngine
from typing import Dict, List, Any
import asyncio

class CompleteReasoningEngine(EnhancedReasoningEngine):
    def __init__(self):
        super().__init__()
        self.tree_engine = None
        self.validator = ReasoningValidator()
        self.counterfactual_engine = CounterfactualEngine()
        
    async def complete_reasoning_pipeline(self, problem: str, context: Dict[str, Any], 
                                        reasoning_type: str = "complete") -> Dict[str, Any]:
        """Complete reasoning pipeline with all components"""
        pipeline_result = {
            "problem": problem,
            "reasoning_type": reasoning_type,
            "pipeline_stages": {}
        }
        
        try:
            # Stage 1: Tree of Thoughts Construction
            if reasoning_type in ["complete", "tree_of_thoughts"]:
                tree_result = await self.tree_of_thoughts_with_construction(problem, context)
                pipeline_result["pipeline_stages"]["tree_construction"] = tree_result
            
            # Stage 2: Enhanced Reasoning
            enhanced_result = await self.enhanced_reasoning(problem, context, "tree_of_thoughts")
            pipeline_result["pipeline_stages"]["enhanced_reasoning"] = enhanced_result
            
            # Stage 3: Reasoning Validation
            validation_result = await self.validator.validate_reasoning(enhanced_result)
            pipeline_result["pipeline_stages"]["validation"] = validation_result
            
            # Stage 4: Counterfactual Analysis
            if reasoning_type in ["complete", "counterfactual"]:
                counterfactual_result = await self.counterfactual_engine.generate_counterfactuals(problem, context)
                pipeline_result["pipeline_stages"]["counterfactual"] = counterfactual_result
            
            # Stage 5: Final Integration
            final_result = await self.integrate_pipeline_results(pipeline_result)
            pipeline_result["final_result"] = final_result
            
            return pipeline_result
            
        except Exception as e:
            return {
                "error": str(e),
                "problem": problem,
                "reasoning_type": reasoning_type,
                "pipeline_stages": pipeline_result.get("pipeline_stages", {})
            }
    
    async def tree_of_thoughts_with_construction(self, problem: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Tree of Thoughts with explicit tree construction"""
        # Initialize reasoning tree
        self.tree_engine = ReasoningTree(problem, max_depth=4, beam_width=3)
        
        # Construct tree
        construction_result = await self.tree_engine.construct_tree(context)
        
        # Get best path
        best_path = self.tree_engine.get_best_path()
        best_path_content = self.tree_engine.get_path_content(best_path) if best_path else []
        
        # Merge multiple paths
        all_paths = self.tree_engine.paths[:3]  # Top 3 paths
        merged_result = self.tree_engine.merge_paths(all_paths)
        
        return {
            "construction_result": construction_result,
            "best_path": {
                "path_nodes": best_path,
                "path_content": best_path_content,
                "path_length": len(best_path) if best_path else 0
            },
            "path_merging": merged_result,
            "tree_statistics": {
                "total_nodes": len(self.tree_engine.nodes),
                "total_paths": len(self.tree_engine.paths),
                "max_depth": max(node.depth for node in self.tree_engine.nodes.values()) if self.tree_engine.nodes else 0
            }
        }
    
    async def multi_path_exploration(self, problem: str, context: Dict[str, Any], num_paths: int = 5) -> Dict[str, Any]:
        """Implement multi-path exploration with beam search"""
        if not self.tree_engine:
            self.tree_engine = ReasoningTree(problem, max_depth=4, beam_width=num_paths)
            await self.tree_engine.construct_tree(context)
        
        # Get multiple paths
        all_paths = self.tree_engine.paths
        
        # Evaluate paths in parallel
        path_evaluations = []
        for i, path in enumerate(all_paths[:num_paths]):
            path_content = self.tree_engine.get_path_content(path)
            evaluation = await self.evaluate_reasoning_path_content(path_content)
            
            path_evaluations.append({
                "path_id": i,
                "path_nodes": path,
                "path_content": path_content,
                "evaluation": evaluation,
                "path_score": evaluation.get("confidence", 0.5)
            })
        
        # Sort by score
        path_evaluations.sort(key=lambda x: x["path_score"], reverse=True)
        
        # Merge top paths
        top_paths = [pe["path_nodes"] for pe in path_evaluations[:3]]
        merged_paths = self.tree_engine.merge_paths(top_paths)
        
        return {
            "total_paths_explored": len(all_paths),
            "paths_evaluated": len(path_evaluations),
            "path_evaluations": path_evaluations,
            "merged_paths": merged_paths,
            "exploration_summary": {
                "best_path_score": path_evaluations[0]["path_score"] if path_evaluations else 0,
                "average_path_score": sum(pe["path_score"] for pe in path_evaluations) / len(path_evaluations) if path_evaluations else 0,
                "path_diversity": len(set(tuple(pe["path_nodes"]) for pe in path_evaluations))
            }
        }
    
    async def evaluate_reasoning_path_content(self, path_content: List[str]) -> Dict[str, Any]:
        """Evaluate reasoning path content"""
        if not path_content:
            return {"confidence": 0.0, "quality": "poor"}
        
        # Simple evaluation based on content
        score = 0.5  # Base score
        
        # Check for reasoning indicators
        content_text = " ".join(path_content).lower()
        if "analysis" in content_text:
            score += 0.2
        if "because" in content_text or "therefore" in content_text:
            score += 0.2
        if "policy" in content_text:
            score += 0.1
        
        return {
            "confidence": min(score, 1.0),
            "quality": "good" if score > 0.7 else "fair" if score > 0.5 else "poor",
            "content_length": len(path_content),
            "reasoning_indicators": sum(1 for indicator in ["analysis", "because", "therefore"] if indicator in content_text)
        }
    
    async def integrate_pipeline_results(self, pipeline_result: Dict[str, Any]) -> Dict[str, Any]:
        """Integrate results from all pipeline stages"""
        stages = pipeline_result.get("pipeline_stages", {})
        
        # Extract key information from each stage
        integration = {
            "final_confidence": 0.5,
            "reasoning_quality": "unknown",
            "validation_status": "unknown",
            "counterfactual_insights": [],
            "integrated_conclusion": "",
            "pipeline_success": True
        }
        
        # From enhanced reasoning
        if "enhanced_reasoning" in stages:
            enhanced = stages["enhanced_reasoning"]
            integration["final_confidence"] = enhanced.get("confidence", 0.5)
            
            if "final_reasoning" in enhanced:
                final_reasoning = enhanced["final_reasoning"]
                integration["integrated_conclusion"] = final_reasoning.get("conclusion", "")
        
        # From validation
        if "validation" in stages:
            validation = stages["validation"]
            integration["validation_status"] = "passed" if validation.get("overall_validity", 0) > 0.6 else "failed"
            integration["reasoning_quality"] = validation.get("overall_validity", 0.5)
            
            # Adjust confidence based on validation
            confidence_adjustment = validation.get("confidence_adjustment", 0.0)
            integration["final_confidence"] = max(0.0, min(1.0, integration["final_confidence"] + confidence_adjustment))
        
        # From counterfactual analysis
        if "counterfactual" in stages:
            counterfactual = stages["counterfactual"]
            integration["counterfactual_insights"] = counterfactual.get("counterfactual_insights", [])
        
        # From tree construction
        if "tree_construction" in stages:
            tree_result = stages["tree_construction"]
            tree_stats = tree_result.get("tree_statistics", {})
            
            # Boost confidence if tree exploration was comprehensive
            if tree_stats.get("total_paths", 0) > 2:
                integration["final_confidence"] = min(1.0, integration["final_confidence"] + 0.1)
        
        # Generate integrated conclusion
        if not integration["integrated_conclusion"]:
            integration["integrated_conclusion"] = self.generate_integrated_conclusion(stages)
        
        return integration
    
    def generate_integrated_conclusion(self, stages: Dict[str, Any]) -> str:
        """Generate integrated conclusion from all stages"""
        conclusion_parts = []
        
        # From enhanced reasoning
        if "enhanced_reasoning" in stages:
            enhanced = stages["enhanced_reasoning"]
            if "final_reasoning" in enhanced and "conclusion" in enhanced["final_reasoning"]:
                conclusion_parts.append(enhanced["final_reasoning"]["conclusion"])
        
        # From validation insights
        if "validation" in stages:
            validation = stages["validation"]
            if validation.get("overall_validity", 0) > 0.7:
                conclusion_parts.append("The reasoning has been validated as logically sound.")
            elif validation.get("overall_validity", 0) < 0.5:
                conclusion_parts.append("The reasoning requires further validation.")
        
        # From counterfactual insights
        if "counterfactual" in stages:
            counterfactual = stages["counterfactual"]
            insights = counterfactual.get("counterfactual_insights", [])
            if insights:
                conclusion_parts.append(f"Alternative scenario analysis reveals: {insights[0]}")
        
        return " ".join(conclusion_parts) if conclusion_parts else "Comprehensive reasoning analysis completed."
    
    async def beam_search_reasoning(self, problem: str, context: Dict[str, Any], beam_width: int = 3) -> Dict[str, Any]:
        """Implement beam search for reasoning paths"""
        tree_engine = ReasoningTree(problem, max_depth=5, beam_width=beam_width)
        construction_result = await tree_engine.construct_tree(context)
        
        # Get all paths and their scores
        paths_with_scores = []
        for path in tree_engine.paths:
            path_content = tree_engine.get_path_content(path)
            evaluation = await self.evaluate_reasoning_path_content(path_content)
            
            paths_with_scores.append({
                "path": path,
                "content": path_content,
                "score": evaluation["confidence"],
                "evaluation": evaluation
            })
        
        # Sort by score (beam search keeps top-k)
        paths_with_scores.sort(key=lambda x: x["score"], reverse=True)
        top_paths = paths_with_scores[:beam_width]
        
        return {
            "beam_width": beam_width,
            "total_paths_generated": len(tree_engine.paths),
            "top_paths": top_paths,
            "best_path_score": top_paths[0]["score"] if top_paths else 0,
            "beam_search_effectiveness": len(top_paths) / max(len(tree_engine.paths), 1),
            "construction_stats": construction_result
        }
    
    def get_complete_performance_summary(self) -> Dict[str, Any]:
        """Get complete performance summary including all components"""
        base_summary = self.get_performance_summary()
        
        # Add tree construction stats
        tree_stats = {}
        if self.tree_engine:
            tree_stats = {
                "tree_nodes_created": len(self.tree_engine.nodes),
                "tree_paths_explored": len(self.tree_engine.paths),
                "tree_max_depth": max(node.depth for node in self.tree_engine.nodes.values()) if self.tree_engine.nodes else 0
            }
        
        # Add validation stats
        validation_stats = {
            "validation_checks_performed": len(self.evaluator.evaluation_history),
            "average_validation_score": sum(eval_result["overall_score"] for eval_result in self.evaluator.evaluation_history) / len(self.evaluator.evaluation_history) if self.evaluator.evaluation_history else 0
        }
        
        return {
            **base_summary,
            "tree_construction_stats": tree_stats,
            "validation_stats": validation_stats,
            "complete_pipeline_ready": True
        }