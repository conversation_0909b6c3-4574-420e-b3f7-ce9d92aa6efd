import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict

@dataclass
class ReasoningTrace:
    trace_id: str
    problem: str
    reasoning_type: str
    steps: List[Dict[str, Any]]
    final_result: Dict[str, Any]
    confidence: float
    execution_time: float
    timestamp: str
    metadata: Optional[Dict[str, Any]] = None

class ReasoningTraceRecorder:
    def __init__(self, storage_path: str = "./reasoning_traces"):
        self.storage_path = storage_path
        self.active_traces = {}
        self.trace_history = []
        
        # Create storage directory
        import os
        os.makedirs(storage_path, exist_ok=True)
    
    def start_trace(self, problem: str, reasoning_type: str, metadata: Dict[str, Any] = None) -> str:
        """Start recording a reasoning trace"""
        trace_id = str(uuid.uuid4())
        
        trace_data = {
            "trace_id": trace_id,
            "problem": problem,
            "reasoning_type": reasoning_type,
            "steps": [],
            "start_time": datetime.now(),
            "metadata": metadata or {}
        }
        
        self.active_traces[trace_id] = trace_data
        return trace_id
    
    def record_step(self, trace_id: str, step_data: Dict[str, Any]):
        """Record a reasoning step"""
        if trace_id not in self.active_traces:
            return False
        
        step_record = {
            "step_id": len(self.active_traces[trace_id]["steps"]) + 1,
            "timestamp": datetime.now().isoformat(),
            "step_type": step_data.get("step_type", "unknown"),
            "description": step_data.get("description", ""),
            "input_data": step_data.get("input_data"),
            "output_data": step_data.get("output_data"),
            "confidence": step_data.get("confidence", 0.5),
            "reasoning_method": step_data.get("reasoning_method", ""),
            "execution_time": step_data.get("execution_time", 0.0)
        }
        
        self.active_traces[trace_id]["steps"].append(step_record)
        return True
    
    def record_decision_point(self, trace_id: str, decision_data: Dict[str, Any]):
        """Record a decision point in reasoning"""
        if trace_id not in self.active_traces:
            return False
        
        decision_record = {
            "decision_id": str(uuid.uuid4()),
            "timestamp": datetime.now().isoformat(),
            "decision_type": decision_data.get("decision_type", "choice"),
            "options": decision_data.get("options", []),
            "selected_option": decision_data.get("selected_option"),
            "selection_criteria": decision_data.get("selection_criteria", ""),
            "confidence": decision_data.get("confidence", 0.5),
            "reasoning": decision_data.get("reasoning", "")
        }
        
        # Add decision to current step or create new step
        current_steps = self.active_traces[trace_id]["steps"]
        if current_steps:
            if "decisions" not in current_steps[-1]:
                current_steps[-1]["decisions"] = []
            current_steps[-1]["decisions"].append(decision_record)
        else:
            # Create a decision step
            self.record_step(trace_id, {
                "step_type": "decision",
                "description": "Decision point",
                "decisions": [decision_record]
            })
        
        return True
    
    def record_backtrack(self, trace_id: str, backtrack_data: Dict[str, Any]):
        """Record backtracking in reasoning"""
        if trace_id not in self.active_traces:
            return False
        
        backtrack_record = {
            "backtrack_id": str(uuid.uuid4()),
            "timestamp": datetime.now().isoformat(),
            "from_step": backtrack_data.get("from_step"),
            "to_step": backtrack_data.get("to_step"),
            "reason": backtrack_data.get("reason", ""),
            "alternative_explored": backtrack_data.get("alternative_explored", False)
        }
        
        # Add to trace metadata
        if "backtracks" not in self.active_traces[trace_id]["metadata"]:
            self.active_traces[trace_id]["metadata"]["backtracks"] = []
        
        self.active_traces[trace_id]["metadata"]["backtracks"].append(backtrack_record)
        return True
    
    def complete_trace(self, trace_id: str, final_result: Dict[str, Any], confidence: float) -> ReasoningTrace:
        """Complete and finalize a reasoning trace"""
        if trace_id not in self.active_traces:
            return None
        
        trace_data = self.active_traces[trace_id]
        end_time = datetime.now()
        execution_time = (end_time - trace_data["start_time"]).total_seconds()
        
        # Create final trace record
        reasoning_trace = ReasoningTrace(
            trace_id=trace_id,
            problem=trace_data["problem"],
            reasoning_type=trace_data["reasoning_type"],
            steps=trace_data["steps"],
            final_result=final_result,
            confidence=confidence,
            execution_time=execution_time,
            timestamp=end_time.isoformat(),
            metadata=trace_data["metadata"]
        )
        
        # Store trace
        self.trace_history.append(reasoning_trace)
        self.save_trace_to_file(reasoning_trace)
        
        # Clean up active trace
        del self.active_traces[trace_id]
        
        return reasoning_trace
    
    def save_trace_to_file(self, trace: ReasoningTrace):
        """Save trace to file"""
        filename = f"trace_{trace.trace_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = f"{self.storage_path}/{filename}"
        
        try:
            with open(filepath, 'w') as f:
                json.dump(asdict(trace), f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving trace: {e}")
    
    def get_trace(self, trace_id: str) -> Optional[ReasoningTrace]:
        """Get a specific trace"""
        for trace in self.trace_history:
            if trace.trace_id == trace_id:
                return trace
        return None
    
    def search_traces(self, criteria: Dict[str, Any]) -> List[ReasoningTrace]:
        """Search traces by criteria"""
        matching_traces = []
        
        for trace in self.trace_history:
            match = True
            
            if "reasoning_type" in criteria:
                if trace.reasoning_type != criteria["reasoning_type"]:
                    match = False
            
            if "min_confidence" in criteria:
                if trace.confidence < criteria["min_confidence"]:
                    match = False
            
            if "problem_contains" in criteria:
                if criteria["problem_contains"].lower() not in trace.problem.lower():
                    match = False
            
            if "date_from" in criteria:
                trace_date = datetime.fromisoformat(trace.timestamp)
                if trace_date < datetime.fromisoformat(criteria["date_from"]):
                    match = False
            
            if match:
                matching_traces.append(trace)
        
        return matching_traces
    
    def analyze_reasoning_patterns(self) -> Dict[str, Any]:
        """Analyze patterns in reasoning traces"""
        if not self.trace_history:
            return {"error": "No traces available for analysis"}
        
        analysis = {
            "total_traces": len(self.trace_history),
            "reasoning_types": {},
            "avg_execution_time": 0.0,
            "avg_confidence": 0.0,
            "avg_steps_per_trace": 0.0,
            "common_step_types": {},
            "success_patterns": []
        }
        
        # Analyze reasoning types
        for trace in self.trace_history:
            reasoning_type = trace.reasoning_type
            if reasoning_type not in analysis["reasoning_types"]:
                analysis["reasoning_types"][reasoning_type] = {
                    "count": 0,
                    "avg_confidence": 0.0,
                    "avg_execution_time": 0.0
                }
            
            analysis["reasoning_types"][reasoning_type]["count"] += 1
            analysis["reasoning_types"][reasoning_type]["avg_confidence"] += trace.confidence
            analysis["reasoning_types"][reasoning_type]["avg_execution_time"] += trace.execution_time
        
        # Calculate averages
        for reasoning_type, data in analysis["reasoning_types"].items():
            count = data["count"]
            data["avg_confidence"] /= count
            data["avg_execution_time"] /= count
        
        # Overall averages
        analysis["avg_execution_time"] = sum(t.execution_time for t in self.trace_history) / len(self.trace_history)
        analysis["avg_confidence"] = sum(t.confidence for t in self.trace_history) / len(self.trace_history)
        analysis["avg_steps_per_trace"] = sum(len(t.steps) for t in self.trace_history) / len(self.trace_history)
        
        # Analyze step types
        for trace in self.trace_history:
            for step in trace.steps:
                step_type = step.get("step_type", "unknown")
                analysis["common_step_types"][step_type] = analysis["common_step_types"].get(step_type, 0) + 1
        
        # Identify success patterns
        high_confidence_traces = [t for t in self.trace_history if t.confidence > 0.8]
        if high_confidence_traces:
            analysis["success_patterns"] = self.identify_success_patterns(high_confidence_traces)
        
        return analysis
    
    def identify_success_patterns(self, successful_traces: List[ReasoningTrace]) -> List[Dict[str, Any]]:
        """Identify patterns in successful reasoning traces"""
        patterns = []
        
        # Pattern 1: Common step sequences
        step_sequences = []
        for trace in successful_traces:
            sequence = [step.get("step_type", "unknown") for step in trace.steps]
            step_sequences.append(sequence)
        
        # Find most common sequence
        from collections import Counter
        sequence_counts = Counter(tuple(seq) for seq in step_sequences)
        
        if sequence_counts:
            most_common_sequence = sequence_counts.most_common(1)[0]
            patterns.append({
                "pattern_type": "common_step_sequence",
                "sequence": list(most_common_sequence[0]),
                "frequency": most_common_sequence[1],
                "success_rate": most_common_sequence[1] / len(successful_traces)
            })
        
        # Pattern 2: Optimal step count
        step_counts = [len(trace.steps) for trace in successful_traces]
        avg_steps = sum(step_counts) / len(step_counts)
        
        patterns.append({
            "pattern_type": "optimal_step_count",
            "avg_steps": avg_steps,
            "range": [min(step_counts), max(step_counts)]
        })
        
        return patterns
    
    def generate_trace_report(self, trace_id: str) -> Dict[str, Any]:
        """Generate detailed report for a trace"""
        trace = self.get_trace(trace_id)
        if not trace:
            return {"error": "Trace not found"}
        
        report = {
            "trace_summary": {
                "trace_id": trace.trace_id,
                "problem": trace.problem,
                "reasoning_type": trace.reasoning_type,
                "confidence": trace.confidence,
                "execution_time": trace.execution_time,
                "timestamp": trace.timestamp
            },
            "step_analysis": {
                "total_steps": len(trace.steps),
                "step_types": list(set(step.get("step_type", "unknown") for step in trace.steps)),
                "confidence_progression": [step.get("confidence", 0.5) for step in trace.steps],
                "execution_times": [step.get("execution_time", 0.0) for step in trace.steps]
            },
            "decision_analysis": {
                "decision_points": 0,
                "backtrack_count": 0
            },
            "performance_metrics": {
                "avg_step_confidence": sum(step.get("confidence", 0.5) for step in trace.steps) / len(trace.steps) if trace.steps else 0,
                "total_execution_time": trace.execution_time,
                "steps_per_second": len(trace.steps) / trace.execution_time if trace.execution_time > 0 else 0
            }
        }
        
        # Count decisions and backtracks
        for step in trace.steps:
            if "decisions" in step:
                report["decision_analysis"]["decision_points"] += len(step["decisions"])
        
        if trace.metadata and "backtracks" in trace.metadata:
            report["decision_analysis"]["backtrack_count"] = len(trace.metadata["backtracks"])
        
        return report
    
    def export_traces(self, filepath: str, criteria: Dict[str, Any] = None):
        """Export traces to file"""
        traces_to_export = self.trace_history
        
        if criteria:
            traces_to_export = self.search_traces(criteria)
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "trace_count": len(traces_to_export),
            "traces": [asdict(trace) for trace in traces_to_export]
        }
        
        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        print(f"Exported {len(traces_to_export)} traces to {filepath}")