"""Core reasoning engine for CHaBot."""

from typing import Dict, List, Any, Optional, Tu<PERSON>
from dataclasses import dataclass
from enum import Enum
import json

class ReasoningStep(Enum):
    ANALYZE = "analyze"
    INFER = "infer"
    VALIDATE = "validate"
    CONCLUDE = "conclude"

@dataclass
class ReasoningNode:
    id: str
    step_type: ReasoningStep
    content: str
    confidence: float
    evidence: List[str]
    children: List['ReasoningNode']
    parent: Optional['ReasoningNode'] = None

class ReasoningEngine:
    def __init__(self):
        self.reasoning_chains = {}
        self.rules = self._load_reasoning_rules()
    
    def _load_reasoning_rules(self) -> Dict[str, Any]:
        """Load reasoning rules."""
        return {
            "hr_policy": {
                "steps": [ReasoningStep.ANALYZE, ReasoningStep.INFER, ReasoningStep.VALIDATE, ReasoningStep.CONCLUDE],
                "confidence_threshold": 0.7,
                "evidence_required": 2
            },
            "leave_request": {
                "steps": [ReasoningStep.ANALYZE, ReasoningStep.VALIDATE, ReasoningStep.CONCLUDE],
                "confidence_threshold": 0.8,
                "evidence_required": 1
            },
            "benefits": {
                "steps": [ReasoningStep.ANALYZE, ReasoningStep.INFER, ReasoningStep.CONCLUDE],
                "confidence_threshold": 0.75,
                "evidence_required": 2
            }
        }
    
    def create_reasoning_chain(self, query: str, context: Dict[str, Any]) -> str:
        """Create a new reasoning chain."""
        chain_id = f"chain_{len(self.reasoning_chains)}"
        
        # Determine reasoning type
        reasoning_type = self._determine_reasoning_type(query, context)
        
        # Create root node
        root_node = ReasoningNode(
            id=f"{chain_id}_root",
            step_type=ReasoningStep.ANALYZE,
            content=f"Analyzing query: {query}",
            confidence=0.9,
            evidence=[query],
            children=[]
        )
        
        self.reasoning_chains[chain_id] = {
            "root": root_node,
            "type": reasoning_type,
            "query": query,
            "context": context,
            "current_node": root_node,
            "completed": False
        }
        
        return chain_id
    
    def _determine_reasoning_type(self, query: str, context: Dict[str, Any]) -> str:
        """Determine the type of reasoning needed."""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["policy", "rule", "guideline"]):
            return "hr_policy"
        elif any(word in query_lower for word in ["leave", "vacation", "time off"]):
            return "leave_request"
        elif any(word in query_lower for word in ["benefit", "insurance", "health"]):
            return "benefits"
        else:
            return "hr_policy"  # Default
    
    def add_reasoning_step(self, chain_id: str, step_type: ReasoningStep, 
                          content: str, evidence: List[str], confidence: float) -> str:
        """Add a reasoning step to the chain."""
        if chain_id not in self.reasoning_chains:
            raise ValueError(f"Chain {chain_id} not found")
        
        chain = self.reasoning_chains[chain_id]
        current_node = chain["current_node"]
        
        # Create new node
        new_node = ReasoningNode(
            id=f"{chain_id}_{step_type.value}_{len(current_node.children)}",
            step_type=step_type,
            content=content,
            confidence=confidence,
            evidence=evidence,
            children=[],
            parent=current_node
        )
        
        # Add to current node's children
        current_node.children.append(new_node)
        
        # Update current node
        chain["current_node"] = new_node
        
        return new_node.id
    
    def validate_reasoning_step(self, chain_id: str, node_id: str) -> Tuple[bool, str]:
        """Validate a reasoning step."""
        chain = self.reasoning_chains.get(chain_id)
        if not chain:
            return False, "Chain not found"
        
        node = self._find_node(chain["root"], node_id)
        if not node:
            return False, "Node not found"
        
        reasoning_type = chain["type"]
        rules = self.rules.get(reasoning_type, self.rules["hr_policy"])
        
        # Check confidence threshold
        if node.confidence < rules["confidence_threshold"]:
            return False, f"Confidence {node.confidence} below threshold {rules['confidence_threshold']}"
        
        # Check evidence requirement
        if len(node.evidence) < rules["evidence_required"]:
            return False, f"Insufficient evidence: {len(node.evidence)} < {rules['evidence_required']}"
        
        return True, "Valid"
    
    def _find_node(self, root: ReasoningNode, node_id: str) -> Optional[ReasoningNode]:
        """Find a node in the reasoning tree."""
        if root.id == node_id:
            return root
        
        for child in root.children:
            result = self._find_node(child, node_id)
            if result:
                return result
        
        return None
    
    def complete_reasoning_chain(self, chain_id: str) -> Dict[str, Any]:
        """Complete and finalize a reasoning chain."""
        if chain_id not in self.reasoning_chains:
            raise ValueError(f"Chain {chain_id} not found")
        
        chain = self.reasoning_chains[chain_id]
        
        # Validate the entire chain
        validation_results = self._validate_chain(chain)
        
        # Generate conclusion
        conclusion = self._generate_conclusion(chain)
        
        # Mark as completed
        chain["completed"] = True
        chain["validation"] = validation_results
        chain["conclusion"] = conclusion
        
        return {
            "chain_id": chain_id,
            "conclusion": conclusion,
            "confidence": conclusion.get("confidence", 0.0),
            "validation": validation_results,
            "reasoning_trace": self._export_reasoning_trace(chain["root"])
        }
    
    def _validate_chain(self, chain: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the entire reasoning chain."""
        reasoning_type = chain["type"]
        rules = self.rules.get(reasoning_type, self.rules["hr_policy"])
        
        # Check if all required steps are present
        required_steps = rules["steps"]
        present_steps = self._get_steps_in_chain(chain["root"])
        
        missing_steps = [step for step in required_steps if step not in present_steps]
        
        return {
            "valid": len(missing_steps) == 0,
            "missing_steps": [step.value for step in missing_steps],
            "confidence_scores": self._get_confidence_scores(chain["root"]),
            "evidence_count": self._count_evidence(chain["root"])
        }
    
    def _get_steps_in_chain(self, node: ReasoningNode) -> List[ReasoningStep]:
        """Get all step types in the reasoning chain."""
        steps = [node.step_type]
        for child in node.children:
            steps.extend(self._get_steps_in_chain(child))
        return list(set(steps))
    
    def _get_confidence_scores(self, node: ReasoningNode) -> List[float]:
        """Get all confidence scores in the chain."""
        scores = [node.confidence]
        for child in node.children:
            scores.extend(self._get_confidence_scores(child))
        return scores
    
    def _count_evidence(self, node: ReasoningNode) -> int:
        """Count total evidence in the chain."""
        count = len(node.evidence)
        for child in node.children:
            count += self._count_evidence(child)
        return count
    
    def _generate_conclusion(self, chain: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a conclusion from the reasoning chain."""
        root = chain["root"]
        
        # Find conclusion nodes
        conclusion_nodes = self._find_nodes_by_type(root, ReasoningStep.CONCLUDE)
        
        if not conclusion_nodes:
            # Generate a conclusion based on available evidence
            all_evidence = self._collect_all_evidence(root)
            confidence_scores = self._get_confidence_scores(root)
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
            
            return {
                "content": "Based on the available evidence and analysis, here is the conclusion.",
                "confidence": avg_confidence,
                "evidence": all_evidence,
                "reasoning_path": self._get_reasoning_path(root)
            }
        
        # Use the highest confidence conclusion
        best_conclusion = max(conclusion_nodes, key=lambda x: x.confidence)
        
        return {
            "content": best_conclusion.content,
            "confidence": best_conclusion.confidence,
            "evidence": best_conclusion.evidence,
            "reasoning_path": self._get_reasoning_path(root)
        }
    
    def _find_nodes_by_type(self, node: ReasoningNode, step_type: ReasoningStep) -> List[ReasoningNode]:
        """Find all nodes of a specific type."""
        nodes = []
        if node.step_type == step_type:
            nodes.append(node)
        
        for child in node.children:
            nodes.extend(self._find_nodes_by_type(child, step_type))
        
        return nodes
    
    def _collect_all_evidence(self, node: ReasoningNode) -> List[str]:
        """Collect all evidence from the reasoning chain."""
        evidence = node.evidence.copy()
        for child in node.children:
            evidence.extend(self._collect_all_evidence(child))
        return list(set(evidence))  # Remove duplicates
    
    def _get_reasoning_path(self, node: ReasoningNode) -> List[str]:
        """Get the reasoning path as a list of steps."""
        path = [f"{node.step_type.value}: {node.content}"]
        for child in node.children:
            child_paths = self._get_reasoning_path(child)
            path.extend([f"  -> {step}" for step in child_paths])
        return path
    
    def _export_reasoning_trace(self, node: ReasoningNode) -> Dict[str, Any]:
        """Export reasoning trace for debugging/explanation."""
        return {
            "id": node.id,
            "step_type": node.step_type.value,
            "content": node.content,
            "confidence": node.confidence,
            "evidence": node.evidence,
            "children": [self._export_reasoning_trace(child) for child in node.children]
        }
    
    def get_reasoning_explanation(self, chain_id: str) -> str:
        """Get a human-readable explanation of the reasoning."""
        if chain_id not in self.reasoning_chains:
            return "Reasoning chain not found."
        
        chain = self.reasoning_chains[chain_id]
        if not chain.get("completed"):
            return "Reasoning chain not completed yet."
        
        conclusion = chain.get("conclusion", {})
        reasoning_path = conclusion.get("reasoning_path", [])
        
        explanation = f"Here's how I reasoned through your question:\n\n"
        for i, step in enumerate(reasoning_path, 1):
            explanation += f"{i}. {step}\n"
        
        explanation += f"\nConclusion: {conclusion.get('content', 'No conclusion available')}\n"
        explanation += f"Confidence: {conclusion.get('confidence', 0.0):.2f}"
        
        return explanation