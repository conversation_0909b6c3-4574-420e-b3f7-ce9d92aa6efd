from typing import Dict, List, Any, Tuple
import time
import asyncio
from dataclasses import dataclass
from collections import defaultdict
import threading

@dataclass
class CacheEntry:
    key: str
    value: Any
    timestamp: float
    access_count: int
    ttl: float

class ReasoningCache:
    def __init__(self, max_size: int = 1000, default_ttl: float = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = {}
        self.access_times = {}
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Any:
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                if time.time() - entry.timestamp < entry.ttl:
                    entry.access_count += 1
                    self.access_times[key] = time.time()
                    return entry.value
                else:
                    del self.cache[key]
                    if key in self.access_times:
                        del self.access_times[key]
            return None
    
    def set(self, key: str, value: Any, ttl: float = None):
        with self.lock:
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            ttl = ttl or self.default_ttl
            self.cache[key] = CacheEntry(
                key=key,
                value=value,
                timestamp=time.time(),
                access_count=1,
                ttl=ttl
            )
            self.access_times[key] = time.time()
    
    def _evict_lru(self):
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.items(), key=lambda x: x[1])[0]
        del self.cache[lru_key]
        del self.access_times[lru_key]

class PerformanceOptimizer:
    def __init__(self):
        self.cache = ReasoningCache()
        self.execution_metrics = defaultdict(list)
        self.optimization_strategies = {}
        self.performance_history = []
        
    async def optimize_reasoning_execution(self, reasoning_func, *args, **kwargs) -> Dict[str, Any]:
        """Optimize reasoning execution with caching and monitoring"""
        # Generate cache key
        cache_key = self.generate_cache_key(reasoning_func.__name__, args, kwargs)
        
        # Check cache first
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return {
                "result": cached_result,
                "execution_time": 0.0,
                "cache_hit": True,
                "optimization_applied": "cache"
            }
        
        # Execute with monitoring
        start_time = time.time()
        result = await reasoning_func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        # Cache result if beneficial
        if self.should_cache_result(reasoning_func.__name__, execution_time, result):
            self.cache.set(cache_key, result)
        
        # Record metrics
        self.record_execution_metrics(reasoning_func.__name__, execution_time, result)
        
        return {
            "result": result,
            "execution_time": execution_time,
            "cache_hit": False,
            "optimization_applied": "none"
        }
    
    def generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """Generate cache key from function parameters"""
        import hashlib
        
        # Create deterministic string from parameters
        key_parts = [func_name]
        
        # Add args
        for arg in args:
            if isinstance(arg, (str, int, float, bool)):
                key_parts.append(str(arg))
            elif isinstance(arg, dict):
                key_parts.append(str(sorted(arg.items())))
            else:
                key_parts.append(str(type(arg).__name__))
        
        # Add kwargs
        for k, v in sorted(kwargs.items()):
            if isinstance(v, (str, int, float, bool)):
                key_parts.append(f"{k}:{v}")
            else:
                key_parts.append(f"{k}:{type(v).__name__}")
        
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def should_cache_result(self, func_name: str, execution_time: float, result: Any) -> bool:
        """Determine if result should be cached"""
        # Cache if execution time is significant
        if execution_time > 1.0:
            return True
        
        # Cache if result is complex
        if isinstance(result, dict) and len(str(result)) > 1000:
            return True
        
        # Cache based on function type
        if "reasoning" in func_name.lower() or "analysis" in func_name.lower():
            return True
        
        return False
    
    def record_execution_metrics(self, func_name: str, execution_time: float, result: Any):
        """Record execution metrics for analysis"""
        metrics = {
            "timestamp": time.time(),
            "execution_time": execution_time,
            "result_size": len(str(result)) if result else 0,
            "success": result is not None
        }
        
        self.execution_metrics[func_name].append(metrics)
        
        # Keep only recent metrics
        if len(self.execution_metrics[func_name]) > 100:
            self.execution_metrics[func_name] = self.execution_metrics[func_name][-100:]
    
    def analyze_performance_bottlenecks(self) -> Dict[str, Any]:
        """Analyze performance bottlenecks"""
        analysis = {
            "slow_functions": [],
            "cache_efficiency": {},
            "optimization_opportunities": [],
            "performance_trends": {}
        }
        
        # Analyze slow functions
        for func_name, metrics in self.execution_metrics.items():
            if not metrics:
                continue
            
            avg_time = sum(m["execution_time"] for m in metrics) / len(metrics)
            max_time = max(m["execution_time"] for m in metrics)
            
            if avg_time > 2.0 or max_time > 5.0:
                analysis["slow_functions"].append({
                    "function": func_name,
                    "avg_execution_time": avg_time,
                    "max_execution_time": max_time,
                    "call_count": len(metrics)
                })
        
        # Analyze cache efficiency
        cache_stats = self.get_cache_statistics()
        analysis["cache_efficiency"] = cache_stats
        
        # Identify optimization opportunities
        analysis["optimization_opportunities"] = self.identify_optimization_opportunities()
        
        return analysis
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        total_entries = len(self.cache.cache)
        
        if total_entries == 0:
            return {"cache_size": 0, "utilization": 0.0}
        
        # Calculate cache utilization
        utilization = total_entries / self.cache.max_size
        
        # Calculate average access count
        avg_access_count = sum(entry.access_count for entry in self.cache.cache.values()) / total_entries
        
        return {
            "cache_size": total_entries,
            "max_size": self.cache.max_size,
            "utilization": utilization,
            "avg_access_count": avg_access_count,
            "hit_rate_estimate": min(avg_access_count / 10.0, 1.0)  # Rough estimate
        }
    
    def identify_optimization_opportunities(self) -> List[Dict[str, Any]]:
        """Identify optimization opportunities"""
        opportunities = []
        
        # Check for frequently called slow functions
        for func_name, metrics in self.execution_metrics.items():
            if len(metrics) > 10:  # Frequently called
                avg_time = sum(m["execution_time"] for m in metrics) / len(metrics)
                if avg_time > 1.0:  # Slow
                    opportunities.append({
                        "type": "caching_opportunity",
                        "function": func_name,
                        "description": f"Function called {len(metrics)} times with avg time {avg_time:.2f}s",
                        "potential_savings": avg_time * len(metrics) * 0.8  # Assume 80% cache hit rate
                    })
        
        # Check cache utilization
        cache_stats = self.get_cache_statistics()
        if cache_stats["utilization"] < 0.5:
            opportunities.append({
                "type": "cache_expansion",
                "description": f"Cache only {cache_stats['utilization']*100:.1f}% utilized",
                "recommendation": "Consider increasing cache size or TTL"
            })
        
        return opportunities
    
    async def apply_optimization_strategy(self, strategy_name: str, target_function: str) -> Dict[str, Any]:
        """Apply specific optimization strategy"""
        if strategy_name == "aggressive_caching":
            return await self.apply_aggressive_caching(target_function)
        elif strategy_name == "parallel_execution":
            return await self.apply_parallel_execution(target_function)
        elif strategy_name == "result_compression":
            return await self.apply_result_compression(target_function)
        else:
            return {"error": f"Unknown optimization strategy: {strategy_name}"}
    
    async def apply_aggressive_caching(self, target_function: str) -> Dict[str, Any]:
        """Apply aggressive caching strategy"""
        # Increase cache size for specific function
        original_ttl = self.cache.default_ttl
        self.cache.default_ttl = original_ttl * 2  # Double TTL
        
        return {
            "strategy": "aggressive_caching",
            "target_function": target_function,
            "changes": {
                "cache_ttl": f"Increased from {original_ttl}s to {self.cache.default_ttl}s"
            },
            "expected_improvement": "20-40% reduction in execution time for repeated calls"
        }
    
    async def apply_parallel_execution(self, target_function: str) -> Dict[str, Any]:
        """Apply parallel execution optimization"""
        # This would be implemented based on specific function characteristics
        return {
            "strategy": "parallel_execution",
            "target_function": target_function,
            "changes": {
                "execution_mode": "parallel",
                "thread_pool_size": 4
            },
            "expected_improvement": "30-60% reduction in execution time for parallelizable tasks"
        }
    
    async def apply_result_compression(self, target_function: str) -> Dict[str, Any]:
        """Apply result compression optimization"""
        return {
            "strategy": "result_compression",
            "target_function": target_function,
            "changes": {
                "compression_enabled": True,
                "compression_algorithm": "gzip"
            },
            "expected_improvement": "50-80% reduction in memory usage"
        }
    
    def monitor_quality_metrics(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Monitor quality metrics for reasoning results"""
        quality_metrics = {
            "confidence_score": reasoning_result.get("confidence", 0.0),
            "completeness_score": 0.0,
            "consistency_score": 0.0,
            "response_time": reasoning_result.get("execution_time", 0.0),
            "overall_quality": 0.0
        }
        
        # Calculate completeness
        if "final_reasoning" in reasoning_result:
            final_reasoning = reasoning_result["final_reasoning"]
            completeness_factors = [
                "conclusion" in final_reasoning,
                "reasoning_chain" in final_reasoning,
                len(final_reasoning.get("reasoning_chain", [])) > 0
            ]
            quality_metrics["completeness_score"] = sum(completeness_factors) / len(completeness_factors)
        
        # Calculate consistency (simplified)
        if "reasoning_steps" in reasoning_result:
            steps = reasoning_result["reasoning_steps"]
            if steps:
                confidences = [step.get("confidence", 0.5) for step in steps if isinstance(step, dict)]
                if confidences:
                    confidence_variance = sum((c - quality_metrics["confidence_score"])**2 for c in confidences) / len(confidences)
                    quality_metrics["consistency_score"] = max(0, 1.0 - confidence_variance)
        
        # Calculate overall quality
        quality_metrics["overall_quality"] = (
            quality_metrics["confidence_score"] * 0.4 +
            quality_metrics["completeness_score"] * 0.3 +
            quality_metrics["consistency_score"] * 0.3
        )
        
        return quality_metrics
    
    def optimize_execution_monitoring(self) -> Dict[str, Any]:
        """Optimize execution monitoring overhead"""
        optimization_result = {
            "monitoring_overhead": 0.0,
            "optimizations_applied": [],
            "performance_impact": {}
        }
        
        # Reduce monitoring frequency for fast functions
        for func_name, metrics in self.execution_metrics.items():
            if metrics:
                avg_time = sum(m["execution_time"] for m in metrics) / len(metrics)
                if avg_time < 0.1:  # Very fast functions
                    # Sample monitoring instead of full monitoring
                    optimization_result["optimizations_applied"].append({
                        "function": func_name,
                        "optimization": "sampling_monitoring",
                        "sample_rate": 0.1  # Monitor 10% of calls
                    })
        
        return optimization_result
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        report = {
            "summary": {
                "total_functions_monitored": len(self.execution_metrics),
                "total_executions": sum(len(metrics) for metrics in self.execution_metrics.values()),
                "cache_utilization": self.get_cache_statistics()["utilization"],
                "avg_execution_time": 0.0
            },
            "bottleneck_analysis": self.analyze_performance_bottlenecks(),
            "optimization_recommendations": [],
            "performance_trends": {}
        }
        
        # Calculate average execution time
        all_times = []
        for metrics in self.execution_metrics.values():
            all_times.extend([m["execution_time"] for m in metrics])
        
        if all_times:
            report["summary"]["avg_execution_time"] = sum(all_times) / len(all_times)
        
        # Generate recommendations
        bottlenecks = report["bottleneck_analysis"]
        
        if bottlenecks["slow_functions"]:
            report["optimization_recommendations"].append({
                "priority": "high",
                "recommendation": "Implement caching for slow functions",
                "affected_functions": [f["function"] for f in bottlenecks["slow_functions"]],
                "expected_improvement": "30-50% performance gain"
            })
        
        cache_efficiency = bottlenecks["cache_efficiency"]
        if cache_efficiency.get("utilization", 0) < 0.3:
            report["optimization_recommendations"].append({
                "priority": "medium",
                "recommendation": "Increase cache utilization",
                "details": "Cache is underutilized, consider adjusting TTL or cache size",
                "expected_improvement": "10-20% performance gain"
            })
        
        return report
    
    def clear_cache(self):
        """Clear performance cache"""
        self.cache.cache.clear()
        self.cache.access_times.clear()
    
    def reset_metrics(self):
        """Reset performance metrics"""
        self.execution_metrics.clear()
        self.performance_history.clear()