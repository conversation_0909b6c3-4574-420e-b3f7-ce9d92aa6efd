import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from ai.reasoning.multi_agent.planning.hierarchical_planner import HierarchicalPlanner
from ai.reasoning.multi_agent.consensus.consensus_manager import ConsensusManager
from ai.reasoning.multi_agent.negotiation.resource_negotiator import ResourceNegotiator
from ai.reasoning.multi_agent.collaboration.collaborative_solver import CollaborativeSolver
from typing import Dict, List, Any
import asyncio

class MultiAgentCoordinator:
    def __init__(self):
        self.hierarchical_planner = HierarchicalPlanner()
        self.consensus_manager = ConsensusManager()
        self.resource_negotiator = ResourceNegotiator()
        self.collaborative_solver = CollaborativeSolver()
        self.coordination_history = []
        
    async def coordinate_multi_agent_reasoning(self, problem: str, available_agents: List[Dict[str, Any]], 
                                             context: Dict[str, Any]) -> Dict[str, Any]:
        """Complete multi-agent coordination for reasoning task"""
        coordination_result = {
            "problem": problem,
            "available_agents": available_agents,
            "coordination_stages": {},
            "final_coordination": {},
            "performance_metrics": {}
        }
        
        try:
            # Stage 1: Hierarchical Planning
            planning_result = await self.hierarchical_planner.create_hierarchical_plan(
                problem, context, available_agents
            )
            coordination_result["coordination_stages"]["hierarchical_planning"] = planning_result
            
            # Stage 2: Resource Negotiation
            resource_requests = self.extract_resource_requests(planning_result, available_agents)
            negotiation_result = await self.resource_negotiator.negotiate_resources(resource_requests)
            coordination_result["coordination_stages"]["resource_negotiation"] = negotiation_result
            
            # Stage 3: Consensus Building
            consensus_proposals = self.create_consensus_proposals(planning_result, negotiation_result)
            consensus_result = await self.achieve_multi_consensus(consensus_proposals, available_agents)
            coordination_result["coordination_stages"]["consensus_building"] = consensus_result
            
            # Stage 4: Collaborative Problem Solving
            collaboration_result = await self.collaborative_solver.solve_collaboratively(
                problem, available_agents, context
            )
            coordination_result["coordination_stages"]["collaborative_solving"] = collaboration_result
            
            # Stage 5: Final Coordination Integration
            final_coordination = await self.integrate_coordination_results(coordination_result)
            coordination_result["final_coordination"] = final_coordination
            
            # Stage 6: Performance Metrics
            performance_metrics = self.calculate_coordination_performance(coordination_result)
            coordination_result["performance_metrics"] = performance_metrics
            
            # Store in history
            self.coordination_history.append(coordination_result)
            
            return coordination_result
            
        except Exception as e:
            return {
                "error": str(e),
                "problem": problem,
                "coordination_failed": True
            }
    
    def extract_resource_requests(self, planning_result: Dict[str, Any], 
                                agents: List[Dict[str, Any]]) -> List:
        """Extract resource requests from planning result"""
        from ai.reasoning.multi_agent.negotiation.resource_negotiator import ResourceRequest, ResourceType, Priority
        
        resource_requests = []
        
        # Extract from goal decomposition
        goals = planning_result.get("goal_decomposition", {}).get("primary_goals", [])
        
        for goal in goals:
            for agent_id in goal.assigned_agents:
                # Create resource request based on goal requirements
                request = ResourceRequest(
                    request_id=f"req_{goal.goal_id}_{agent_id}",
                    agent_id=agent_id,
                    resource_type=ResourceType.COMPUTATIONAL,  # Default
                    amount_requested=goal.estimated_effort / 10.0,  # Scale down
                    priority=Priority(goal.priority.value),
                    deadline=goal.estimated_effort,
                    justification=f"Required for {goal.description}",
                    flexibility=0.7
                )
                resource_requests.append(request)
        
        return resource_requests
    
    def create_consensus_proposals(self, planning_result: Dict[str, Any], 
                                 negotiation_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create consensus proposals from planning and negotiation results"""
        proposals = []
        
        # Proposal 1: Execution Plan Approval
        execution_plan = planning_result.get("execution_plan", {})
        if execution_plan:
            proposals.append({
                "proposal_id": "execution_plan_approval",
                "content": f"Approve execution plan with {len(execution_plan.get('phases', []))} phases",
                "type": "plan_approval",
                "details": execution_plan
            })
        
        # Proposal 2: Resource Allocation Approval
        if negotiation_result.get("resource_allocations"):
            proposals.append({
                "proposal_id": "resource_allocation_approval",
                "content": f"Approve resource allocation with {negotiation_result['final_allocation_efficiency']:.2f} efficiency",
                "type": "resource_approval",
                "details": negotiation_result
            })
        
        # Proposal 3: Coordination Protocol
        proposals.append({
            "proposal_id": "coordination_protocol",
            "content": "Establish coordination protocol for multi-agent collaboration",
            "type": "protocol_establishment",
            "details": {"communication_frequency": "high", "synchronization_points": "automatic"}
        })
        
        return proposals
    
    async def achieve_multi_consensus(self, proposals: List[Dict[str, Any]], 
                                    agents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Achieve consensus on multiple proposals"""
        from ai.reasoning.multi_agent.consensus.consensus_manager import VoteType
        
        multi_consensus_result = {
            "total_proposals": len(proposals),
            "consensus_results": [],
            "overall_consensus_rate": 0.0,
            "failed_consensus": []
        }
        
        agent_ids = [agent.get("agent_id", agent.get("name", "unknown")) for agent in agents]
        successful_consensus = 0
        
        for proposal in proposals:
            consensus_result = await self.consensus_manager.achieve_consensus(
                proposal, agent_ids, VoteType.MAJORITY
            )
            
            multi_consensus_result["consensus_results"].append(consensus_result)
            
            if consensus_result["consensus_achieved"]:
                successful_consensus += 1
            else:
                multi_consensus_result["failed_consensus"].append(proposal["proposal_id"])
        
        multi_consensus_result["overall_consensus_rate"] = successful_consensus / len(proposals) if proposals else 0
        
        return multi_consensus_result
    
    async def integrate_coordination_results(self, coordination_result: Dict[str, Any]) -> Dict[str, Any]:
        """Integrate results from all coordination stages"""
        integration_result = {
            "coordination_success": True,
            "integrated_plan": {},
            "agent_assignments": {},
            "resource_allocation": {},
            "consensus_decisions": {},
            "collaborative_solution": {},
            "coordination_quality": 0.0
        }
        
        stages = coordination_result.get("coordination_stages", {})
        
        # Integrate planning results
        if "hierarchical_planning" in stages:
            planning = stages["hierarchical_planning"]
            integration_result["integrated_plan"] = {
                "total_goals": planning.get("goal_decomposition", {}).get("total_goals", 0),
                "execution_phases": len(planning.get("execution_plan", {}).get("phases", [])),
                "coordination_requirements": len(planning.get("coordination_requirements", []))
            }
            
            # Extract agent assignments
            allocation = planning.get("subgoal_allocation", {})
            integration_result["agent_assignments"] = allocation.get("agent_assignments", {})
        
        # Integrate resource negotiation
        if "resource_negotiation" in stages:
            negotiation = stages["resource_negotiation"]
            integration_result["resource_allocation"] = {
                "allocation_efficiency": negotiation.get("final_allocation_efficiency", 0.0),
                "total_allocations": len(negotiation.get("resource_allocations", [])),
                "conflicts_resolved": negotiation.get("conflicts_resolved", 0)
            }
        
        # Integrate consensus results
        if "consensus_building" in stages:
            consensus = stages["consensus_building"]
            integration_result["consensus_decisions"] = {
                "consensus_rate": consensus.get("overall_consensus_rate", 0.0),
                "successful_proposals": len(consensus.get("consensus_results", [])),
                "failed_consensus": len(consensus.get("failed_consensus", []))
            }
        
        # Integrate collaborative solution
        if "collaborative_solving" in stages:
            collaboration = stages["collaborative_solving"]
            integration_result["collaborative_solution"] = {
                "solution_confidence": collaboration.get("final_solution", {}).get("confidence_level", 0.0),
                "participating_agents": len(collaboration.get("participating_agents", [])),
                "knowledge_items": len(collaboration.get("shared_knowledge", {}).get("knowledge_items", []))
            }
        
        # Calculate overall coordination quality
        quality_factors = [
            integration_result["resource_allocation"].get("allocation_efficiency", 0.0),
            integration_result["consensus_decisions"].get("consensus_rate", 0.0),
            integration_result["collaborative_solution"].get("solution_confidence", 0.0)
        ]
        
        integration_result["coordination_quality"] = sum(quality_factors) / len(quality_factors) if quality_factors else 0.0
        
        # Determine coordination success
        integration_result["coordination_success"] = integration_result["coordination_quality"] > 0.6
        
        return integration_result
    
    def calculate_coordination_performance(self, coordination_result: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate performance metrics for multi-agent coordination"""
        performance_metrics = {
            "planning_efficiency": 0.0,
            "resource_utilization": 0.0,
            "consensus_effectiveness": 0.0,
            "collaboration_quality": 0.0,
            "overall_coordination_score": 0.0,
            "bottlenecks_identified": [],
            "improvement_recommendations": []
        }
        
        stages = coordination_result.get("coordination_stages", {})
        
        # Planning efficiency
        if "hierarchical_planning" in stages:
            planning = stages["hierarchical_planning"]
            allocation_efficiency = planning.get("subgoal_allocation", {}).get("allocation_efficiency", 0.0)
            performance_metrics["planning_efficiency"] = allocation_efficiency
        
        # Resource utilization
        if "resource_negotiation" in stages:
            negotiation = stages["resource_negotiation"]
            performance_metrics["resource_utilization"] = negotiation.get("final_allocation_efficiency", 0.0)
        
        # Consensus effectiveness
        if "consensus_building" in stages:
            consensus = stages["consensus_building"]
            performance_metrics["consensus_effectiveness"] = consensus.get("overall_consensus_rate", 0.0)
        
        # Collaboration quality
        if "collaborative_solving" in stages:
            collaboration = stages["collaborative_solving"]
            collab_metrics = collaboration.get("collaboration_metrics", {})
            performance_metrics["collaboration_quality"] = collab_metrics.get("overall_collaboration_score", 0.0)
        
        # Overall coordination score
        scores = [
            performance_metrics["planning_efficiency"],
            performance_metrics["resource_utilization"],
            performance_metrics["consensus_effectiveness"],
            performance_metrics["collaboration_quality"]
        ]
        
        performance_metrics["overall_coordination_score"] = sum(scores) / len(scores) if scores else 0.0
        
        # Identify bottlenecks
        if performance_metrics["planning_efficiency"] < 0.5:
            performance_metrics["bottlenecks_identified"].append("planning_inefficiency")
        if performance_metrics["resource_utilization"] < 0.5:
            performance_metrics["bottlenecks_identified"].append("resource_underutilization")
        if performance_metrics["consensus_effectiveness"] < 0.5:
            performance_metrics["bottlenecks_identified"].append("consensus_difficulties")
        
        # Generate recommendations
        if "planning_inefficiency" in performance_metrics["bottlenecks_identified"]:
            performance_metrics["improvement_recommendations"].append("Improve goal decomposition and agent matching")
        if "resource_underutilization" in performance_metrics["bottlenecks_identified"]:
            performance_metrics["improvement_recommendations"].append("Optimize resource allocation algorithms")
        if "consensus_difficulties" in performance_metrics["bottlenecks_identified"]:
            performance_metrics["improvement_recommendations"].append("Implement better consensus mechanisms")
        
        return performance_metrics
    
    async def adaptive_coordination(self, problem: str, agents: List[Dict[str, Any]], 
                                  context: Dict[str, Any], coordination_strategy: str = "balanced") -> Dict[str, Any]:
        """Adaptive coordination based on problem characteristics and strategy"""
        adaptive_result = {
            "strategy_used": coordination_strategy,
            "adaptations_made": [],
            "coordination_result": {}
        }
        
        # Analyze problem characteristics
        problem_analysis = self.analyze_problem_characteristics(problem, context)
        
        # Adapt coordination strategy
        if coordination_strategy == "efficiency_focused":
            # Prioritize resource efficiency and speed
            adapted_agents = self.select_most_efficient_agents(agents, problem_analysis)
            adaptive_result["adaptations_made"].append("Selected most efficient agents")
            
        elif coordination_strategy == "quality_focused":
            # Prioritize solution quality and consensus
            adapted_agents = self.select_highest_quality_agents(agents, problem_analysis)
            adaptive_result["adaptations_made"].append("Selected highest quality agents")
            
        elif coordination_strategy == "collaborative_focused":
            # Prioritize collaboration and knowledge sharing
            adapted_agents = agents  # Use all available agents
            adaptive_result["adaptations_made"].append("Maximized agent collaboration")
            
        else:  # balanced
            adapted_agents = agents
            adaptive_result["adaptations_made"].append("Used balanced approach")
        
        # Execute coordination with adapted strategy
        coordination_result = await self.coordinate_multi_agent_reasoning(problem, adapted_agents, context)
        adaptive_result["coordination_result"] = coordination_result
        
        return adaptive_result
    
    def analyze_problem_characteristics(self, problem: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze problem characteristics to guide coordination strategy"""
        problem_lower = problem.lower()
        
        characteristics = {
            "complexity": "medium",
            "domain_specificity": "general",
            "time_sensitivity": "normal",
            "collaboration_requirement": "medium",
            "resource_intensity": "medium"
        }
        
        # Analyze complexity
        if any(word in problem_lower for word in ["complex", "multiple", "various", "comprehensive"]):
            characteristics["complexity"] = "high"
        elif any(word in problem_lower for word in ["simple", "basic", "straightforward"]):
            characteristics["complexity"] = "low"
        
        # Analyze domain specificity
        if any(word in problem_lower for word in ["policy", "regulation", "compliance", "legal"]):
            characteristics["domain_specificity"] = "high"
        
        # Analyze time sensitivity
        if any(word in problem_lower for word in ["urgent", "immediate", "asap", "quickly"]):
            characteristics["time_sensitivity"] = "high"
        
        # Analyze collaboration requirement
        if any(word in problem_lower for word in ["compare", "analyze", "evaluate", "comprehensive"]):
            characteristics["collaboration_requirement"] = "high"
        
        return characteristics
    
    def select_most_efficient_agents(self, agents: List[Dict[str, Any]], 
                                   problem_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Select most efficient agents for the problem"""
        # Simple selection - prefer agents with high capability match
        efficient_agents = []
        
        for agent in agents:
            capabilities = agent.get("capabilities", [])
            if len(capabilities) > 3:  # Agents with more capabilities are considered more efficient
                efficient_agents.append(agent)
        
        return efficient_agents if efficient_agents else agents[:3]  # Limit to top 3
    
    def select_highest_quality_agents(self, agents: List[Dict[str, Any]], 
                                    problem_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Select highest quality agents for the problem"""
        # Prefer specialized agents for quality
        quality_agents = []
        
        for agent in agents:
            agent_id = agent.get("agent_id", agent.get("name", ""))
            if any(word in agent_id.lower() for word in ["reasoning", "critic", "expert"]):
                quality_agents.append(agent)
        
        # Add other agents if needed
        remaining_agents = [agent for agent in agents if agent not in quality_agents]
        quality_agents.extend(remaining_agents[:2])  # Add up to 2 more
        
        return quality_agents
    
    def get_coordination_statistics(self) -> Dict[str, Any]:
        """Get coordination performance statistics"""
        if not self.coordination_history:
            return {"no_data": True}
        
        total_coordinations = len(self.coordination_history)
        successful_coordinations = sum(1 for coord in self.coordination_history 
                                     if coord.get("final_coordination", {}).get("coordination_success", False))
        
        # Calculate average performance metrics
        avg_metrics = {
            "planning_efficiency": 0.0,
            "resource_utilization": 0.0,
            "consensus_effectiveness": 0.0,
            "collaboration_quality": 0.0
        }
        
        for coord in self.coordination_history:
            perf_metrics = coord.get("performance_metrics", {})
            for metric in avg_metrics:
                avg_metrics[metric] += perf_metrics.get(metric, 0.0)
        
        for metric in avg_metrics:
            avg_metrics[metric] /= total_coordinations
        
        return {
            "total_coordinations": total_coordinations,
            "success_rate": successful_coordinations / total_coordinations if total_coordinations > 0 else 0,
            "average_performance_metrics": avg_metrics,
            "most_common_bottleneck": "planning_inefficiency",  # Would calculate from actual data
            "coordination_efficiency_trend": "improving"  # Would calculate from historical data
        }