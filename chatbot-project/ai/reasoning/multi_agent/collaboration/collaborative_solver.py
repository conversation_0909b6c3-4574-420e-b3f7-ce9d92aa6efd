from typing import Dict, <PERSON>, <PERSON>, <PERSON><PERSON>
from dataclasses import dataclass
import asyncio
import uuid

@dataclass
class SharedKnowledge:
    knowledge_id: str
    content: Dict[str, Any]
    contributor_agents: List[str]
    confidence: float
    last_updated: str
    access_permissions: List[str]

@dataclass
class DistributedTask:
    task_id: str
    description: str
    assigned_agents: List[str]
    subtasks: List[Dict[str, Any]]
    dependencies: List[str]
    status: str
    results: Dict[str, Any]

class CollaborativeSolver:
    def __init__(self):
        self.shared_knowledge_base = {}
        self.active_collaborations = {}
        self.distributed_tasks = {}
        self.solution_integration_history = []
        
    async def solve_collaboratively(self, problem: str, participating_agents: List[Dict[str, Any]], 
                                  context: Dict[str, Any]) -> Dict[str, Any]:
        """Solve problem through collaborative multi-agent approach"""
        collaboration_result = {
            "problem": problem,
            "participating_agents": [agent.get("agent_id", agent.get("name")) for agent in participating_agents],
            "shared_knowledge": {},
            "distributed_reasoning": {},
            "solution_integration": {},
            "final_solution": {},
            "collaboration_metrics": {}
        }
        
        # Step 1: Create shared knowledge representation
        shared_knowledge = await self.create_shared_knowledge(problem, context, participating_agents)
        collaboration_result["shared_knowledge"] = shared_knowledge
        
        # Step 2: Distribute reasoning tasks
        distributed_reasoning = await self.distribute_reasoning_tasks(problem, participating_agents, shared_knowledge)
        collaboration_result["distributed_reasoning"] = distributed_reasoning
        
        # Step 3: Integrate solutions
        solution_integration = await self.integrate_solutions(distributed_reasoning, problem)
        collaboration_result["solution_integration"] = solution_integration
        
        # Step 4: Generate final solution
        final_solution = await self.generate_final_solution(solution_integration, shared_knowledge)
        collaboration_result["final_solution"] = final_solution
        
        # Step 5: Calculate collaboration metrics
        metrics = self.calculate_collaboration_metrics(collaboration_result)
        collaboration_result["collaboration_metrics"] = metrics
        
        return collaboration_result
    
    async def create_shared_knowledge(self, problem: str, context: Dict[str, Any], 
                                    agents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create shared knowledge representation accessible to all agents"""
        shared_knowledge_result = {
            "knowledge_base_id": str(uuid.uuid4()),
            "knowledge_items": [],
            "access_matrix": {},
            "knowledge_graph": {},
            "update_history": []
        }
        
        # Extract knowledge from problem and context
        problem_knowledge = self.extract_problem_knowledge(problem, context)
        
        # Collect knowledge contributions from each agent
        for agent in agents:
            agent_id = agent.get("agent_id", agent.get("name", "unknown"))
            agent_knowledge = await self.collect_agent_knowledge(agent, problem, context)
            
            # Create shared knowledge item
            knowledge_item = SharedKnowledge(
                knowledge_id=f"knowledge_{len(shared_knowledge_result['knowledge_items']) + 1}",
                content=agent_knowledge,
                contributor_agents=[agent_id],
                confidence=agent_knowledge.get("confidence", 0.7),
                last_updated="now",
                access_permissions=["all"]  # All agents can access
            )
            
            shared_knowledge_result["knowledge_items"].append(knowledge_item)
            shared_knowledge_result["access_matrix"][agent_id] = ["read", "write", "contribute"]
        
        # Create knowledge graph connections
        knowledge_graph = self.create_knowledge_graph(shared_knowledge_result["knowledge_items"])
        shared_knowledge_result["knowledge_graph"] = knowledge_graph
        
        # Store in shared knowledge base
        kb_id = shared_knowledge_result["knowledge_base_id"]
        self.shared_knowledge_base[kb_id] = shared_knowledge_result
        
        return shared_knowledge_result
    
    def extract_problem_knowledge(self, problem: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract knowledge from problem statement and context"""
        problem_lower = problem.lower()
        
        knowledge = {
            "problem_type": "unknown",
            "domain": context.get("domain", "general"),
            "organization": context.get("organization", "unknown"),
            "key_concepts": [],
            "requirements": [],
            "constraints": []
        }
        
        # Identify problem type
        if any(word in problem_lower for word in ["policy", "rule", "regulation"]):
            knowledge["problem_type"] = "policy_inquiry"
            knowledge["key_concepts"].append("policy_interpretation")
        elif any(word in problem_lower for word in ["compare", "analyze", "evaluate"]):
            knowledge["problem_type"] = "analytical"
            knowledge["key_concepts"].append("comparative_analysis")
        elif any(word in problem_lower for word in ["calculate", "compute", "determine"]):
            knowledge["problem_type"] = "computational"
            knowledge["key_concepts"].append("numerical_computation")
        
        # Extract requirements
        if "must" in problem_lower or "required" in problem_lower:
            knowledge["requirements"].append("mandatory_compliance")
        if "should" in problem_lower or "recommended" in problem_lower:
            knowledge["requirements"].append("recommended_practice")
        
        return knowledge
    
    async def collect_agent_knowledge(self, agent: Dict[str, Any], problem: str, 
                                    context: Dict[str, Any]) -> Dict[str, Any]:
        """Collect knowledge contribution from a specific agent"""
        agent_id = agent.get("agent_id", agent.get("name", "unknown"))
        capabilities = agent.get("capabilities", [])
        
        agent_knowledge = {
            "agent_id": agent_id,
            "capabilities": capabilities,
            "domain_expertise": [],
            "relevant_knowledge": {},
            "confidence": 0.7
        }
        
        # Determine domain expertise based on agent type
        if "organization" in agent_id.lower():
            agent_knowledge["domain_expertise"] = ["organizational_policies", "company_procedures"]
            agent_knowledge["relevant_knowledge"] = {
                "policy_knowledge": "High familiarity with organizational policies",
                "compliance_requirements": "Understanding of regulatory compliance"
            }
            agent_knowledge["confidence"] = 0.8
            
        elif "department" in agent_id.lower():
            if "hr" in agent_id.lower():
                agent_knowledge["domain_expertise"] = ["human_resources", "employee_relations"]
                agent_knowledge["relevant_knowledge"] = {
                    "hr_policies": "Expert knowledge of HR procedures",
                    "employee_benefits": "Detailed understanding of benefit structures"
                }
            elif "finance" in agent_id.lower():
                agent_knowledge["domain_expertise"] = ["financial_policies", "budget_management"]
                agent_knowledge["relevant_knowledge"] = {
                    "financial_regulations": "Knowledge of financial compliance",
                    "cost_analysis": "Expertise in financial analysis"
                }
            agent_knowledge["confidence"] = 0.9
            
        elif "reasoning" in agent_id.lower():
            agent_knowledge["domain_expertise"] = ["logical_analysis", "problem_solving"]
            agent_knowledge["relevant_knowledge"] = {
                "analytical_methods": "Advanced reasoning capabilities",
                "solution_synthesis": "Ability to integrate multiple perspectives"
            }
            agent_knowledge["confidence"] = 0.85
        
        return agent_knowledge
    
    def create_knowledge_graph(self, knowledge_items: List[SharedKnowledge]) -> Dict[str, Any]:
        """Create knowledge graph from shared knowledge items"""
        knowledge_graph = {
            "nodes": [],
            "edges": [],
            "clusters": [],
            "centrality_scores": {}
        }
        
        # Create nodes for each knowledge item
        for item in knowledge_items:
            node = {
                "node_id": item.knowledge_id,
                "content_summary": list(item.content.keys()),
                "contributors": item.contributor_agents,
                "confidence": item.confidence
            }
            knowledge_graph["nodes"].append(node)
        
        # Create edges based on content similarity
        for i, item1 in enumerate(knowledge_items):
            for j, item2 in enumerate(knowledge_items[i+1:], i+1):
                similarity = self.calculate_knowledge_similarity(item1, item2)
                if similarity > 0.3:  # Threshold for connection
                    edge = {
                        "from_node": item1.knowledge_id,
                        "to_node": item2.knowledge_id,
                        "similarity": similarity,
                        "connection_type": "content_similarity"
                    }
                    knowledge_graph["edges"].append(edge)
        
        # Identify knowledge clusters
        clusters = self.identify_knowledge_clusters(knowledge_graph["nodes"], knowledge_graph["edges"])
        knowledge_graph["clusters"] = clusters
        
        return knowledge_graph
    
    def calculate_knowledge_similarity(self, item1: SharedKnowledge, item2: SharedKnowledge) -> float:
        """Calculate similarity between two knowledge items"""
        # Simple similarity based on common keys
        keys1 = set(item1.content.keys())
        keys2 = set(item2.content.keys())
        
        if not keys1 or not keys2:
            return 0.0
        
        intersection = keys1 & keys2
        union = keys1 | keys2
        
        return len(intersection) / len(union) if union else 0.0
    
    def identify_knowledge_clusters(self, nodes: List[Dict[str, Any]], 
                                  edges: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify clusters in knowledge graph"""
        clusters = []
        
        # Simple clustering based on high similarity connections
        processed_nodes = set()
        
        for node in nodes:
            if node["node_id"] in processed_nodes:
                continue
            
            cluster = {
                "cluster_id": f"cluster_{len(clusters) + 1}",
                "nodes": [node["node_id"]],
                "theme": "unknown",
                "confidence": node["confidence"]
            }
            
            processed_nodes.add(node["node_id"])
            
            # Find connected nodes with high similarity
            for edge in edges:
                if (edge["from_node"] == node["node_id"] and 
                    edge["similarity"] > 0.7 and 
                    edge["to_node"] not in processed_nodes):
                    
                    cluster["nodes"].append(edge["to_node"])
                    processed_nodes.add(edge["to_node"])
            
            # Determine cluster theme
            if len(cluster["nodes"]) > 1:
                cluster["theme"] = "collaborative_knowledge"
            else:
                cluster["theme"] = "specialized_knowledge"
            
            clusters.append(cluster)
        
        return clusters
    
    async def distribute_reasoning_tasks(self, problem: str, agents: List[Dict[str, Any]], 
                                       shared_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """Distribute reasoning tasks among agents"""
        distribution_result = {
            "task_distribution": [],
            "agent_assignments": {},
            "reasoning_coordination": {},
            "expected_outcomes": {}
        }
        
        # Decompose problem into reasoning subtasks
        subtasks = self.decompose_reasoning_problem(problem, shared_knowledge)
        
        # Assign subtasks to agents based on capabilities
        for subtask in subtasks:
            best_agent = self.find_best_agent_for_task(subtask, agents)
            
            if best_agent:
                task_assignment = {
                    "subtask_id": subtask["subtask_id"],
                    "description": subtask["description"],
                    "assigned_agent": best_agent["agent_id"],
                    "required_capabilities": subtask["required_capabilities"],
                    "expected_duration": subtask.get("estimated_time", 60),
                    "dependencies": subtask.get("dependencies", [])
                }
                
                distribution_result["task_distribution"].append(task_assignment)
                
                agent_id = best_agent["agent_id"]
                if agent_id not in distribution_result["agent_assignments"]:
                    distribution_result["agent_assignments"][agent_id] = []
                distribution_result["agent_assignments"][agent_id].append(subtask["subtask_id"])
        
        # Create coordination plan
        coordination_plan = self.create_reasoning_coordination_plan(distribution_result["task_distribution"])
        distribution_result["reasoning_coordination"] = coordination_plan
        
        return distribution_result
    
    def decompose_reasoning_problem(self, problem: str, shared_knowledge: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Decompose problem into reasoning subtasks"""
        subtasks = []
        problem_lower = problem.lower()
        
        # Basic subtask decomposition
        if any(word in problem_lower for word in ["compare", "analyze", "evaluate"]):
            subtasks.extend([
                {
                    "subtask_id": "info_gathering",
                    "description": "Gather relevant information and data",
                    "required_capabilities": ["knowledge_search", "information_retrieval"],
                    "estimated_time": 30,
                    "dependencies": []
                },
                {
                    "subtask_id": "analysis",
                    "description": "Perform comparative analysis",
                    "required_capabilities": ["analytical_reasoning", "comparison"],
                    "estimated_time": 60,
                    "dependencies": ["info_gathering"]
                },
                {
                    "subtask_id": "synthesis",
                    "description": "Synthesize findings and conclusions",
                    "required_capabilities": ["synthesis", "conclusion_generation"],
                    "estimated_time": 45,
                    "dependencies": ["analysis"]
                }
            ])
        else:
            # General problem-solving subtasks
            subtasks.extend([
                {
                    "subtask_id": "problem_understanding",
                    "description": "Understand and clarify the problem",
                    "required_capabilities": ["problem_analysis", "clarification"],
                    "estimated_time": 20,
                    "dependencies": []
                },
                {
                    "subtask_id": "solution_generation",
                    "description": "Generate potential solutions",
                    "required_capabilities": ["solution_generation", "creativity"],
                    "estimated_time": 40,
                    "dependencies": ["problem_understanding"]
                },
                {
                    "subtask_id": "solution_evaluation",
                    "description": "Evaluate and select best solution",
                    "required_capabilities": ["evaluation", "decision_making"],
                    "estimated_time": 30,
                    "dependencies": ["solution_generation"]
                }
            ])
        
        return subtasks
    
    def find_best_agent_for_task(self, subtask: Dict[str, Any], agents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Find the best agent for a specific subtask"""
        required_capabilities = set(subtask.get("required_capabilities", []))
        best_agent = None
        best_score = 0
        
        for agent in agents:
            agent_capabilities = set(agent.get("capabilities", []))
            
            # Calculate capability match score
            match_score = len(required_capabilities & agent_capabilities) / len(required_capabilities) if required_capabilities else 0
            
            # Bonus for specialized agents
            agent_id = agent.get("agent_id", agent.get("name", ""))
            if "reasoning" in agent_id.lower() and "reasoning" in subtask["description"].lower():
                match_score += 0.2
            elif "organization" in agent_id.lower() and "policy" in subtask["description"].lower():
                match_score += 0.2
            
            if match_score > best_score:
                best_score = match_score
                best_agent = agent
        
        return best_agent
    
    def create_reasoning_coordination_plan(self, task_assignments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create coordination plan for distributed reasoning"""
        coordination_plan = {
            "execution_sequence": [],
            "synchronization_points": [],
            "information_sharing_schedule": [],
            "conflict_resolution_protocol": {}
        }
        
        # Create execution sequence based on dependencies
        sequence = self.create_task_execution_sequence(task_assignments)
        coordination_plan["execution_sequence"] = sequence
        
        # Identify synchronization points
        sync_points = self.identify_synchronization_points(task_assignments)
        coordination_plan["synchronization_points"] = sync_points
        
        # Schedule information sharing
        sharing_schedule = self.create_information_sharing_schedule(task_assignments)
        coordination_plan["information_sharing_schedule"] = sharing_schedule
        
        return coordination_plan
    
    def create_task_execution_sequence(self, task_assignments: List[Dict[str, Any]]) -> List[str]:
        """Create execution sequence respecting task dependencies"""
        sequence = []
        remaining_tasks = {task["subtask_id"]: task for task in task_assignments}
        
        while remaining_tasks:
            # Find tasks with no unresolved dependencies
            ready_tasks = []
            for task_id, task in remaining_tasks.items():
                dependencies = task.get("dependencies", [])
                if not dependencies or all(dep not in remaining_tasks for dep in dependencies):
                    ready_tasks.append(task_id)
            
            if not ready_tasks:
                # Break circular dependency
                ready_tasks = [list(remaining_tasks.keys())[0]]
            
            sequence.extend(ready_tasks)
            
            # Remove processed tasks
            for task_id in ready_tasks:
                del remaining_tasks[task_id]
        
        return sequence
    
    def identify_synchronization_points(self, task_assignments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify points where agents need to synchronize"""
        sync_points = []
        
        # Find tasks with dependencies - these are synchronization points
        for task in task_assignments:
            if task.get("dependencies"):
                sync_point = {
                    "sync_id": f"sync_{task['subtask_id']}",
                    "description": f"Synchronization before {task['description']}",
                    "waiting_task": task["subtask_id"],
                    "prerequisite_tasks": task["dependencies"],
                    "involved_agents": [task["assigned_agent"]]
                }
                
                # Add agents from prerequisite tasks
                for dep_task_id in task["dependencies"]:
                    dep_task = next((t for t in task_assignments if t["subtask_id"] == dep_task_id), None)
                    if dep_task:
                        sync_point["involved_agents"].append(dep_task["assigned_agent"])
                
                sync_points.append(sync_point)
        
        return sync_points
    
    def create_information_sharing_schedule(self, task_assignments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create schedule for information sharing between agents"""
        sharing_schedule = []
        
        # Schedule sharing at task completion
        for task in task_assignments:
            sharing_event = {
                "event_id": f"share_{task['subtask_id']}",
                "trigger": "task_completion",
                "source_agent": task["assigned_agent"],
                "target_agents": "all",  # Share with all agents
                "information_type": "task_results",
                "estimated_time": task.get("expected_duration", 60)
            }
            sharing_schedule.append(sharing_event)
        
        return sharing_schedule
    
    async def integrate_solutions(self, distributed_reasoning: Dict[str, Any], problem: str) -> Dict[str, Any]:
        """Integrate solutions from distributed reasoning"""
        integration_result = {
            "individual_solutions": [],
            "integration_method": "weighted_consensus",
            "integrated_solution": {},
            "confidence_scores": {},
            "integration_conflicts": []
        }
        
        # Simulate individual solutions from agents
        task_assignments = distributed_reasoning.get("task_distribution", [])
        
        for task in task_assignments:
            individual_solution = await self.simulate_agent_solution(task, problem)
            integration_result["individual_solutions"].append(individual_solution)
        
        # Integrate solutions using weighted consensus
        integrated_solution = self.apply_weighted_consensus(integration_result["individual_solutions"])
        integration_result["integrated_solution"] = integrated_solution
        
        # Calculate confidence scores
        confidence_scores = self.calculate_integration_confidence(integration_result["individual_solutions"])
        integration_result["confidence_scores"] = confidence_scores
        
        return integration_result
    
    async def simulate_agent_solution(self, task: Dict[str, Any], problem: str) -> Dict[str, Any]:
        """Simulate solution from an agent for a specific task"""
        agent_id = task["assigned_agent"]
        subtask_id = task["subtask_id"]
        
        # Simulate different solution approaches based on agent type
        if "reasoning" in agent_id.lower():
            solution = {
                "agent_id": agent_id,
                "subtask_id": subtask_id,
                "solution_content": f"Logical analysis suggests systematic approach to {task['description']}",
                "confidence": 0.85,
                "reasoning_method": "logical_analysis",
                "supporting_evidence": ["Systematic methodology", "Logical consistency"]
            }
        elif "organization" in agent_id.lower():
            solution = {
                "agent_id": agent_id,
                "subtask_id": subtask_id,
                "solution_content": f"Organizational policy recommends standard procedure for {task['description']}",
                "confidence": 0.8,
                "reasoning_method": "policy_based",
                "supporting_evidence": ["Organizational guidelines", "Established procedures"]
            }
        else:
            solution = {
                "agent_id": agent_id,
                "subtask_id": subtask_id,
                "solution_content": f"Domain expertise suggests best practice for {task['description']}",
                "confidence": 0.75,
                "reasoning_method": "domain_expertise",
                "supporting_evidence": ["Domain knowledge", "Best practices"]
            }
        
        return solution
    
    def apply_weighted_consensus(self, individual_solutions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Apply weighted consensus to integrate individual solutions"""
        if not individual_solutions:
            return {"integrated_content": "", "overall_confidence": 0.0}
        
        # Weight solutions by confidence
        total_weight = sum(sol["confidence"] for sol in individual_solutions)
        
        integrated_content_parts = []
        supporting_evidence = []
        
        for solution in individual_solutions:
            weight = solution["confidence"] / total_weight if total_weight > 0 else 1.0 / len(individual_solutions)
            
            # Weight the content contribution
            content_contribution = f"[{solution['agent_id']}]: {solution['solution_content']}"
            integrated_content_parts.append(content_contribution)
            
            supporting_evidence.extend(solution.get("supporting_evidence", []))
        
        integrated_solution = {
            "integrated_content": " | ".join(integrated_content_parts),
            "overall_confidence": sum(sol["confidence"] for sol in individual_solutions) / len(individual_solutions),
            "contributing_agents": [sol["agent_id"] for sol in individual_solutions],
            "integration_method": "weighted_consensus",
            "supporting_evidence": list(set(supporting_evidence))  # Remove duplicates
        }
        
        return integrated_solution
    
    def calculate_integration_confidence(self, individual_solutions: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate confidence scores for solution integration"""
        if not individual_solutions:
            return {}
        
        confidence_scores = {
            "average_confidence": sum(sol["confidence"] for sol in individual_solutions) / len(individual_solutions),
            "min_confidence": min(sol["confidence"] for sol in individual_solutions),
            "max_confidence": max(sol["confidence"] for sol in individual_solutions),
            "confidence_variance": 0.0
        }
        
        # Calculate variance
        avg_conf = confidence_scores["average_confidence"]
        variance = sum((sol["confidence"] - avg_conf) ** 2 for sol in individual_solutions) / len(individual_solutions)
        confidence_scores["confidence_variance"] = variance
        
        return confidence_scores
    
    async def generate_final_solution(self, solution_integration: Dict[str, Any], 
                                    shared_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final collaborative solution"""
        integrated_solution = solution_integration.get("integrated_solution", {})
        
        final_solution = {
            "solution_summary": integrated_solution.get("integrated_content", ""),
            "confidence_level": integrated_solution.get("overall_confidence", 0.0),
            "contributing_agents": integrated_solution.get("contributing_agents", []),
            "knowledge_sources": len(shared_knowledge.get("knowledge_items", [])),
            "solution_quality": "unknown",
            "recommendations": []
        }
        
        # Assess solution quality
        confidence = final_solution["confidence_level"]
        if confidence > 0.8:
            final_solution["solution_quality"] = "high"
        elif confidence > 0.6:
            final_solution["solution_quality"] = "medium"
        else:
            final_solution["solution_quality"] = "low"
        
        # Generate recommendations
        if final_solution["solution_quality"] == "low":
            final_solution["recommendations"].append("Consider additional agent consultation")
        
        if len(final_solution["contributing_agents"]) < 3:
            final_solution["recommendations"].append("Involve more diverse agent perspectives")
        
        return final_solution
    
    def calculate_collaboration_metrics(self, collaboration_result: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate metrics for collaboration effectiveness"""
        metrics = {
            "agent_participation_rate": 0.0,
            "knowledge_sharing_efficiency": 0.0,
            "solution_integration_success": 0.0,
            "overall_collaboration_score": 0.0
        }
        
        # Agent participation rate
        total_agents = len(collaboration_result["participating_agents"])
        participating_agents = len(collaboration_result.get("distributed_reasoning", {}).get("agent_assignments", {}))
        metrics["agent_participation_rate"] = participating_agents / total_agents if total_agents > 0 else 0
        
        # Knowledge sharing efficiency
        knowledge_items = len(collaboration_result.get("shared_knowledge", {}).get("knowledge_items", []))
        metrics["knowledge_sharing_efficiency"] = min(knowledge_items / total_agents, 1.0) if total_agents > 0 else 0
        
        # Solution integration success
        final_confidence = collaboration_result.get("final_solution", {}).get("confidence_level", 0.0)
        metrics["solution_integration_success"] = final_confidence
        
        # Overall collaboration score
        metrics["overall_collaboration_score"] = (
            metrics["agent_participation_rate"] * 0.3 +
            metrics["knowledge_sharing_efficiency"] * 0.3 +
            metrics["solution_integration_success"] * 0.4
        )
        
        return metrics