from typing import Dict, List, Any, Tu<PERSON>
from dataclasses import dataclass
from enum import Enum
import uuid

class GoalPriority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class Goal:
    goal_id: str
    description: str
    priority: GoalPriority
    complexity: float
    required_capabilities: List[str]
    dependencies: List[str]
    estimated_effort: int
    assigned_agents: List[str]
    status: str = "pending"

@dataclass
class SubGoal:
    subgoal_id: str
    parent_goal_id: str
    description: str
    assigned_agent: str
    estimated_time: int
    dependencies: List[str]
    status: str = "pending"

class HierarchicalPlanner:
    def __init__(self):
        self.goals = {}
        self.subgoals = {}
        self.agent_capabilities = {}
        self.planning_history = []
        
    async def create_hierarchical_plan(self, main_objective: str, context: Dict[str, Any], 
                                     available_agents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create hierarchical plan with goal decomposition"""
        planning_result = {
            "main_objective": main_objective,
            "goal_decomposition": {},
            "subgoal_allocation": {},
            "plan_integration": {},
            "execution_plan": {},
            "coordination_requirements": []
        }
        
        # Step 1: Goal Decomposition
        goals = await self.decompose_goal(main_objective, context)
        planning_result["goal_decomposition"] = {
            "primary_goals": goals,
            "total_goals": len(goals),
            "complexity_distribution": self.analyze_goal_complexity(goals)
        }
        
        # Step 2: Sub-goal Allocation
        allocation_result = await self.allocate_subgoals(goals, available_agents)
        planning_result["subgoal_allocation"] = allocation_result
        
        # Step 3: Plan Integration
        integration_result = await self.integrate_plans(goals, allocation_result)
        planning_result["plan_integration"] = integration_result
        
        # Step 4: Create Execution Plan
        execution_plan = await self.create_execution_plan(goals, allocation_result)
        planning_result["execution_plan"] = execution_plan
        
        # Step 5: Identify Coordination Requirements
        coordination_reqs = self.identify_coordination_requirements(goals, allocation_result)
        planning_result["coordination_requirements"] = coordination_reqs
        
        return planning_result
    
    async def decompose_goal(self, main_objective: str, context: Dict[str, Any]) -> List[Goal]:
        """Decompose main objective into manageable goals"""
        goals = []
        objective_lower = main_objective.lower()
        
        # Analyze objective complexity
        if any(word in objective_lower for word in ["compare", "analyze", "evaluate"]):
            # Complex analytical goal
            goals.append(Goal(
                goal_id=str(uuid.uuid4()),
                description="Information gathering and analysis",
                priority=GoalPriority.HIGH,
                complexity=0.8,
                required_capabilities=["knowledge_search", "analysis"],
                dependencies=[],
                estimated_effort=60,
                assigned_agents=[]
            ))
            
            goals.append(Goal(
                goal_id=str(uuid.uuid4()),
                description="Comparative analysis and reasoning",
                priority=GoalPriority.HIGH,
                complexity=0.9,
                required_capabilities=["reasoning", "comparison"],
                dependencies=[goals[0].goal_id],
                estimated_effort=90,
                assigned_agents=[]
            ))
            
            goals.append(Goal(
                goal_id=str(uuid.uuid4()),
                description="Result synthesis and validation",
                priority=GoalPriority.MEDIUM,
                complexity=0.6,
                required_capabilities=["synthesis", "validation"],
                dependencies=[goals[1].goal_id],
                estimated_effort=45,
                assigned_agents=[]
            ))
        
        elif any(word in objective_lower for word in ["policy", "rule", "procedure"]):
            # Policy-related goal
            goals.append(Goal(
                goal_id=str(uuid.uuid4()),
                description="Policy document retrieval",
                priority=GoalPriority.HIGH,
                complexity=0.5,
                required_capabilities=["document_search", "policy_knowledge"],
                dependencies=[],
                estimated_effort=30,
                assigned_agents=[]
            ))
            
            goals.append(Goal(
                goal_id=str(uuid.uuid4()),
                description="Policy interpretation and application",
                priority=GoalPriority.HIGH,
                complexity=0.7,
                required_capabilities=["policy_interpretation", "reasoning"],
                dependencies=[goals[0].goal_id],
                estimated_effort=60,
                assigned_agents=[]
            ))
        
        else:
            # General goal
            goals.append(Goal(
                goal_id=str(uuid.uuid4()),
                description="Information processing and response generation",
                priority=GoalPriority.MEDIUM,
                complexity=0.6,
                required_capabilities=["general_reasoning", "response_generation"],
                dependencies=[],
                estimated_effort=45,
                assigned_agents=[]
            ))
        
        return goals
    
    async def allocate_subgoals(self, goals: List[Goal], available_agents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Allocate sub-goals to appropriate agents"""
        allocation_result = {
            "agent_assignments": {},
            "subgoals_created": [],
            "allocation_efficiency": 0.0,
            "unassigned_goals": []
        }
        
        # Create agent capability mapping
        agent_capabilities = {}
        for agent in available_agents:
            agent_id = agent.get("agent_id", agent.get("name", "unknown"))
            capabilities = agent.get("capabilities", [])
            agent_capabilities[agent_id] = capabilities
        
        # Allocate each goal
        for goal in goals:
            best_agents = self.find_best_agents(goal, agent_capabilities)
            
            if best_agents:
                # Create sub-goals for the goal
                subgoals = self.create_subgoals_for_goal(goal, best_agents)
                allocation_result["subgoals_created"].extend(subgoals)
                
                # Assign agents
                for subgoal in subgoals:
                    agent_id = subgoal.assigned_agent
                    if agent_id not in allocation_result["agent_assignments"]:
                        allocation_result["agent_assignments"][agent_id] = []
                    allocation_result["agent_assignments"][agent_id].append(subgoal.subgoal_id)
                
                goal.assigned_agents = [sg.assigned_agent for sg in subgoals]
            else:
                allocation_result["unassigned_goals"].append(goal.goal_id)
        
        # Calculate allocation efficiency
        total_goals = len(goals)
        assigned_goals = total_goals - len(allocation_result["unassigned_goals"])
        allocation_result["allocation_efficiency"] = assigned_goals / total_goals if total_goals > 0 else 0
        
        return allocation_result
    
    def find_best_agents(self, goal: Goal, agent_capabilities: Dict[str, List[str]]) -> List[str]:
        """Find best agents for a goal based on capabilities"""
        suitable_agents = []
        
        for agent_id, capabilities in agent_capabilities.items():
            # Calculate capability match
            required_caps = set(goal.required_capabilities)
            agent_caps = set(capabilities)
            match_score = len(required_caps & agent_caps) / len(required_caps) if required_caps else 0
            
            if match_score > 0.5:  # At least 50% capability match
                suitable_agents.append((agent_id, match_score))
        
        # Sort by match score and return top agents
        suitable_agents.sort(key=lambda x: x[1], reverse=True)
        return [agent_id for agent_id, _ in suitable_agents[:2]]  # Top 2 agents
    
    def create_subgoals_for_goal(self, goal: Goal, assigned_agents: List[str]) -> List[SubGoal]:
        """Create sub-goals for a main goal"""
        subgoals = []
        
        if len(assigned_agents) == 1:
            # Single agent handles the entire goal
            subgoals.append(SubGoal(
                subgoal_id=str(uuid.uuid4()),
                parent_goal_id=goal.goal_id,
                description=f"Execute: {goal.description}",
                assigned_agent=assigned_agents[0],
                estimated_time=goal.estimated_effort,
                dependencies=goal.dependencies
            ))
        else:
            # Multiple agents - split the goal
            effort_per_agent = goal.estimated_effort // len(assigned_agents)
            
            for i, agent_id in enumerate(assigned_agents):
                subgoals.append(SubGoal(
                    subgoal_id=str(uuid.uuid4()),
                    parent_goal_id=goal.goal_id,
                    description=f"Part {i+1}: {goal.description}",
                    assigned_agent=agent_id,
                    estimated_time=effort_per_agent,
                    dependencies=goal.dependencies if i == 0 else [subgoals[i-1].subgoal_id]
                ))
        
        return subgoals
    
    async def integrate_plans(self, goals: List[Goal], allocation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Integrate individual plans into cohesive execution plan"""
        integration_result = {
            "execution_sequence": [],
            "parallel_execution_groups": [],
            "critical_path": [],
            "integration_points": [],
            "estimated_total_time": 0
        }
        
        # Create execution sequence based on dependencies
        execution_sequence = self.create_execution_sequence(goals)
        integration_result["execution_sequence"] = execution_sequence
        
        # Identify parallel execution opportunities
        parallel_groups = self.identify_parallel_execution(goals)
        integration_result["parallel_execution_groups"] = parallel_groups
        
        # Calculate critical path
        critical_path = self.calculate_critical_path(goals)
        integration_result["critical_path"] = critical_path
        integration_result["estimated_total_time"] = sum(goal.estimated_effort for goal in critical_path)
        
        # Identify integration points where agents need to coordinate
        integration_points = self.identify_integration_points(goals, allocation_result)
        integration_result["integration_points"] = integration_points
        
        return integration_result
    
    def create_execution_sequence(self, goals: List[Goal]) -> List[str]:
        """Create execution sequence respecting dependencies"""
        sequence = []
        remaining_goals = {goal.goal_id: goal for goal in goals}
        
        while remaining_goals:
            # Find goals with no unresolved dependencies
            ready_goals = []
            for goal_id, goal in remaining_goals.items():
                if not goal.dependencies or all(dep not in remaining_goals for dep in goal.dependencies):
                    ready_goals.append(goal_id)
            
            if not ready_goals:
                # Circular dependency - break it
                ready_goals = [list(remaining_goals.keys())[0]]
            
            # Sort by priority and add to sequence
            ready_goals.sort(key=lambda gid: remaining_goals[gid].priority.value, reverse=True)
            sequence.extend(ready_goals)
            
            # Remove processed goals
            for goal_id in ready_goals:
                del remaining_goals[goal_id]
        
        return sequence
    
    def identify_parallel_execution(self, goals: List[Goal]) -> List[List[str]]:
        """Identify goals that can be executed in parallel"""
        parallel_groups = []
        goal_dict = {goal.goal_id: goal for goal in goals}
        
        # Group goals with no dependencies between them
        processed = set()
        
        for goal in goals:
            if goal.goal_id in processed:
                continue
            
            group = [goal.goal_id]
            processed.add(goal.goal_id)
            
            # Find other goals that can run in parallel
            for other_goal in goals:
                if (other_goal.goal_id not in processed and
                    goal.goal_id not in other_goal.dependencies and
                    other_goal.goal_id not in goal.dependencies):
                    group.append(other_goal.goal_id)
                    processed.add(other_goal.goal_id)
            
            if len(group) > 1:
                parallel_groups.append(group)
        
        return parallel_groups
    
    def calculate_critical_path(self, goals: List[Goal]) -> List[Goal]:
        """Calculate critical path through goals"""
        # Simple critical path - longest dependency chain
        goal_dict = {goal.goal_id: goal for goal in goals}
        
        def get_path_length(goal_id, visited=None):
            if visited is None:
                visited = set()
            
            if goal_id in visited:
                return 0  # Circular dependency
            
            visited.add(goal_id)
            goal = goal_dict[goal_id]
            
            if not goal.dependencies:
                return goal.estimated_effort
            
            max_dep_length = max(get_path_length(dep, visited.copy()) for dep in goal.dependencies)
            return goal.estimated_effort + max_dep_length
        
        # Find goal with longest path
        longest_path_goal = max(goals, key=lambda g: get_path_length(g.goal_id))
        
        # Build critical path
        critical_path = []
        current_goal = longest_path_goal
        
        while current_goal:
            critical_path.append(current_goal)
            
            # Find dependency with longest path
            if current_goal.dependencies:
                next_goal_id = max(current_goal.dependencies, key=lambda dep: get_path_length(dep))
                current_goal = goal_dict.get(next_goal_id)
            else:
                current_goal = None
        
        return list(reversed(critical_path))
    
    def identify_coordination_requirements(self, goals: List[Goal], allocation_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify coordination requirements for the plan"""
        coordination_reqs = []
        
        # Multi-agent goals require coordination
        for goal in goals:
            if len(goal.assigned_agents) > 1:
                coordination_reqs.append({
                    "type": "multi_agent_coordination",
                    "goal_id": goal.goal_id,
                    "agents": goal.assigned_agents,
                    "coordination_type": "result_synchronization"
                })
        
        # Dependencies require handoff coordination
        for goal in goals:
            for dep_goal_id in goal.dependencies:
                dep_goal = next((g for g in goals if g.goal_id == dep_goal_id), None)
                if dep_goal and set(goal.assigned_agents) != set(dep_goal.assigned_agents):
                    coordination_reqs.append({
                        "type": "handoff_coordination",
                        "from_goal": dep_goal_id,
                        "to_goal": goal.goal_id,
                        "coordination_type": "dependency_handoff"
                    })
        
        return coordination_reqs
    
    def identify_integration_points(self, goals: List[Goal], allocation_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify points where agents need to coordinate"""
        integration_points = []
        
        # Find goals with dependencies that involve different agents
        for goal in goals:
            if goal.dependencies and len(goal.assigned_agents) > 1:
                integration_points.append({
                    "type": "multi_agent_goal",
                    "goal_id": goal.goal_id,
                    "agents_involved": goal.assigned_agents,
                    "coordination_type": "result_integration"
                })
        
        # Find handoff points between dependent goals
        for goal in goals:
            for dep_goal_id in goal.dependencies:
                dep_goal = next((g for g in goals if g.goal_id == dep_goal_id), None)
                if dep_goal and set(goal.assigned_agents) != set(dep_goal.assigned_agents):
                    integration_points.append({
                        "type": "handoff_point",
                        "from_goal": dep_goal_id,
                        "to_goal": goal.goal_id,
                        "from_agents": dep_goal.assigned_agents,
                        "to_agents": goal.assigned_agents,
                        "coordination_type": "result_handoff"
                    })
        
        return integration_points
    
    async def create_execution_plan(self, goals: List[Goal], allocation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Create detailed execution plan"""
        execution_plan = {
            "phases": [],
            "agent_schedules": {},
            "coordination_schedule": [],
            "milestones": [],
            "risk_factors": []
        }
        
        # Create phases based on dependencies
        phases = self.create_execution_phases(goals)
        execution_plan["phases"] = phases
        
        # Create agent schedules
        agent_schedules = self.create_agent_schedules(allocation_result)
        execution_plan["agent_schedules"] = agent_schedules
        
        # Schedule coordination points
        coordination_schedule = self.schedule_coordination_points(goals, allocation_result)
        execution_plan["coordination_schedule"] = coordination_schedule
        
        # Define milestones
        milestones = self.define_milestones(goals)
        execution_plan["milestones"] = milestones
        
        # Identify risk factors
        risk_factors = self.identify_risk_factors(goals, allocation_result)
        execution_plan["risk_factors"] = risk_factors
        
        return execution_plan
    
    def create_execution_phases(self, goals: List[Goal]) -> List[Dict[str, Any]]:
        """Create execution phases"""
        phases = []
        goal_dict = {goal.goal_id: goal for goal in goals}
        
        # Group goals by dependency level
        levels = {}
        for goal in goals:
            level = self.calculate_dependency_level(goal, goal_dict)
            if level not in levels:
                levels[level] = []
            levels[level].append(goal)
        
        # Create phases
        for level in sorted(levels.keys()):
            phases.append({
                "phase_id": level,
                "goals": [goal.goal_id for goal in levels[level]],
                "estimated_duration": max(goal.estimated_effort for goal in levels[level]),
                "parallel_execution": len(levels[level]) > 1
            })
        
        return phases
    
    def calculate_dependency_level(self, goal: Goal, goal_dict: Dict[str, Goal]) -> int:
        """Calculate dependency level of a goal"""
        if not goal.dependencies:
            return 0
        
        max_dep_level = 0
        for dep_id in goal.dependencies:
            if dep_id in goal_dict:
                dep_level = self.calculate_dependency_level(goal_dict[dep_id], goal_dict)
                max_dep_level = max(max_dep_level, dep_level)
        
        return max_dep_level + 1
    
    def create_agent_schedules(self, allocation_result: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """Create schedules for each agent"""
        agent_schedules = {}
        
        for agent_id, subgoal_ids in allocation_result.get("agent_assignments", {}).items():
            schedule = []
            current_time = 0
            
            for subgoal_id in subgoal_ids:
                subgoal = next((sg for sg in allocation_result.get("subgoals_created", []) 
                              if sg.subgoal_id == subgoal_id), None)
                
                if subgoal:
                    schedule.append({
                        "subgoal_id": subgoal_id,
                        "description": subgoal.description,
                        "start_time": current_time,
                        "duration": subgoal.estimated_time,
                        "end_time": current_time + subgoal.estimated_time
                    })
                    current_time += subgoal.estimated_time
            
            agent_schedules[agent_id] = schedule
        
        return agent_schedules
    
    def schedule_coordination_points(self, goals: List[Goal], allocation_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Schedule coordination points"""
        coordination_points = []
        
        # Add coordination at goal completion
        for goal in goals:
            if len(goal.assigned_agents) > 1:
                coordination_points.append({
                    "type": "goal_completion_sync",
                    "goal_id": goal.goal_id,
                    "agents": goal.assigned_agents,
                    "estimated_time": goal.estimated_effort,
                    "purpose": "Synchronize and integrate results"
                })
        
        return coordination_points
    
    def define_milestones(self, goals: List[Goal]) -> List[Dict[str, Any]]:
        """Define project milestones"""
        milestones = []
        
        # Milestone for each high-priority goal completion
        for goal in goals:
            if goal.priority in [GoalPriority.HIGH, GoalPriority.CRITICAL]:
                milestones.append({
                    "milestone_id": f"milestone_{goal.goal_id}",
                    "description": f"Completion of {goal.description}",
                    "goal_id": goal.goal_id,
                    "estimated_time": goal.estimated_effort,
                    "success_criteria": "Goal completed successfully with quality validation"
                })
        
        return milestones
    
    def identify_risk_factors(self, goals: List[Goal], allocation_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify potential risk factors"""
        risks = []
        
        # Unassigned goals risk
        unassigned = allocation_result.get("unassigned_goals", [])
        if unassigned:
            risks.append({
                "risk_type": "unassigned_goals",
                "description": f"{len(unassigned)} goals could not be assigned to agents",
                "impact": "high",
                "mitigation": "Find alternative agents or redistribute workload"
            })
        
        # Complex goals risk
        complex_goals = [goal for goal in goals if goal.complexity > 0.8]
        if complex_goals:
            risks.append({
                "risk_type": "high_complexity",
                "description": f"{len(complex_goals)} goals have high complexity",
                "impact": "medium",
                "mitigation": "Provide additional support and monitoring"
            })
        
        # Single point of failure risk
        single_agent_goals = [goal for goal in goals if len(goal.assigned_agents) == 1]
        if len(single_agent_goals) > len(goals) * 0.7:
            risks.append({
                "risk_type": "single_point_failure",
                "description": "Many goals depend on single agents",
                "impact": "medium",
                "mitigation": "Consider backup agent assignments"
            })
        
        return risks
    
    def analyze_goal_complexity(self, goals: List[Goal]) -> Dict[str, Any]:
        """Analyze complexity distribution of goals"""
        if not goals:
            return {"error": "No goals to analyze"}
        
        complexities = [goal.complexity for goal in goals]
        
        return {
            "average_complexity": sum(complexities) / len(complexities),
            "max_complexity": max(complexities),
            "min_complexity": min(complexities),
            "high_complexity_count": len([c for c in complexities if c > 0.7]),
            "medium_complexity_count": len([c for c in complexities if 0.4 <= c <= 0.7]),
            "low_complexity_count": len([c for c in complexities if c < 0.4])
        }