from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import heapq

class ResourceType(Enum):
    COMPUTATIONAL = "computational"
    MEMORY = "memory"
    TIME = "time"
    KNOWLEDGE_ACCESS = "knowledge_access"
    AGENT_ATTENTION = "agent_attention"

class Priority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class ResourceRequest:
    request_id: str
    agent_id: str
    resource_type: ResourceType
    amount_requested: float
    priority: Priority
    deadline: int
    justification: str
    flexibility: float  # 0-1, how flexible the request is

@dataclass
class ResourceAllocation:
    allocation_id: str
    agent_id: str
    resource_type: ResourceType
    amount_allocated: float
    start_time: int
    duration: int
    conditions: List[str]

class ResourceNegotiator:
    def __init__(self):
        self.available_resources = {
            ResourceType.COMPUTATIONAL: 100.0,
            ResourceType.MEMORY: 1000.0,
            ResourceType.TIME: 3600,  # seconds
            ResourceType.KNOWLEDGE_ACCESS: 50.0,
            ResourceType.AGENT_ATTENTION: 10.0
        }
        self.active_allocations = []
        self.negotiation_history = []
        self.conflict_resolution_strategies = []
        
    async def negotiate_resources(self, requests: List[ResourceRequest]) -> Dict[str, Any]:
        """Negotiate resource allocation among competing requests"""
        negotiation_result = {
            "total_requests": len(requests),
            "resource_allocations": [],
            "rejected_requests": [],
            "conflicts_resolved": 0,
            "negotiation_rounds": 0,
            "final_allocation_efficiency": 0.0
        }
        
        # Step 1: Initial resource allocation attempt
        initial_allocation = await self.initial_resource_allocation(requests)
        negotiation_result.update(initial_allocation)
        
        # Step 2: Identify and resolve conflicts
        conflicts = self.identify_resource_conflicts(requests, initial_allocation["resource_allocations"])
        if conflicts:
            conflict_resolution = await self.resolve_resource_conflicts(conflicts, requests)
            negotiation_result["conflicts_resolved"] = len(conflicts)
            negotiation_result["conflict_resolution"] = conflict_resolution
            
            # Update allocations based on conflict resolution
            if conflict_resolution["resolved_allocations"]:
                negotiation_result["resource_allocations"] = conflict_resolution["resolved_allocations"]
        
        # Step 3: Priority-based scheduling
        scheduling_result = await self.priority_based_scheduling(negotiation_result["resource_allocations"])
        negotiation_result["scheduling"] = scheduling_result
        
        # Step 4: Calculate efficiency
        efficiency = self.calculate_allocation_efficiency(requests, negotiation_result["resource_allocations"])
        negotiation_result["final_allocation_efficiency"] = efficiency
        
        return negotiation_result
    
    async def initial_resource_allocation(self, requests: List[ResourceRequest]) -> Dict[str, Any]:
        """Perform initial resource allocation"""
        allocation_result = {
            "resource_allocations": [],
            "rejected_requests": [],
            "partial_allocations": [],
            "resource_utilization": {}
        }
        
        # Sort requests by priority and deadline
        sorted_requests = sorted(requests, key=lambda r: (r.priority.value, r.deadline), reverse=True)
        
        current_resources = self.available_resources.copy()
        
        for request in sorted_requests:
            allocation = self.attempt_allocation(request, current_resources)
            
            if allocation["status"] == "allocated":
                resource_allocation = ResourceAllocation(
                    allocation_id=f"alloc_{len(allocation_result['resource_allocations']) + 1}",
                    agent_id=request.agent_id,
                    resource_type=request.resource_type,
                    amount_allocated=allocation["amount"],
                    start_time=0,  # Immediate start
                    duration=allocation.get("duration", 3600),
                    conditions=allocation.get("conditions", [])
                )
                
                allocation_result["resource_allocations"].append(resource_allocation)
                current_resources[request.resource_type] -= allocation["amount"]
                
            elif allocation["status"] == "partial":
                allocation_result["partial_allocations"].append({
                    "request_id": request.request_id,
                    "requested": request.amount_requested,
                    "allocated": allocation["amount"]
                })
            else:
                allocation_result["rejected_requests"].append({
                    "request_id": request.request_id,
                    "reason": allocation.get("reason", "Insufficient resources")
                })
        
        # Calculate resource utilization
        for resource_type, available in self.available_resources.items():
            used = available - current_resources[resource_type]
            allocation_result["resource_utilization"][resource_type.value] = {
                "used": used,
                "available": available,
                "utilization_rate": used / available if available > 0 else 0
            }
        
        return allocation_result
    
    def attempt_allocation(self, request: ResourceRequest, available_resources: Dict[ResourceType, float]) -> Dict[str, Any]:
        """Attempt to allocate resources for a single request"""
        resource_type = request.resource_type
        requested_amount = request.amount_requested
        available_amount = available_resources.get(resource_type, 0)
        
        if available_amount >= requested_amount:
            # Full allocation possible
            return {
                "status": "allocated",
                "amount": requested_amount,
                "duration": 3600,  # Default duration
                "conditions": []
            }
        elif available_amount > 0 and request.flexibility > 0.5:
            # Partial allocation possible if request is flexible
            partial_amount = available_amount * request.flexibility
            return {
                "status": "partial",
                "amount": partial_amount,
                "duration": 3600,
                "conditions": ["Partial allocation due to resource constraints"]
            }
        else:
            # No allocation possible
            return {
                "status": "rejected",
                "reason": f"Insufficient {resource_type.value} resources"
            }
    
    def identify_resource_conflicts(self, requests: List[ResourceRequest], 
                                  allocations: List[ResourceAllocation]) -> List[Dict[str, Any]]:
        """Identify conflicts in resource allocation"""
        conflicts = []
        
        # Group allocations by resource type
        resource_groups = {}
        for allocation in allocations:
            resource_type = allocation.resource_type
            if resource_type not in resource_groups:
                resource_groups[resource_type] = []
            resource_groups[resource_type].append(allocation)
        
        # Check for over-allocation
        for resource_type, group_allocations in resource_groups.items():
            total_allocated = sum(alloc.amount_allocated for alloc in group_allocations)
            available = self.available_resources.get(resource_type, 0)
            
            if total_allocated > available:
                conflicts.append({
                    "conflict_type": "over_allocation",
                    "resource_type": resource_type,
                    "total_allocated": total_allocated,
                    "available": available,
                    "excess": total_allocated - available,
                    "affected_allocations": [alloc.allocation_id for alloc in group_allocations]
                })
        
        # Check for timing conflicts (simplified)
        for resource_type, group_allocations in resource_groups.items():
            if len(group_allocations) > 1:
                conflicts.append({
                    "conflict_type": "concurrent_access",
                    "resource_type": resource_type,
                    "concurrent_allocations": len(group_allocations),
                    "affected_allocations": [alloc.allocation_id for alloc in group_allocations]
                })
        
        return conflicts
    
    async def resolve_resource_conflicts(self, conflicts: List[Dict[str, Any]], 
                                       original_requests: List[ResourceRequest]) -> Dict[str, Any]:
        """Resolve identified resource conflicts"""
        resolution_result = {
            "resolved_conflicts": 0,
            "resolution_strategies_used": [],
            "resolved_allocations": [],
            "unresolved_conflicts": []
        }
        
        request_dict = {req.request_id: req for req in original_requests}
        
        for conflict in conflicts:
            if conflict["conflict_type"] == "over_allocation":
                resolution = await self.resolve_over_allocation(conflict, request_dict)
                if resolution["success"]:
                    resolution_result["resolved_conflicts"] += 1
                    resolution_result["resolution_strategies_used"].append("priority_reallocation")
                    resolution_result["resolved_allocations"].extend(resolution["new_allocations"])
                else:
                    resolution_result["unresolved_conflicts"].append(conflict)
            
            elif conflict["conflict_type"] == "concurrent_access":
                resolution = await self.resolve_concurrent_access(conflict, request_dict)
                if resolution["success"]:
                    resolution_result["resolved_conflicts"] += 1
                    resolution_result["resolution_strategies_used"].append("time_slicing")
                    resolution_result["resolved_allocations"].extend(resolution["new_allocations"])
                else:
                    resolution_result["unresolved_conflicts"].append(conflict)
        
        return resolution_result
    
    async def resolve_over_allocation(self, conflict: Dict[str, Any], 
                                    requests: Dict[str, ResourceRequest]) -> Dict[str, Any]:
        """Resolve over-allocation conflict using priority-based reallocation"""
        resource_type = conflict["resource_type"]
        available = conflict["available"]
        affected_allocations = conflict["affected_allocations"]
        
        # Get requests for affected allocations
        affected_requests = []
        for alloc_id in affected_allocations:
            # Find corresponding request (simplified lookup)
            for request in requests.values():
                if request.resource_type == resource_type:
                    affected_requests.append(request)
                    break
        
        if not affected_requests:
            return {"success": False, "reason": "No corresponding requests found"}
        
        # Sort by priority and flexibility
        sorted_requests = sorted(affected_requests, 
                               key=lambda r: (r.priority.value, 1 - r.flexibility), 
                               reverse=True)
        
        # Reallocate based on priority
        new_allocations = []
        remaining_resources = available
        
        for request in sorted_requests:
            if remaining_resources <= 0:
                break
            
            allocated_amount = min(request.amount_requested, remaining_resources)
            
            if allocated_amount > 0:
                new_allocation = ResourceAllocation(
                    allocation_id=f"resolved_alloc_{len(new_allocations) + 1}",
                    agent_id=request.agent_id,
                    resource_type=request.resource_type,
                    amount_allocated=allocated_amount,
                    start_time=0,
                    duration=3600,
                    conditions=["Resolved through priority reallocation"]
                )
                new_allocations.append(new_allocation)
                remaining_resources -= allocated_amount
        
        return {
            "success": True,
            "new_allocations": new_allocations,
            "strategy": "priority_reallocation"
        }
    
    async def resolve_concurrent_access(self, conflict: Dict[str, Any], 
                                      requests: Dict[str, ResourceRequest]) -> Dict[str, Any]:
        """Resolve concurrent access conflict using time slicing"""
        resource_type = conflict["resource_type"]
        concurrent_count = conflict["concurrent_allocations"]
        
        # Simple time slicing - divide time equally
        time_slice = 3600 // concurrent_count  # Divide total time
        
        new_allocations = []
        current_start_time = 0
        
        # Create time-sliced allocations
        for i in range(concurrent_count):
            # Find a request for this allocation (simplified)
            request = next((req for req in requests.values() 
                          if req.resource_type == resource_type), None)
            
            if request:
                new_allocation = ResourceAllocation(
                    allocation_id=f"timesliced_alloc_{i + 1}",
                    agent_id=request.agent_id,
                    resource_type=resource_type,
                    amount_allocated=request.amount_requested,
                    start_time=current_start_time,
                    duration=time_slice,
                    conditions=["Time-sliced allocation"]
                )
                new_allocations.append(new_allocation)
                current_start_time += time_slice
        
        return {
            "success": True,
            "new_allocations": new_allocations,
            "strategy": "time_slicing"
        }
    
    async def priority_based_scheduling(self, allocations: List[ResourceAllocation]) -> Dict[str, Any]:
        """Create priority-based scheduling for resource allocations"""
        scheduling_result = {
            "schedule": [],
            "total_duration": 0,
            "resource_queues": {},
            "scheduling_efficiency": 0.0
        }
        
        # Group allocations by resource type
        resource_groups = {}
        for allocation in allocations:
            resource_type = allocation.resource_type
            if resource_type not in resource_groups:
                resource_groups[resource_type] = []
            resource_groups[resource_type].append(allocation)
        
        # Create schedule for each resource type
        total_scheduled_time = 0
        
        for resource_type, group_allocations in resource_groups.items():
            # Sort by start time and priority
            sorted_allocations = sorted(group_allocations, 
                                      key=lambda a: (a.start_time, -a.amount_allocated))
            
            resource_schedule = []
            current_time = 0
            
            for allocation in sorted_allocations:
                scheduled_item = {
                    "allocation_id": allocation.allocation_id,
                    "agent_id": allocation.agent_id,
                    "start_time": current_time,
                    "end_time": current_time + allocation.duration,
                    "resource_amount": allocation.amount_allocated
                }
                resource_schedule.append(scheduled_item)
                current_time += allocation.duration
            
            scheduling_result["resource_queues"][resource_type.value] = resource_schedule
            total_scheduled_time = max(total_scheduled_time, current_time)
        
        scheduling_result["total_duration"] = total_scheduled_time
        
        # Calculate scheduling efficiency
        if allocations:
            avg_allocation_time = sum(alloc.duration for alloc in allocations) / len(allocations)
            scheduling_result["scheduling_efficiency"] = avg_allocation_time / total_scheduled_time if total_scheduled_time > 0 else 0
        
        return scheduling_result
    
    def calculate_allocation_efficiency(self, requests: List[ResourceRequest], 
                                     allocations: List[ResourceAllocation]) -> float:
        """Calculate overall allocation efficiency"""
        if not requests or not allocations:
            return 0.0
        
        # Calculate satisfaction rate
        satisfied_requests = 0
        total_satisfaction = 0.0
        
        for request in requests:
            # Find corresponding allocation
            matching_allocations = [alloc for alloc in allocations 
                                  if alloc.agent_id == request.agent_id and 
                                     alloc.resource_type == request.resource_type]
            
            if matching_allocations:
                allocation = matching_allocations[0]
                satisfaction = min(allocation.amount_allocated / request.amount_requested, 1.0)
                total_satisfaction += satisfaction
                if satisfaction > 0.8:  # Consider 80%+ as satisfied
                    satisfied_requests += 1
        
        # Combine satisfaction metrics
        satisfaction_rate = satisfied_requests / len(requests)
        avg_satisfaction = total_satisfaction / len(requests)
        
        return (satisfaction_rate + avg_satisfaction) / 2
    
    async def implement_resource_allocation_algorithms(self, requests: List[ResourceRequest]) -> Dict[str, Any]:
        """Implement various resource allocation algorithms"""
        algorithms_result = {
            "first_come_first_served": await self.fcfs_allocation(requests),
            "priority_based": await self.priority_allocation(requests),
            "fair_share": await self.fair_share_allocation(requests),
            "best_fit": await self.best_fit_allocation(requests)
        }
        
        # Compare algorithm performance
        performance_comparison = self.compare_allocation_algorithms(algorithms_result)
        algorithms_result["performance_comparison"] = performance_comparison
        
        return algorithms_result
    
    async def fcfs_allocation(self, requests: List[ResourceRequest]) -> Dict[str, Any]:
        """First Come First Served allocation"""
        # Sort by request order (simplified - using request_id)
        sorted_requests = sorted(requests, key=lambda r: r.request_id)
        return await self.initial_resource_allocation(sorted_requests)
    
    async def priority_allocation(self, requests: List[ResourceRequest]) -> Dict[str, Any]:
        """Priority-based allocation"""
        sorted_requests = sorted(requests, key=lambda r: r.priority.value, reverse=True)
        return await self.initial_resource_allocation(sorted_requests)
    
    async def fair_share_allocation(self, requests: List[ResourceRequest]) -> Dict[str, Any]:
        """Fair share allocation"""
        # Group requests by agent
        agent_requests = {}
        for request in requests:
            if request.agent_id not in agent_requests:
                agent_requests[request.agent_id] = []
            agent_requests[request.agent_id].append(request)
        
        # Allocate fairly among agents
        fair_requests = []
        max_requests = max(len(reqs) for reqs in agent_requests.values()) if agent_requests else 0
        
        for i in range(max_requests):
            for agent_id, reqs in agent_requests.items():
                if i < len(reqs):
                    fair_requests.append(reqs[i])
        
        return await self.initial_resource_allocation(fair_requests)
    
    async def best_fit_allocation(self, requests: List[ResourceRequest]) -> Dict[str, Any]:
        """Best fit allocation based on resource requirements"""
        # Sort by resource amount (ascending) for best fit
        sorted_requests = sorted(requests, key=lambda r: r.amount_requested)
        return await self.initial_resource_allocation(sorted_requests)
    
    def compare_allocation_algorithms(self, algorithms_result: Dict[str, Any]) -> Dict[str, Any]:
        """Compare performance of different allocation algorithms"""
        comparison = {
            "efficiency_scores": {},
            "resource_utilization": {},
            "rejection_rates": {},
            "best_algorithm": None
        }
        
        best_efficiency = 0
        best_algorithm = None
        
        for algorithm_name, result in algorithms_result.items():
            if algorithm_name == "performance_comparison":
                continue
            
            # Calculate metrics
            total_requests = result.get("total_requests", 0)
            rejected_count = len(result.get("rejected_requests", []))
            rejection_rate = rejected_count / total_requests if total_requests > 0 else 0
            
            # Resource utilization
            utilization = result.get("resource_utilization", {})
            avg_utilization = sum(res["utilization_rate"] for res in utilization.values()) / len(utilization) if utilization else 0
            
            comparison["efficiency_scores"][algorithm_name] = avg_utilization
            comparison["rejection_rates"][algorithm_name] = rejection_rate
            comparison["resource_utilization"][algorithm_name] = avg_utilization
            
            # Track best algorithm
            if avg_utilization > best_efficiency:
                best_efficiency = avg_utilization
                best_algorithm = algorithm_name
        
        comparison["best_algorithm"] = best_algorithm
        return comparison
    
    def get_negotiation_statistics(self) -> Dict[str, Any]:
        """Get resource negotiation statistics"""
        if not self.negotiation_history:
            return {"no_data": True}
        
        total_negotiations = len(self.negotiation_history)
        successful_negotiations = sum(1 for neg in self.negotiation_history 
                                    if neg.get("final_allocation_efficiency", 0) > 0.7)
        
        return {
            "total_negotiations": total_negotiations,
            "success_rate": successful_negotiations / total_negotiations if total_negotiations > 0 else 0,
            "average_efficiency": sum(neg.get("final_allocation_efficiency", 0) 
                                    for neg in self.negotiation_history) / total_negotiations if total_negotiations > 0 else 0,
            "most_contested_resource": "computational",  # Would calculate from actual data
            "average_conflicts_per_negotiation": sum(neg.get("conflicts_resolved", 0) 
                                                   for neg in self.negotiation_history) / total_negotiations if total_negotiations > 0 else 0
        }