from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio

class VoteType(Enum):
    MAJORITY = "majority"
    UNANIMOUS = "unanimous"
    WEIGHTED = "weighted"
    THRESHOLD = "threshold"

@dataclass
class AgentBelief:
    agent_id: str
    belief_content: Dict[str, Any]
    confidence: float
    evidence: List[str]
    timestamp: str

@dataclass
class Vote:
    agent_id: str
    proposal_id: str
    vote_value: Any
    confidence: float
    reasoning: str

class ConsensusManager:
    def __init__(self):
        self.active_proposals = {}
        self.voting_history = []
        self.agent_beliefs = {}
        self.consensus_threshold = 0.7
        
    async def achieve_consensus(self, proposal: Dict[str, Any], participating_agents: List[str], 
                              vote_type: VoteType = VoteType.MAJORITY) -> Dict[str, Any]:
        """Achieve consensus among agents for a proposal"""
        consensus_result = {
            "proposal": proposal,
            "participating_agents": participating_agents,
            "vote_type": vote_type.value,
            "voting_results": {},
            "consensus_achieved": False,
            "final_decision": None,
            "confidence_level": 0.0,
            "dissenting_agents": [],
            "consensus_process": []
        }
        
        # Step 1: Collect votes from agents
        votes = await self.collect_votes(proposal, participating_agents)
        consensus_result["voting_results"] = votes
        
        # Step 2: Apply voting protocol
        decision_result = await self.apply_voting_protocol(votes, vote_type)
        consensus_result.update(decision_result)
        
        # Step 3: Verify agreement
        agreement_verification = await self.verify_agreement(votes, decision_result)
        consensus_result["agreement_verification"] = agreement_verification
        
        # Step 4: Handle dissent if any
        if not consensus_result["consensus_achieved"]:
            dissent_handling = await self.handle_dissent(votes, proposal)
            consensus_result["dissent_handling"] = dissent_handling
        
        return consensus_result
    
    async def collect_votes(self, proposal: Dict[str, Any], agents: List[str]) -> Dict[str, Vote]:
        """Collect votes from participating agents"""
        votes = {}
        proposal_id = proposal.get("proposal_id", "unknown")
        
        for agent_id in agents:
            # Simulate agent voting (in production, would call actual agents)
            vote = await self.simulate_agent_vote(agent_id, proposal)
            votes[agent_id] = vote
        
        return votes
    
    async def simulate_agent_vote(self, agent_id: str, proposal: Dict[str, Any]) -> Vote:
        """Simulate agent voting behavior"""
        proposal_content = proposal.get("content", "")
        proposal_id = proposal.get("proposal_id", "unknown")
        
        # Simple voting simulation based on agent type
        if "organization" in agent_id.lower():
            # Organization agents tend to be conservative
            vote_value = "approve" if "policy" in proposal_content.lower() else "neutral"
            confidence = 0.7
            reasoning = "Organization policy alignment"
        elif "department" in agent_id.lower():
            # Department agents focus on domain expertise
            vote_value = "approve" if any(word in proposal_content.lower() 
                                       for word in ["hr", "finance", "it"]) else "neutral"
            confidence = 0.8
            reasoning = "Domain expertise assessment"
        elif "reasoning" in agent_id.lower():
            # Reasoning agents analyze logical consistency
            vote_value = "approve" if "analysis" in proposal_content.lower() else "approve"
            confidence = 0.9
            reasoning = "Logical analysis completed"
        else:
            # Default voting behavior
            vote_value = "approve"
            confidence = 0.6
            reasoning = "General assessment"
        
        return Vote(
            agent_id=agent_id,
            proposal_id=proposal_id,
            vote_value=vote_value,
            confidence=confidence,
            reasoning=reasoning
        )
    
    async def apply_voting_protocol(self, votes: Dict[str, Vote], vote_type: VoteType) -> Dict[str, Any]:
        """Apply specified voting protocol"""
        if vote_type == VoteType.MAJORITY:
            return await self.majority_voting(votes)
        elif vote_type == VoteType.UNANIMOUS:
            return await self.unanimous_voting(votes)
        elif vote_type == VoteType.WEIGHTED:
            return await self.weighted_voting(votes)
        elif vote_type == VoteType.THRESHOLD:
            return await self.threshold_voting(votes)
        else:
            return {"error": "Unknown voting protocol"}
    
    async def majority_voting(self, votes: Dict[str, Vote]) -> Dict[str, Any]:
        """Implement majority voting protocol"""
        vote_counts = {}
        total_votes = len(votes)
        
        # Count votes
        for vote in votes.values():
            vote_value = vote.vote_value
            vote_counts[vote_value] = vote_counts.get(vote_value, 0) + 1
        
        # Find majority
        majority_threshold = total_votes / 2
        winning_option = None
        max_votes = 0
        
        for option, count in vote_counts.items():
            if count > max_votes:
                max_votes = count
                winning_option = option
        
        consensus_achieved = max_votes > majority_threshold
        
        # Calculate confidence
        confidence = max_votes / total_votes if total_votes > 0 else 0
        
        return {
            "consensus_achieved": consensus_achieved,
            "final_decision": winning_option if consensus_achieved else None,
            "confidence_level": confidence,
            "vote_distribution": vote_counts,
            "winning_votes": max_votes,
            "total_votes": total_votes
        }
    
    async def unanimous_voting(self, votes: Dict[str, Vote]) -> Dict[str, Any]:
        """Implement unanimous voting protocol"""
        if not votes:
            return {"consensus_achieved": False, "final_decision": None, "confidence_level": 0.0}
        
        # Check if all votes are the same
        vote_values = [vote.vote_value for vote in votes.values()]
        unanimous_decision = vote_values[0] if len(set(vote_values)) == 1 else None
        
        consensus_achieved = unanimous_decision is not None
        confidence = 1.0 if consensus_achieved else 0.0
        
        return {
            "consensus_achieved": consensus_achieved,
            "final_decision": unanimous_decision,
            "confidence_level": confidence,
            "unanimous_vote": unanimous_decision,
            "dissenting_count": len(vote_values) - vote_values.count(vote_values[0]) if vote_values else 0
        }
    
    async def weighted_voting(self, votes: Dict[str, Vote]) -> Dict[str, Any]:
        """Implement weighted voting protocol"""
        weighted_scores = {}
        total_weight = 0
        
        # Calculate weighted scores
        for vote in votes.values():
            weight = vote.confidence  # Use confidence as weight
            vote_value = vote.vote_value
            
            if vote_value not in weighted_scores:
                weighted_scores[vote_value] = 0
            
            weighted_scores[vote_value] += weight
            total_weight += weight
        
        # Find winning option
        winning_option = max(weighted_scores.items(), key=lambda x: x[1]) if weighted_scores else (None, 0)
        
        consensus_achieved = winning_option[1] > total_weight / 2 if total_weight > 0 else False
        confidence = winning_option[1] / total_weight if total_weight > 0 else 0
        
        return {
            "consensus_achieved": consensus_achieved,
            "final_decision": winning_option[0] if consensus_achieved else None,
            "confidence_level": confidence,
            "weighted_scores": weighted_scores,
            "total_weight": total_weight
        }
    
    async def threshold_voting(self, votes: Dict[str, Vote]) -> Dict[str, Any]:
        """Implement threshold-based voting protocol"""
        vote_counts = {}
        confidence_sums = {}
        
        # Count votes and sum confidences
        for vote in votes.values():
            vote_value = vote.vote_value
            vote_counts[vote_value] = vote_counts.get(vote_value, 0) + 1
            confidence_sums[vote_value] = confidence_sums.get(vote_value, 0) + vote.confidence
        
        # Check threshold
        total_votes = len(votes)
        threshold_count = int(total_votes * self.consensus_threshold)
        
        winning_option = None
        max_votes = 0
        
        for option, count in vote_counts.items():
            if count >= threshold_count and count > max_votes:
                max_votes = count
                winning_option = option
        
        consensus_achieved = winning_option is not None
        confidence = confidence_sums.get(winning_option, 0) / max_votes if max_votes > 0 else 0
        
        return {
            "consensus_achieved": consensus_achieved,
            "final_decision": winning_option,
            "confidence_level": confidence,
            "threshold_required": threshold_count,
            "votes_received": max_votes,
            "vote_distribution": vote_counts
        }
    
    async def verify_agreement(self, votes: Dict[str, Vote], decision_result: Dict[str, Any]) -> Dict[str, Any]:
        """Verify agreement and identify dissenting agents"""
        verification_result = {
            "agreement_level": 0.0,
            "dissenting_agents": [],
            "supporting_agents": [],
            "neutral_agents": [],
            "agreement_strength": "weak"
        }
        
        final_decision = decision_result.get("final_decision")
        if not final_decision:
            return verification_result
        
        # Categorize agents based on their votes
        for agent_id, vote in votes.items():
            if vote.vote_value == final_decision:
                verification_result["supporting_agents"].append({
                    "agent_id": agent_id,
                    "confidence": vote.confidence,
                    "reasoning": vote.reasoning
                })
            elif vote.vote_value == "neutral":
                verification_result["neutral_agents"].append(agent_id)
            else:
                verification_result["dissenting_agents"].append({
                    "agent_id": agent_id,
                    "vote": vote.vote_value,
                    "confidence": vote.confidence,
                    "reasoning": vote.reasoning
                })
        
        # Calculate agreement level
        total_agents = len(votes)
        supporting_count = len(verification_result["supporting_agents"])
        verification_result["agreement_level"] = supporting_count / total_agents if total_agents > 0 else 0
        
        # Determine agreement strength
        if verification_result["agreement_level"] >= 0.9:
            verification_result["agreement_strength"] = "very_strong"
        elif verification_result["agreement_level"] >= 0.7:
            verification_result["agreement_strength"] = "strong"
        elif verification_result["agreement_level"] >= 0.5:
            verification_result["agreement_strength"] = "moderate"
        else:
            verification_result["agreement_strength"] = "weak"
        
        return verification_result
    
    async def handle_dissent(self, votes: Dict[str, Vote], proposal: Dict[str, Any]) -> Dict[str, Any]:
        """Handle dissenting opinions and attempt resolution"""
        dissent_handling = {
            "dissent_resolution_attempted": True,
            "resolution_strategies": [],
            "compromise_proposals": [],
            "final_resolution": None
        }
        
        # Identify dissenting reasons
        dissenting_votes = [vote for vote in votes.values() if vote.vote_value != "approve"]
        
        if not dissenting_votes:
            dissent_handling["dissent_resolution_attempted"] = False
            return dissent_handling
        
        # Analyze dissent patterns
        dissent_reasons = [vote.reasoning for vote in dissenting_votes]
        
        # Strategy 1: Address specific concerns
        if any("policy" in reason.lower() for reason in dissent_reasons):
            dissent_handling["resolution_strategies"].append({
                "strategy": "policy_clarification",
                "description": "Provide additional policy context and clarification"
            })
        
        # Strategy 2: Compromise proposal
        if len(dissenting_votes) < len(votes) / 2:  # Minority dissent
            compromise = {
                "type": "modified_proposal",
                "description": "Incorporate dissenting concerns into modified proposal",
                "modifications": ["Address policy concerns", "Provide additional evidence"]
            }
            dissent_handling["compromise_proposals"].append(compromise)
        
        # Strategy 3: Escalation
        if len(dissenting_votes) >= len(votes) / 2:  # Majority dissent
            dissent_handling["resolution_strategies"].append({
                "strategy": "escalation",
                "description": "Escalate to higher authority or seek additional input"
            })
        
        return dissent_handling
    
    async def merge_beliefs(self, agent_beliefs: List[AgentBelief]) -> Dict[str, Any]:
        """Merge beliefs from multiple agents"""
        if not agent_beliefs:
            return {"merged_belief": None, "confidence": 0.0}
        
        merged_result = {
            "merged_belief": {},
            "confidence": 0.0,
            "contributing_agents": [],
            "belief_conflicts": [],
            "consensus_areas": []
        }
        
        # Collect all belief keys
        all_keys = set()
        for belief in agent_beliefs:
            all_keys.update(belief.belief_content.keys())
        
        # Merge beliefs for each key
        for key in all_keys:
            key_beliefs = []
            for belief in agent_beliefs:
                if key in belief.belief_content:
                    key_beliefs.append({
                        "agent_id": belief.agent_id,
                        "value": belief.belief_content[key],
                        "confidence": belief.confidence
                    })
            
            # Merge beliefs for this key
            merged_value, confidence, conflicts = self.merge_key_beliefs(key_beliefs)
            
            merged_result["merged_belief"][key] = merged_value
            if conflicts:
                merged_result["belief_conflicts"].append({
                    "key": key,
                    "conflicts": conflicts
                })
            else:
                merged_result["consensus_areas"].append(key)
        
        # Calculate overall confidence
        total_confidence = sum(belief.confidence for belief in agent_beliefs)
        merged_result["confidence"] = total_confidence / len(agent_beliefs)
        merged_result["contributing_agents"] = [belief.agent_id for belief in agent_beliefs]
        
        return merged_result
    
    def merge_key_beliefs(self, key_beliefs: List[Dict[str, Any]]) -> Tuple[Any, float, List[Dict[str, Any]]]:
        """Merge beliefs for a specific key"""
        if not key_beliefs:
            return None, 0.0, []
        
        if len(key_beliefs) == 1:
            return key_beliefs[0]["value"], key_beliefs[0]["confidence"], []
        
        # Check for conflicts
        values = [belief["value"] for belief in key_beliefs]
        unique_values = list(set(str(v) for v in values))  # Convert to string for comparison
        
        if len(unique_values) == 1:
            # No conflict - all agents agree
            avg_confidence = sum(belief["confidence"] for belief in key_beliefs) / len(key_beliefs)
            return key_beliefs[0]["value"], avg_confidence, []
        
        # Conflict exists - use weighted average or highest confidence value
        conflicts = []
        for i, belief1 in enumerate(key_beliefs):
            for belief2 in key_beliefs[i+1:]:
                if str(belief1["value"]) != str(belief2["value"]):
                    conflicts.append({
                        "agent1": belief1["agent_id"],
                        "value1": belief1["value"],
                        "agent2": belief2["agent_id"],
                        "value2": belief2["value"]
                    })
        
        # Choose value with highest confidence
        best_belief = max(key_beliefs, key=lambda b: b["confidence"])
        return best_belief["value"], best_belief["confidence"], conflicts
    
    async def create_voting_proposal(self, content: str, proposal_type: str = "decision") -> Dict[str, Any]:
        """Create a voting proposal"""
        proposal = {
            "proposal_id": str(len(self.active_proposals) + 1),
            "content": content,
            "type": proposal_type,
            "created_at": "now",  # Would use actual timestamp
            "status": "active",
            "voting_deadline": "future",  # Would set actual deadline
            "required_votes": None,
            "vote_type": VoteType.MAJORITY.value
        }
        
        self.active_proposals[proposal["proposal_id"]] = proposal
        return proposal
    
    def get_consensus_statistics(self) -> Dict[str, Any]:
        """Get consensus achievement statistics"""
        if not self.voting_history:
            return {"no_data": True}
        
        total_votes = len(self.voting_history)
        successful_consensus = sum(1 for vote in self.voting_history if vote.get("consensus_achieved", False))
        
        return {
            "total_voting_sessions": total_votes,
            "successful_consensus_rate": successful_consensus / total_votes if total_votes > 0 else 0,
            "average_confidence": sum(vote.get("confidence_level", 0) for vote in self.voting_history) / total_votes if total_votes > 0 else 0,
            "most_common_vote_type": "majority",  # Would calculate from actual data
            "consensus_efficiency": successful_consensus / total_votes if total_votes > 0 else 0
        }