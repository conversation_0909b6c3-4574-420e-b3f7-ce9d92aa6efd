from typing import Dict, List, Any, Tuple
import re
from datetime import datetime

class ReasoningValidator:
    def __init__(self):
        self.consistency_rules = []
        self.factual_knowledge = {}
        self.logical_patterns = {
            "contradiction": [r"not.*but", r"however.*opposite", r"although.*contrary"],
            "causation": [r"because.*therefore", r"since.*thus", r"due to.*result"],
            "implication": [r"if.*then", r"when.*will", r"assuming.*follows"]
        }
    
    async def validate_reasoning(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive reasoning validation"""
        validation_result = {
            "consistency_check": await self.check_consistency(reasoning_result),
            "logical_validity": await self.assess_logical_validity(reasoning_result),
            "factual_accuracy": await self.verify_factual_accuracy(reasoning_result),
            "overall_validity": 0.0,
            "validation_issues": [],
            "confidence_adjustment": 0.0
        }
        
        # Calculate overall validity
        scores = [
            validation_result["consistency_check"]["score"],
            validation_result["logical_validity"]["score"],
            validation_result["factual_accuracy"]["score"]
        ]
        validation_result["overall_validity"] = sum(scores) / len(scores)
        
        # Determine confidence adjustment
        if validation_result["overall_validity"] > 0.8:
            validation_result["confidence_adjustment"] = 0.1
        elif validation_result["overall_validity"] < 0.5:
            validation_result["confidence_adjustment"] = -0.2
        
        return validation_result
    
    async def check_consistency(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Check internal consistency of reasoning"""
        consistency_result = {
            "score": 0.0,
            "issues": [],
            "contradictions_found": 0,
            "consistency_level": "unknown"
        }
        
        # Extract reasoning steps/content
        reasoning_content = self.extract_reasoning_content(reasoning_result)
        
        if not reasoning_content:
            consistency_result["score"] = 0.5
            consistency_result["consistency_level"] = "insufficient_data"
            return consistency_result
        
        # Check for contradictions
        contradictions = self.find_contradictions(reasoning_content)
        consistency_result["contradictions_found"] = len(contradictions)
        consistency_result["issues"].extend(contradictions)
        
        # Check for logical flow
        flow_issues = self.check_logical_flow(reasoning_content)
        consistency_result["issues"].extend(flow_issues)
        
        # Calculate consistency score
        base_score = 1.0
        base_score -= len(contradictions) * 0.3
        base_score -= len(flow_issues) * 0.1
        
        consistency_result["score"] = max(0.0, base_score)
        consistency_result["consistency_level"] = self.determine_consistency_level(consistency_result["score"])
        
        return consistency_result
    
    async def assess_logical_validity(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Assess logical validity of reasoning"""
        validity_result = {
            "score": 0.0,
            "logical_patterns_found": [],
            "validity_issues": [],
            "reasoning_structure": "unknown"
        }
        
        reasoning_content = self.extract_reasoning_content(reasoning_result)
        
        if not reasoning_content:
            validity_result["score"] = 0.5
            return validity_result
        
        # Analyze logical patterns
        for pattern_type, patterns in self.logical_patterns.items():
            for content in reasoning_content:
                for pattern in patterns:
                    if re.search(pattern, content.lower()):
                        validity_result["logical_patterns_found"].append({
                            "type": pattern_type,
                            "pattern": pattern,
                            "content": content[:100]
                        })
        
        # Check argument structure
        structure_score = self.analyze_argument_structure(reasoning_content)
        
        # Check for logical fallacies
        fallacies = self.detect_logical_fallacies(reasoning_content)
        validity_result["validity_issues"].extend(fallacies)
        
        # Calculate validity score
        pattern_score = min(len(validity_result["logical_patterns_found"]) * 0.2, 0.6)
        fallacy_penalty = len(fallacies) * 0.2
        
        validity_result["score"] = max(0.0, structure_score + pattern_score - fallacy_penalty)
        validity_result["reasoning_structure"] = self.classify_reasoning_structure(reasoning_content)
        
        return validity_result
    
    async def verify_factual_accuracy(self, reasoning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Verify factual accuracy of reasoning"""
        accuracy_result = {
            "score": 0.0,
            "factual_claims": [],
            "verified_facts": 0,
            "unverified_facts": 0,
            "accuracy_issues": []
        }
        
        reasoning_content = self.extract_reasoning_content(reasoning_result)
        
        # Extract factual claims
        factual_claims = self.extract_factual_claims(reasoning_content)
        accuracy_result["factual_claims"] = factual_claims
        
        # Verify each claim
        for claim in factual_claims:
            verification = await self.verify_claim(claim)
            
            if verification["verified"]:
                accuracy_result["verified_facts"] += 1
            else:
                accuracy_result["unverified_facts"] += 1
                accuracy_result["accuracy_issues"].append({
                    "claim": claim,
                    "issue": verification.get("issue", "Cannot verify")
                })
        
        # Calculate accuracy score
        total_claims = len(factual_claims)
        if total_claims > 0:
            accuracy_result["score"] = accuracy_result["verified_facts"] / total_claims
        else:
            accuracy_result["score"] = 0.7  # Neutral score for no claims
        
        return accuracy_result
    
    def extract_reasoning_content(self, reasoning_result: Dict[str, Any]) -> List[str]:
        """Extract textual content from reasoning result"""
        content = []
        
        # Extract from different possible structures
        if "final_reasoning" in reasoning_result:
            final_reasoning = reasoning_result["final_reasoning"]
            if isinstance(final_reasoning, dict):
                if "conclusion" in final_reasoning:
                    content.append(str(final_reasoning["conclusion"]))
                if "reasoning_chain" in final_reasoning:
                    for step in final_reasoning["reasoning_chain"]:
                        if isinstance(step, dict) and "reasoning" in step:
                            content.append(str(step["reasoning"]))
        
        if "reasoning_steps" in reasoning_result:
            for step in reasoning_result["reasoning_steps"]:
                if hasattr(step, 'description'):
                    content.append(step.description)
                elif isinstance(step, dict) and "description" in step:
                    content.append(step["description"])
        
        if "response" in reasoning_result:
            content.append(str(reasoning_result["response"]))
        
        return [c for c in content if c and len(c.strip()) > 0]
    
    def find_contradictions(self, content_list: List[str]) -> List[Dict[str, Any]]:
        """Find contradictions in reasoning content"""
        contradictions = []
        
        # Simple contradiction detection
        for i, content1 in enumerate(content_list):
            for j, content2 in enumerate(content_list[i+1:], i+1):
                contradiction = self.detect_contradiction_pair(content1, content2)
                if contradiction:
                    contradictions.append({
                        "type": "contradiction",
                        "content1": content1[:100],
                        "content2": content2[:100],
                        "issue": contradiction
                    })
        
        return contradictions
    
    def detect_contradiction_pair(self, content1: str, content2: str) -> str:
        """Detect contradiction between two pieces of content"""
        c1_lower = content1.lower()
        c2_lower = content2.lower()
        
        # Check for opposing statements
        opposing_pairs = [
            ("allow", "prohibit"), ("can", "cannot"), ("yes", "no"),
            ("required", "optional"), ("must", "may not"), ("always", "never")
        ]
        
        for pos, neg in opposing_pairs:
            if pos in c1_lower and neg in c2_lower:
                return f"Contradiction: '{pos}' vs '{neg}'"
            if neg in c1_lower and pos in c2_lower:
                return f"Contradiction: '{neg}' vs '{pos}'"
        
        return None
    
    def check_logical_flow(self, content_list: List[str]) -> List[Dict[str, Any]]:
        """Check logical flow between reasoning steps"""
        flow_issues = []
        
        for i in range(len(content_list) - 1):
            current = content_list[i].lower()
            next_content = content_list[i+1].lower()
            
            # Check for abrupt topic changes
            if not self.has_logical_connection(current, next_content):
                flow_issues.append({
                    "type": "flow_discontinuity",
                    "step": i,
                    "issue": "Abrupt topic change without logical connection"
                })
        
        return flow_issues
    
    def has_logical_connection(self, content1: str, content2: str) -> bool:
        """Check if two pieces of content have logical connection"""
        # Simple heuristic - check for common keywords or logical connectors
        connectors = ["therefore", "thus", "because", "since", "however", "but", "and", "also"]
        
        # Check if content2 starts with a connector
        for connector in connectors:
            if content2.strip().startswith(connector):
                return True
        
        # Check for common keywords
        words1 = set(content1.split())
        words2 = set(content2.split())
        common_words = words1 & words2
        
        return len(common_words) >= 2
    
    def analyze_argument_structure(self, content_list: List[str]) -> float:
        """Analyze the structure of the argument"""
        if not content_list:
            return 0.0
        
        structure_score = 0.5  # Base score
        
        # Check for premise-conclusion structure
        has_premises = any("because" in c.lower() or "since" in c.lower() for c in content_list)
        has_conclusion = any("therefore" in c.lower() or "thus" in c.lower() for c in content_list)
        
        if has_premises:
            structure_score += 0.2
        if has_conclusion:
            structure_score += 0.2
        
        # Check for evidence
        has_evidence = any("data" in c.lower() or "evidence" in c.lower() for c in content_list)
        if has_evidence:
            structure_score += 0.1
        
        return min(structure_score, 1.0)
    
    def detect_logical_fallacies(self, content_list: List[str]) -> List[Dict[str, Any]]:
        """Detect common logical fallacies"""
        fallacies = []
        
        all_content = " ".join(content_list).lower()
        
        # Ad hominem
        if any(word in all_content for word in ["stupid", "ignorant", "foolish"]):
            fallacies.append({"type": "ad_hominem", "description": "Personal attack detected"})
        
        # False dichotomy
        if "either" in all_content and "or" in all_content and "only" in all_content:
            fallacies.append({"type": "false_dichotomy", "description": "False either/or choice"})
        
        # Circular reasoning
        repeated_phrases = self.find_repeated_phrases(content_list)
        if repeated_phrases:
            fallacies.append({"type": "circular_reasoning", "description": "Repeated assertions without new evidence"})
        
        return fallacies
    
    def find_repeated_phrases(self, content_list: List[str]) -> List[str]:
        """Find repeated phrases that might indicate circular reasoning"""
        phrases = []
        for content in content_list:
            words = content.lower().split()
            for i in range(len(words) - 2):
                phrase = " ".join(words[i:i+3])
                phrases.append(phrase)
        
        # Find phrases that appear more than once
        from collections import Counter
        phrase_counts = Counter(phrases)
        return [phrase for phrase, count in phrase_counts.items() if count > 1]
    
    def extract_factual_claims(self, content_list: List[str]) -> List[str]:
        """Extract factual claims from reasoning content"""
        claims = []
        
        for content in content_list:
            # Look for statements with numbers, dates, or specific facts
            if re.search(r'\d+', content):  # Contains numbers
                claims.append(content)
            elif any(word in content.lower() for word in ["policy", "rule", "regulation", "law"]):
                claims.append(content)
            elif re.search(r'\b(is|are|was|were|has|have)\b', content.lower()):
                claims.append(content)
        
        return claims[:5]  # Limit to avoid too many claims
    
    async def verify_claim(self, claim: str) -> Dict[str, Any]:
        """Verify a factual claim"""
        # Simple verification - in production would use knowledge base
        verification = {"verified": False, "confidence": 0.0}
        
        claim_lower = claim.lower()
        
        # Check against known facts (simplified)
        if "30 days" in claim_lower and "privilege leave" in claim_lower:
            verification = {"verified": True, "confidence": 0.9}
        elif "26 weeks" in claim_lower and "maternity" in claim_lower:
            verification = {"verified": True, "confidence": 0.9}
        elif "policy" in claim_lower:
            verification = {"verified": True, "confidence": 0.6}
        else:
            verification = {"verified": False, "confidence": 0.3, "issue": "Cannot verify claim"}
        
        return verification
    
    def classify_reasoning_structure(self, content_list: List[str]) -> str:
        """Classify the structure of reasoning"""
        if not content_list:
            return "empty"
        
        all_content = " ".join(content_list).lower()
        
        if "because" in all_content and "therefore" in all_content:
            return "deductive"
        elif "example" in all_content or "instance" in all_content:
            return "inductive"
        elif "similar" in all_content or "like" in all_content:
            return "analogical"
        else:
            return "informal"
    
    def determine_consistency_level(self, score: float) -> str:
        """Determine consistency level from score"""
        if score >= 0.8:
            return "highly_consistent"
        elif score >= 0.6:
            return "mostly_consistent"
        elif score >= 0.4:
            return "somewhat_consistent"
        else:
            return "inconsistent"