from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import uuid
import asyncio

@dataclass
class ReasoningNode:
    node_id: str
    content: str
    confidence: float
    depth: int
    parent_id: Optional[str] = None
    children: List[str] = None
    evaluation_score: float = 0.0
    is_terminal: bool = False
    
    def __post_init__(self):
        if self.children is None:
            self.children = []

class ReasoningTree:
    def __init__(self, problem: str, max_depth: int = 5, beam_width: int = 3):
        self.problem = problem
        self.max_depth = max_depth
        self.beam_width = beam_width
        self.nodes = {}
        self.root_id = None
        self.paths = []
        
    async def construct_tree(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Construct reasoning tree with beam search"""
        # Create root node
        root_node = ReasoningNode(
            node_id=str(uuid.uuid4()),
            content=f"Problem: {self.problem}",
            confidence=1.0,
            depth=0
        )
        
        self.root_id = root_node.node_id
        self.nodes[root_node.node_id] = root_node
        
        # Build tree level by level
        current_frontier = [root_node.node_id]
        
        for depth in range(1, self.max_depth + 1):
            next_frontier = []
            
            # Generate children for each node in current frontier
            for node_id in current_frontier:
                children = await self.generate_children(node_id, context, depth)
                next_frontier.extend(children)
            
            # Apply beam search - keep only top beam_width nodes
            if len(next_frontier) > self.beam_width:
                evaluated_nodes = [(nid, self.nodes[nid].evaluation_score) for nid in next_frontier]
                evaluated_nodes.sort(key=lambda x: x[1], reverse=True)
                next_frontier = [nid for nid, _ in evaluated_nodes[:self.beam_width]]
            
            current_frontier = next_frontier
            
            # Stop if no more nodes to expand
            if not current_frontier:
                break
        
        # Extract all paths from root to leaves
        self.paths = self.extract_all_paths()
        
        return {
            "tree_constructed": True,
            "total_nodes": len(self.nodes),
            "max_depth_reached": max(node.depth for node in self.nodes.values()),
            "total_paths": len(self.paths),
            "root_id": self.root_id
        }
    
    async def generate_children(self, parent_id: str, context: Dict[str, Any], depth: int) -> List[str]:
        """Generate child nodes for a parent node"""
        parent_node = self.nodes[parent_id]
        children_ids = []
        
        # Generate different reasoning approaches
        reasoning_approaches = self.get_reasoning_approaches(parent_node.content, context)
        
        for approach in reasoning_approaches:
            child_content = await self.apply_reasoning_approach(parent_node.content, approach, context)
            
            child_node = ReasoningNode(
                node_id=str(uuid.uuid4()),
                content=child_content,
                confidence=approach.get("confidence", 0.5),
                depth=depth,
                parent_id=parent_id
            )
            
            # Evaluate child node
            child_node.evaluation_score = await self.evaluate_node(child_node, context)
            
            # Check if terminal
            child_node.is_terminal = self.is_terminal_node(child_node, depth)
            
            self.nodes[child_node.node_id] = child_node
            parent_node.children.append(child_node.node_id)
            children_ids.append(child_node.node_id)
        
        return children_ids
    
    def get_reasoning_approaches(self, content: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get possible reasoning approaches for current content"""
        approaches = []
        
        # Deductive reasoning
        approaches.append({
            "type": "deductive",
            "description": "Apply general rules to specific case",
            "confidence": 0.8
        })
        
        # Inductive reasoning
        approaches.append({
            "type": "inductive", 
            "description": "Generalize from specific examples",
            "confidence": 0.7
        })
        
        # Analogical reasoning
        approaches.append({
            "type": "analogical",
            "description": "Compare with similar situations",
            "confidence": 0.6
        })
        
        return approaches[:2]  # Limit to prevent explosion
    
    async def apply_reasoning_approach(self, content: str, approach: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Apply specific reasoning approach to generate new content"""
        approach_type = approach["type"]
        
        if approach_type == "deductive":
            return f"Deductive analysis: {content} → Apply policy rules"
        elif approach_type == "inductive":
            return f"Inductive analysis: {content} → Pattern from examples"
        elif approach_type == "analogical":
            return f"Analogical analysis: {content} → Similar case comparison"
        else:
            return f"General analysis: {content}"
    
    async def evaluate_node(self, node: ReasoningNode, context: Dict[str, Any]) -> float:
        """Evaluate reasoning node quality"""
        score = 0.0
        
        # Content relevance
        if "policy" in node.content.lower():
            score += 0.3
        if "analysis" in node.content.lower():
            score += 0.2
        
        # Depth penalty (prefer shorter paths)
        score += max(0, 1.0 - node.depth * 0.1)
        
        # Confidence factor
        score += node.confidence * 0.3
        
        return min(score, 1.0)
    
    def is_terminal_node(self, node: ReasoningNode, depth: int) -> bool:
        """Check if node should be terminal"""
        return (depth >= self.max_depth or 
                "conclusion" in node.content.lower() or
                node.evaluation_score < 0.3)
    
    def extract_all_paths(self) -> List[List[str]]:
        """Extract all paths from root to terminal nodes"""
        paths = []
        
        def dfs_paths(node_id: str, current_path: List[str]):
            current_path.append(node_id)
            node = self.nodes[node_id]
            
            if node.is_terminal or not node.children:
                paths.append(current_path.copy())
            else:
                for child_id in node.children:
                    dfs_paths(child_id, current_path)
            
            current_path.pop()
        
        if self.root_id:
            dfs_paths(self.root_id, [])
        
        return paths
    
    def get_best_path(self) -> Optional[List[str]]:
        """Get the best reasoning path based on evaluation scores"""
        if not self.paths:
            return None
        
        best_path = None
        best_score = -1
        
        for path in self.paths:
            # Calculate path score as average of node scores
            path_score = sum(self.nodes[node_id].evaluation_score for node_id in path) / len(path)
            
            if path_score > best_score:
                best_score = path_score
                best_path = path
        
        return best_path
    
    def get_path_content(self, path: List[str]) -> List[str]:
        """Get content for nodes in a path"""
        return [self.nodes[node_id].content for node_id in path]
    
    def merge_paths(self, paths: List[List[str]]) -> Dict[str, Any]:
        """Merge multiple reasoning paths"""
        if not paths:
            return {"merged_content": "", "confidence": 0.0}
        
        # Get content from all paths
        all_contents = []
        total_confidence = 0.0
        
        for path in paths:
            path_content = self.get_path_content(path)
            all_contents.extend(path_content)
            
            # Calculate path confidence
            path_confidence = sum(self.nodes[node_id].confidence for node_id in path) / len(path)
            total_confidence += path_confidence
        
        # Create merged reasoning
        unique_contents = list(set(all_contents))
        merged_content = " → ".join(unique_contents[:5])  # Limit length
        
        return {
            "merged_content": merged_content,
            "confidence": total_confidence / len(paths),
            "paths_merged": len(paths),
            "unique_steps": len(unique_contents)
        }