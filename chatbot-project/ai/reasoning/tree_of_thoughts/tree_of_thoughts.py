"""Tree of Thoughts implementation for advanced reasoning."""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import uuid
import json

class ThoughtType(Enum):
    HYPOTHESIS = "hypothesis"
    EVIDENCE = "evidence"
    INFERENCE = "inference"
    CONCLUSION = "conclusion"
    QUESTION = "question"

@dataclass
class Thought:
    id: str
    content: str
    type: ThoughtType
    confidence: float
    parent_id: Optional[str]
    children: List[str]
    evidence: List[str]
    reasoning_path: List[str]
    metadata: Dict[str, Any]

class TreeOfThoughts:
    def __init__(self):
        self.thoughts = {}
        self.root_thoughts = []
        self.reasoning_paths = []
        
    def generate_thoughts(self, query: str, context: Dict[str, Any], max_depth: int = 3) -> List[str]:
        """Generate multiple reasoning paths for a query."""
        # Generate initial hypotheses
        initial_thoughts = self._generate_initial_thoughts(query, context)
        
        # Expand each thought path
        all_paths = []
        for thought_id in initial_thoughts:
            paths = self._expand_thought_path(thought_id, max_depth)
            all_paths.extend(paths)
        
        # Evaluate and rank paths
        ranked_paths = self._evaluate_paths(all_paths, query, context)
        
        return ranked_paths
    
    def _generate_initial_thoughts(self, query: str, context: Dict[str, Any]) -> List[str]:
        """Generate initial hypotheses for the query."""
        initial_thoughts = []
        
        # Generate different types of initial thoughts
        thought_generators = [
            self._generate_direct_hypothesis,
            self._generate_alternative_hypothesis,
            self._generate_questioning_hypothesis
        ]
        
        for generator in thought_generators:
            thought_id = generator(query, context)
            if thought_id:
                initial_thoughts.append(thought_id)
                self.root_thoughts.append(thought_id)
        
        return initial_thoughts
    
    def _generate_direct_hypothesis(self, query: str, context: Dict[str, Any]) -> Optional[str]:
        """Generate a direct hypothesis based on the query."""
        thought_id = str(uuid.uuid4())
        
        # Simple direct approach
        hypothesis_content = f"Direct approach: {query}"
        
        thought = Thought(
            id=thought_id,
            content=hypothesis_content,
            type=ThoughtType.HYPOTHESIS,
            confidence=0.7,
            parent_id=None,
            children=[],
            evidence=[],
            reasoning_path=[hypothesis_content],
            metadata={"approach": "direct", "query": query}
        )
        
        self.thoughts[thought_id] = thought
        return thought_id
    
    def _generate_alternative_hypothesis(self, query: str, context: Dict[str, Any]) -> Optional[str]:
        """Generate an alternative hypothesis."""
        thought_id = str(uuid.uuid4())
        
        # Alternative approach
        hypothesis_content = f"Alternative approach: Consider multiple perspectives on {query}"
        
        thought = Thought(
            id=thought_id,
            content=hypothesis_content,
            type=ThoughtType.HYPOTHESIS,
            confidence=0.6,
            parent_id=None,
            children=[],
            evidence=[],
            reasoning_path=[hypothesis_content],
            metadata={"approach": "alternative", "query": query}
        )
        
        self.thoughts[thought_id] = thought
        return thought_id
    
    def _generate_questioning_hypothesis(self, query: str, context: Dict[str, Any]) -> Optional[str]:
        """Generate a questioning hypothesis."""
        thought_id = str(uuid.uuid4())
        
        # Questioning approach
        hypothesis_content = f"Question-based approach: What assumptions underlie {query}?"
        
        thought = Thought(
            id=thought_id,
            content=hypothesis_content,
            type=ThoughtType.QUESTION,
            confidence=0.5,
            parent_id=None,
            children=[],
            evidence=[],
            reasoning_path=[hypothesis_content],
            metadata={"approach": "questioning", "query": query}
        )
        
        self.thoughts[thought_id] = thought
        return thought_id
    
    def _expand_thought_path(self, thought_id: str, max_depth: int) -> List[List[str]]:
        """Expand a thought path through multiple reasoning steps."""
        if max_depth <= 0:
            return [[thought_id]]
        
        current_thought = self.thoughts[thought_id]
        
        # Generate child thoughts
        child_thoughts = self._generate_child_thoughts(current_thought)
        
        if not child_thoughts:
            return [[thought_id]]
        
        # Recursively expand each child path
        all_paths = []
        for child_id in child_thoughts:
            child_paths = self._expand_thought_path(child_id, max_depth - 1)
            for path in child_paths:
                all_paths.append([thought_id] + path)
        
        return all_paths
    
    def _generate_child_thoughts(self, parent_thought: Thought) -> List[str]:
        """Generate child thoughts based on parent thought."""
        child_thoughts = []
        
        if parent_thought.type == ThoughtType.HYPOTHESIS:
            # Generate evidence-seeking thoughts
            evidence_thought = self._create_evidence_thought(parent_thought)
            if evidence_thought:
                child_thoughts.append(evidence_thought)
            
            # Generate inference thoughts
            inference_thought = self._create_inference_thought(parent_thought)
            if inference_thought:
                child_thoughts.append(inference_thought)
        
        elif parent_thought.type == ThoughtType.EVIDENCE:
            # Generate inference from evidence
            inference_thought = self._create_inference_from_evidence(parent_thought)
            if inference_thought:
                child_thoughts.append(inference_thought)
        
        elif parent_thought.type == ThoughtType.INFERENCE:
            # Generate conclusion
            conclusion_thought = self._create_conclusion_thought(parent_thought)
            if conclusion_thought:
                child_thoughts.append(conclusion_thought)
        
        elif parent_thought.type == ThoughtType.QUESTION:
            # Generate hypothesis from question
            hypothesis_thought = self._create_hypothesis_from_question(parent_thought)
            if hypothesis_thought:
                child_thoughts.append(hypothesis_thought)
        
        return child_thoughts
    
    def _create_evidence_thought(self, parent_thought: Thought) -> Optional[str]:
        """Create an evidence-seeking thought."""
        thought_id = str(uuid.uuid4())
        
        content = f"Seek evidence for: {parent_thought.content}"
        
        thought = Thought(
            id=thought_id,
            content=content,
            type=ThoughtType.EVIDENCE,
            confidence=0.6,
            parent_id=parent_thought.id,
            children=[],
            evidence=[],
            reasoning_path=parent_thought.reasoning_path + [content],
            metadata={"parent_type": parent_thought.type.value}
        )
        
        self.thoughts[thought_id] = thought
        parent_thought.children.append(thought_id)
        
        return thought_id
    
    def _create_inference_thought(self, parent_thought: Thought) -> Optional[str]:
        """Create an inference thought."""
        thought_id = str(uuid.uuid4())
        
        content = f"Infer from: {parent_thought.content}"
        
        thought = Thought(
            id=thought_id,
            content=content,
            type=ThoughtType.INFERENCE,
            confidence=0.7,
            parent_id=parent_thought.id,
            children=[],
            evidence=[],
            reasoning_path=parent_thought.reasoning_path + [content],
            metadata={"parent_type": parent_thought.type.value}
        )
        
        self.thoughts[thought_id] = thought
        parent_thought.children.append(thought_id)
        
        return thought_id
    
    def _create_inference_from_evidence(self, parent_thought: Thought) -> Optional[str]:
        """Create inference from evidence."""
        thought_id = str(uuid.uuid4())
        
        content = f"Based on evidence: {parent_thought.content}, we can infer..."
        
        thought = Thought(
            id=thought_id,
            content=content,
            type=ThoughtType.INFERENCE,
            confidence=0.8,
            parent_id=parent_thought.id,
            children=[],
            evidence=[parent_thought.content],
            reasoning_path=parent_thought.reasoning_path + [content],
            metadata={"parent_type": parent_thought.type.value}
        )
        
        self.thoughts[thought_id] = thought
        parent_thought.children.append(thought_id)
        
        return thought_id
    
    def _create_conclusion_thought(self, parent_thought: Thought) -> Optional[str]:
        """Create a conclusion thought."""
        thought_id = str(uuid.uuid4())
        
        content = f"Conclusion from: {parent_thought.content}"
        
        thought = Thought(
            id=thought_id,
            content=content,
            type=ThoughtType.CONCLUSION,
            confidence=0.8,
            parent_id=parent_thought.id,
            children=[],
            evidence=parent_thought.evidence,
            reasoning_path=parent_thought.reasoning_path + [content],
            metadata={"parent_type": parent_thought.type.value}
        )
        
        self.thoughts[thought_id] = thought
        parent_thought.children.append(thought_id)
        
        return thought_id
    
    def _create_hypothesis_from_question(self, parent_thought: Thought) -> Optional[str]:
        """Create hypothesis from question."""
        thought_id = str(uuid.uuid4())
        
        content = f"Hypothesis addressing: {parent_thought.content}"
        
        thought = Thought(
            id=thought_id,
            content=content,
            type=ThoughtType.HYPOTHESIS,
            confidence=0.6,
            parent_id=parent_thought.id,
            children=[],
            evidence=[],
            reasoning_path=parent_thought.reasoning_path + [content],
            metadata={"parent_type": parent_thought.type.value}
        )
        
        self.thoughts[thought_id] = thought
        parent_thought.children.append(thought_id)
        
        return thought_id
    
    def _evaluate_paths(self, paths: List[List[str]], query: str, context: Dict[str, Any]) -> List[str]:
        """Evaluate and rank reasoning paths."""
        path_scores = []
        
        for path in paths:
            score = self._calculate_path_score(path, query, context)
            path_scores.append((path, score))
        
        # Sort by score (descending)
        path_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Return top paths
        return [path for path, score in path_scores[:3]]
    
    def _calculate_path_score(self, path: List[str], query: str, context: Dict[str, Any]) -> float:
        """Calculate score for a reasoning path."""
        if not path:
            return 0.0
        
        total_score = 0.0
        path_length = len(path)
        
        # Score based on thought confidences
        confidence_score = 0.0
        for thought_id in path:
            thought = self.thoughts[thought_id]
            confidence_score += thought.confidence
        
        confidence_score /= path_length
        total_score += confidence_score * 0.4
        
        # Score based on path completeness
        final_thought = self.thoughts[path[-1]]
        if final_thought.type == ThoughtType.CONCLUSION:
            total_score += 0.3
        
        # Score based on evidence
        evidence_count = sum(len(self.thoughts[tid].evidence) for tid in path)
        evidence_score = min(evidence_count / 3.0, 1.0)  # Normalize to max 1.0
        total_score += evidence_score * 0.2
        
        # Score based on reasoning depth
        depth_score = min(path_length / 4.0, 1.0)  # Normalize to max 1.0
        total_score += depth_score * 0.1
        
        return total_score
    
    def get_best_reasoning_path(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get the best reasoning path for a query."""
        paths = self.generate_thoughts(query, context)
        
        if not paths:
            return {"path": [], "confidence": 0.0, "reasoning": []}
        
        best_path = paths[0]
        
        # Extract reasoning steps
        reasoning_steps = []
        total_confidence = 0.0
        
        for thought_id in best_path:
            thought = self.thoughts[thought_id]
            reasoning_steps.append({
                "step": thought.content,
                "type": thought.type.value,
                "confidence": thought.confidence,
                "evidence": thought.evidence
            })
            total_confidence += thought.confidence
        
        avg_confidence = total_confidence / len(best_path) if best_path else 0.0
        
        return {
            "path": best_path,
            "confidence": avg_confidence,
            "reasoning": reasoning_steps,
            "total_thoughts": len(self.thoughts)
        }
    
    def export_tree(self) -> Dict[str, Any]:
        """Export the complete thought tree."""
        return {
            "thoughts": {
                tid: {
                    "content": thought.content,
                    "type": thought.type.value,
                    "confidence": thought.confidence,
                    "parent_id": thought.parent_id,
                    "children": thought.children,
                    "evidence": thought.evidence,
                    "reasoning_path": thought.reasoning_path,
                    "metadata": thought.metadata
                }
                for tid, thought in self.thoughts.items()
            },
            "root_thoughts": self.root_thoughts,
            "total_thoughts": len(self.thoughts)
        }