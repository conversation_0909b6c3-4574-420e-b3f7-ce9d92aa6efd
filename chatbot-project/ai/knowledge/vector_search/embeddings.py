from sentence_transformers import SentenceTransformer
import numpy as np
from typing import List, Dict
import faiss

class EmbeddingGenerator:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model = SentenceTransformer(model_name)
        self.dimension = 384  # all-MiniLM-L6-v2 dimension
        
    def generate_embeddings(self, texts: List[str]) -> np.ndarray:
        return self.model.encode(texts)
    
    def create_vector_index(self, embeddings: np.ndarray) -> faiss.IndexFlatIP:
        index = faiss.IndexFlatIP(self.dimension)
        index.add(embeddings.astype('float32'))
        return index
    
    def search_similar(self, query: str, index: faiss.IndexFlatIP, k: int = 5) -> List[Dict]:
        query_embedding = self.model.encode([query])
        scores, indices = index.search(query_embedding.astype('float32'), k)
        
        return [
            {"index": int(idx), "score": float(score)} 
            for idx, score in zip(indices[0], scores[0])
        ]