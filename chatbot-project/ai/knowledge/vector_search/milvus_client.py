from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
import numpy as np
from typing import List, Dict

class MilvusClient:
    def __init__(self, host: str = "localhost", port: str = "19530"):
        connections.connect("default", host=host, port=port)
        self.collection_name = "document_embeddings"
        self.collection = None
        
    def create_collection(self, dimension: int = 384):
        if utility.has_collection(self.collection_name):
            utility.drop_collection(self.collection_name)
            
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=dimension),
            FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=2000),
            FieldSchema(name="organization", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="filename", dtype=DataType.VARCHAR, max_length=200)
        ]
        
        schema = CollectionSchema(fields, "Document embeddings collection")
        self.collection = Collection(self.collection_name, schema)
        
        # Create index
        index_params = {
            "metric_type": "IP",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        self.collection.create_index("embedding", index_params)
        
    def insert_embeddings(self, embeddings: np.ndarray, texts: List[str], 
                         organizations: List[str], filenames: List[str]):
        if not self.collection:
            self.create_collection()
            
        data = [
            embeddings.tolist(),
            texts,
            organizations,
            filenames
        ]
        
        self.collection.insert(data)
        self.collection.flush()
        
    def search_similar(self, query_embedding: np.ndarray, top_k: int = 5, 
                      organization: str = None) -> List[Dict]:
        if not self.collection:
            return []
            
        self.collection.load()
        
        search_params = {"metric_type": "IP", "params": {"nprobe": 10}}
        
        if organization:
            expr = f'organization == "{organization}"'
            results = self.collection.search(
                [query_embedding.tolist()], "embedding", search_params, 
                limit=top_k, expr=expr, output_fields=["text", "organization", "filename"]
            )
        else:
            results = self.collection.search(
                [query_embedding.tolist()], "embedding", search_params,
                limit=top_k, output_fields=["text", "organization", "filename"]
            )
        
        return [
            {
                "text": hit.entity.get("text"),
                "organization": hit.entity.get("organization"),
                "filename": hit.entity.get("filename"),
                "score": hit.score
            }
            for hit in results[0]
        ]