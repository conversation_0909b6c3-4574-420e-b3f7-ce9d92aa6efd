import numpy as np
from typing import List, Dict, Optional
import re
from collections import Counter

class RelevanceRanker:
    def __init__(self):
        self.organization_weights = {
            "NUVO AI": 1.2,
            "Meril Life Sciences": 1.2,
            "Meril Healthcare": 1.1,
            "Meril Diagnostics": 1.1,
            "Meril Endo-Surgery": 1.1
        }
        
        self.policy_weights = {
            "privilege leave": 1.3,
            "sexual harassment": 1.3,
            "maternity leave": 1.2,
            "travel policy": 1.1,
            "lta": 1.1
        }
    
    def rerank_results(self, results: List[Dict], query: str, user_context: Dict = None) -> List[Dict]:
        reranked_results = []
        
        for result in results:
            enhanced_result = result.copy()
            
            # Calculate various relevance scores
            scores = {
                'semantic_score': result.get('score', 0.0),
                'query_match_score': self._calculate_query_match(result['text'], query),
                'organization_boost': self._calculate_organization_boost(result, user_context),
                'policy_relevance': self._calculate_policy_relevance(result['text']),
                'freshness_score': self._calculate_freshness_score(result.get('metadata', {})),
                'completeness_score': self._calculate_completeness_score(result['text'])
            }
            
            # Calculate final score
            final_score = self._combine_scores(scores)
            
            enhanced_result.update({
                'final_score': final_score,
                'ranking_scores': scores
            })
            
            reranked_results.append(enhanced_result)
        
        # Sort by final score
        reranked_results.sort(key=lambda x: x['final_score'], reverse=True)
        return reranked_results
    
    def _calculate_query_match(self, text: str, query: str) -> float:
        text_lower = text.lower()
        query_terms = query.lower().split()
        
        # Exact phrase match
        if query.lower() in text_lower:
            return 1.0
        
        # Term frequency scoring
        term_matches = sum(1 for term in query_terms if term in text_lower)
        if not query_terms:
            return 0.0
        
        return term_matches / len(query_terms)
    
    def _calculate_organization_boost(self, result: Dict, user_context: Dict = None) -> float:
        if not user_context:
            return 1.0
        
        user_org = user_context.get('organization', '').lower()
        if not user_org:
            return 1.0
        
        text = result['text'].lower()
        metadata = result.get('metadata', {})
        
        # Check if user's organization is mentioned
        for org, weight in self.organization_weights.items():
            if org.lower() in user_org and org.lower() in text:
                return weight
        
        # Check metadata
        result_org = metadata.get('organization', '').lower()
        if user_org in result_org:
            return 1.3
        
        return 1.0
    
    def _calculate_policy_relevance(self, text: str) -> float:
        text_lower = text.lower()
        max_weight = 1.0
        
        for policy, weight in self.policy_weights.items():
            if policy in text_lower:
                max_weight = max(max_weight, weight)
        
        return max_weight
    
    def _calculate_freshness_score(self, metadata: Dict) -> float:
        # Simple freshness based on filename patterns
        filename = metadata.get('filename', '').lower()
        
        if '2025' in filename:
            return 1.3
        elif '2024' in filename:
            return 1.2
        elif '2023' in filename:
            return 1.1
        elif '2022' in filename:
            return 1.0
        else:
            return 0.9
    
    def _calculate_completeness_score(self, text: str) -> float:
        # Score based on text length and structure
        word_count = len(text.split())
        
        if word_count < 50:
            return 0.8
        elif word_count < 200:
            return 1.0
        elif word_count < 500:
            return 1.1
        else:
            return 1.2
    
    def _combine_scores(self, scores: Dict) -> float:
        weights = {
            'semantic_score': 0.4,
            'query_match_score': 0.25,
            'organization_boost': 0.15,
            'policy_relevance': 0.1,
            'freshness_score': 0.05,
            'completeness_score': 0.05
        }
        
        final_score = 0.0
        for score_type, score_value in scores.items():
            weight = weights.get(score_type, 0.0)
            final_score += weight * score_value
        
        return final_score
    
    def diversity_rerank(self, results: List[Dict], diversity_factor: float = 0.3) -> List[Dict]:
        if not results:
            return results
        
        diversified_results = [results[0]]  # Always include top result
        remaining_results = results[1:]
        
        for _ in range(min(len(remaining_results), len(results) - 1)):
            best_candidate = None
            best_score = -1
            
            for candidate in remaining_results:
                # Calculate diversity score
                diversity_score = self._calculate_diversity_score(candidate, diversified_results)
                
                # Combine relevance and diversity
                combined_score = ((1 - diversity_factor) * candidate.get('final_score', 0) + 
                                diversity_factor * diversity_score)
                
                if combined_score > best_score:
                    best_score = combined_score
                    best_candidate = candidate
            
            if best_candidate:
                diversified_results.append(best_candidate)
                remaining_results.remove(best_candidate)
        
        return diversified_results
    
    def _calculate_diversity_score(self, candidate: Dict, selected_results: List[Dict]) -> float:
        if not selected_results:
            return 1.0
        
        candidate_text = candidate['text'].lower()
        candidate_terms = set(candidate_text.split())
        
        min_similarity = 1.0
        
        for selected in selected_results:
            selected_text = selected['text'].lower()
            selected_terms = set(selected_text.split())
            
            # Calculate Jaccard similarity
            intersection = len(candidate_terms & selected_terms)
            union = len(candidate_terms | selected_terms)
            
            if union > 0:
                similarity = intersection / union
                min_similarity = min(min_similarity, similarity)
        
        # Return diversity score (1 - similarity)
        return 1.0 - min_similarity
    
    def explain_ranking(self, result: Dict) -> Dict:
        ranking_scores = result.get('ranking_scores', {})
        
        explanation = {
            'final_score': result.get('final_score', 0.0),
            'factors': []
        }
        
        for factor, score in ranking_scores.items():
            if score > 1.0:
                explanation['factors'].append({
                    'factor': factor,
                    'score': score,
                    'impact': 'positive',
                    'description': self._get_factor_description(factor, score)
                })
            elif score < 1.0:
                explanation['factors'].append({
                    'factor': factor,
                    'score': score,
                    'impact': 'negative',
                    'description': self._get_factor_description(factor, score)
                })
        
        return explanation
    
    def _get_factor_description(self, factor: str, score: float) -> str:
        descriptions = {
            'semantic_score': f"Semantic similarity to query: {score:.2f}",
            'query_match_score': f"Direct query term matches: {score:.2f}",
            'organization_boost': f"Organization relevance boost: {score:.2f}",
            'policy_relevance': f"Policy-specific relevance: {score:.2f}",
            'freshness_score': f"Document freshness factor: {score:.2f}",
            'completeness_score': f"Content completeness: {score:.2f}"
        }
        
        return descriptions.get(factor, f"{factor}: {score:.2f}")