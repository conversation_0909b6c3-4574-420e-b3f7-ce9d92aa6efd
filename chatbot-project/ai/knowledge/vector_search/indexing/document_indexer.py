import numpy as np
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Tuple
import faiss
import pickle
import os

class DocumentIndexer:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model = SentenceTransformer(model_name)
        self.dimension = 384
        self.index = None
        self.documents = []
        self.metadata = []
        
    def create_index(self, index_type: str = "flat") -> faiss.Index:
        if index_type == "flat":
            self.index = faiss.IndexFlatIP(self.dimension)
        elif index_type == "ivf":
            quantizer = faiss.IndexFlatIP(self.dimension)
            self.index = faiss.IndexIVFFlat(quantizer, self.dimension, 100)
        elif index_type == "hnsw":
            self.index = faiss.IndexHNSWFlat(self.dimension, 32)
            self.index.hnsw.efConstruction = 200
        
        return self.index
    
    def add_documents(self, documents: List[str], metadata: List[Dict] = None):
        if not self.index:
            self.create_index()
            
        # Generate embeddings
        embeddings = self.model.encode(documents, convert_to_numpy=True)
        
        # Normalize for cosine similarity
        faiss.normalize_L2(embeddings)
        
        # Add to index
        if hasattr(self.index, 'is_trained') and not self.index.is_trained:
            self.index.train(embeddings)
        
        self.index.add(embeddings.astype('float32'))
        
        # Store documents and metadata
        self.documents.extend(documents)
        if metadata:
            self.metadata.extend(metadata)
        else:
            self.metadata.extend([{"doc_id": i + len(self.documents) - len(documents)} for i in range(len(documents))])
    
    def chunk_documents(self, documents: List[Dict], chunk_size: int = 512, overlap: int = 50) -> List[Dict]:
        chunks = []
        
        for doc in documents:
            content = doc.get('content', '')
            words = content.split()
            
            for i in range(0, len(words), chunk_size - overlap):
                chunk_words = words[i:i + chunk_size]
                chunk_text = ' '.join(chunk_words)
                
                chunk_metadata = doc.copy()
                chunk_metadata.update({
                    'chunk_id': len(chunks),
                    'chunk_start': i,
                    'chunk_end': min(i + chunk_size, len(words)),
                    'text': chunk_text
                })
                
                chunks.append(chunk_metadata)
        
        return chunks
    
    def index_document_collection(self, documents: List[Dict]) -> Dict:
        # Chunk documents
        chunks = self.chunk_documents(documents)
        
        # Extract text and metadata
        texts = [chunk['text'] for chunk in chunks]
        metadata = [{k: v for k, v in chunk.items() if k != 'text'} for chunk in chunks]
        
        # Add to index
        self.add_documents(texts, metadata)
        
        return {
            "total_documents": len(documents),
            "total_chunks": len(chunks),
            "index_size": self.index.ntotal if self.index else 0
        }
    
    def save_index(self, path: str):
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save FAISS index
        faiss.write_index(self.index, f"{path}.faiss")
        
        # Save documents and metadata
        with open(f"{path}_docs.pkl", 'wb') as f:
            pickle.dump({
                'documents': self.documents,
                'metadata': self.metadata
            }, f)
    
    def load_index(self, path: str):
        # Load FAISS index
        self.index = faiss.read_index(f"{path}.faiss")
        
        # Load documents and metadata
        with open(f"{path}_docs.pkl", 'rb') as f:
            data = pickle.load(f)
            self.documents = data['documents']
            self.metadata = data['metadata']
    
    def get_index_stats(self) -> Dict:
        return {
            "total_vectors": self.index.ntotal if self.index else 0,
            "dimension": self.dimension,
            "total_documents": len(self.documents),
            "index_type": type(self.index).__name__ if self.index else None
        }