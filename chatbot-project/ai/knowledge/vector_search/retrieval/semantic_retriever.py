import numpy as np
from sentence_transformers import SentenceTransformer
import faiss
from typing import List, Dict, Optional, Tuple
import re

class SemanticRetriever:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model = SentenceTransformer(model_name)
        self.index = None
        self.documents = []
        self.metadata = []
        
    def load_index(self, indexer):
        self.index = indexer.index
        self.documents = indexer.documents
        self.metadata = indexer.metadata
    
    def search(self, query: str, k: int = 10, filters: Dict = None) -> List[Dict]:
        if not self.index:
            return []
        
        # Generate query embedding
        query_embedding = self.model.encode([query], convert_to_numpy=True)
        faiss.normalize_L2(query_embedding)
        
        # Search
        scores, indices = self.index.search(query_embedding.astype('float32'), k * 2)  # Get more for filtering
        
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx == -1:  # Invalid index
                continue
                
            result = {
                'text': self.documents[idx],
                'score': float(score),
                'metadata': self.metadata[idx] if idx < len(self.metadata) else {}
            }
            
            # Apply filters
            if filters and not self._apply_filters(result, filters):
                continue
                
            results.append(result)
            
            if len(results) >= k:
                break
        
        return results
    
    def _apply_filters(self, result: Dict, filters: Dict) -> bool:
        metadata = result.get('metadata', {})
        
        for key, value in filters.items():
            if key == 'organization':
                org = metadata.get('organization', '').lower()
                if value.lower() not in org:
                    return False
            elif key == 'filename':
                filename = metadata.get('filename', '').lower()
                if value.lower() not in filename:
                    return False
            elif key == 'min_score':
                if result['score'] < value:
                    return False
        
        return True
    
    def hybrid_search(self, query: str, k: int = 10, alpha: float = 0.7, filters: Dict = None) -> List[Dict]:
        # Semantic search
        semantic_results = self.search(query, k, filters)
        
        # Keyword search
        keyword_results = self._keyword_search(query, k, filters)
        
        # Combine results
        combined_results = self._combine_results(semantic_results, keyword_results, alpha)
        
        return combined_results[:k]
    
    def _keyword_search(self, query: str, k: int, filters: Dict = None) -> List[Dict]:
        query_terms = set(query.lower().split())
        results = []
        
        for i, doc in enumerate(self.documents):
            doc_terms = set(doc.lower().split())
            
            # Calculate keyword overlap
            overlap = len(query_terms & doc_terms)
            if overlap == 0:
                continue
            
            # Simple TF-IDF-like scoring
            score = overlap / len(query_terms)
            
            result = {
                'text': doc,
                'score': score,
                'metadata': self.metadata[i] if i < len(self.metadata) else {}
            }
            
            # Apply filters
            if filters and not self._apply_filters(result, filters):
                continue
            
            results.append(result)
        
        # Sort by score
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:k]
    
    def _combine_results(self, semantic_results: List[Dict], keyword_results: List[Dict], alpha: float) -> List[Dict]:
        # Create combined scoring
        combined = {}
        
        # Add semantic results
        for result in semantic_results:
            text = result['text']
            combined[text] = {
                'text': text,
                'semantic_score': result['score'],
                'keyword_score': 0.0,
                'metadata': result['metadata']
            }
        
        # Add keyword results
        for result in keyword_results:
            text = result['text']
            if text in combined:
                combined[text]['keyword_score'] = result['score']
            else:
                combined[text] = {
                    'text': text,
                    'semantic_score': 0.0,
                    'keyword_score': result['score'],
                    'metadata': result['metadata']
                }
        
        # Calculate combined scores
        final_results = []
        for item in combined.values():
            combined_score = (alpha * item['semantic_score'] + 
                            (1 - alpha) * item['keyword_score'])
            
            final_results.append({
                'text': item['text'],
                'score': combined_score,
                'semantic_score': item['semantic_score'],
                'keyword_score': item['keyword_score'],
                'metadata': item['metadata']
            })
        
        # Sort by combined score
        final_results.sort(key=lambda x: x['score'], reverse=True)
        return final_results
    
    def multi_query_search(self, queries: List[str], k: int = 10, aggregation: str = "max") -> List[Dict]:
        all_results = {}
        
        for query in queries:
            results = self.search(query, k)
            
            for result in results:
                text = result['text']
                if text not in all_results:
                    all_results[text] = {
                        'text': text,
                        'scores': [],
                        'metadata': result['metadata']
                    }
                all_results[text]['scores'].append(result['score'])
        
        # Aggregate scores
        final_results = []
        for item in all_results.values():
            if aggregation == "max":
                final_score = max(item['scores'])
            elif aggregation == "mean":
                final_score = sum(item['scores']) / len(item['scores'])
            elif aggregation == "sum":
                final_score = sum(item['scores'])
            else:
                final_score = max(item['scores'])
            
            final_results.append({
                'text': item['text'],
                'score': final_score,
                'query_matches': len(item['scores']),
                'metadata': item['metadata']
            })
        
        # Sort and return top k
        final_results.sort(key=lambda x: x['score'], reverse=True)
        return final_results[:k]
    
    def contextual_search(self, query: str, context: str, k: int = 10) -> List[Dict]:
        # Enhance query with context
        enhanced_query = f"{context} {query}"
        
        # Perform search
        results = self.search(enhanced_query, k)
        
        # Re-rank based on context relevance
        for result in results:
            context_relevance = self._calculate_context_relevance(result['text'], context)
            result['context_relevance'] = context_relevance
            result['score'] = 0.7 * result['score'] + 0.3 * context_relevance
        
        # Sort by updated scores
        results.sort(key=lambda x: x['score'], reverse=True)
        return results
    
    def _calculate_context_relevance(self, text: str, context: str) -> float:
        # Simple context relevance based on term overlap
        text_terms = set(text.lower().split())
        context_terms = set(context.lower().split())
        
        if not context_terms:
            return 0.0
        
        overlap = len(text_terms & context_terms)
        return overlap / len(context_terms)