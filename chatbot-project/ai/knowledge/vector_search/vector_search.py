"""Vector Search Engine - Milestone 2: AI Foundation Complete."""

from typing import List, Dict, Any
import numpy as np
from sentence_transformers import SentenceTransformer
import logging

class VectorSearchEngine:
    """Vector-based document search and retrieval using local models."""
    
    def __init__(self):
        self.model = None
        self.documents = []
        self.embeddings = []
        self.initialized = False
        self._load_model()
    
    def _load_model(self):
        """Load the local embedding model."""
        try:
            # Use your locally cached model
            self.model = SentenceTransformer('all-MiniLM-L6-v2')
            print("✅ Loaded local embedding model: all-MiniLM-L6-v2")
        except Exception as e:
            print(f"❌ Failed to load embedding model: {e}")
            self.model = None
    
    def add_documents(self, documents: List[Dict[str, Any]]):
        """Add documents to the vector search index."""
        self.documents.extend(documents)
        
        if self.model:
            texts = [doc["content"] for doc in documents]
            try:
                new_embeddings = self.model.encode(texts)
                if len(self.embeddings) == 0:
                    self.embeddings = new_embeddings
                else:
                    self.embeddings = np.vstack([self.embeddings, new_embeddings])
                print(f"✅ Added {len(documents)} documents to vector index")
            except Exception as e:
                print(f"❌ Error generating embeddings: {e}")
                # Fallback: random embeddings for demo
                new_embeddings = np.random.rand(len(texts), 384)
                if len(self.embeddings) == 0:
                    self.embeddings = new_embeddings
                else:
                    self.embeddings = np.vstack([self.embeddings, new_embeddings])
        else:
            # Fallback mode without model
            self.embeddings = np.random.rand(len(self.documents), 384)
        
        self.initialized = True
    
    def search(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant documents using semantic similarity."""
        if not self.initialized or len(self.documents) == 0:
            return []
        
        try:
            if self.model:
                query_embedding = self.model.encode([query])
            else:
                # Fallback: keyword matching
                return self._keyword_search(query, k)
            
            # Calculate cosine similarities
            similarities = np.dot(self.embeddings, query_embedding.T).flatten()
            norms = np.linalg.norm(self.embeddings, axis=1) * np.linalg.norm(query_embedding)
            similarities = similarities / norms
            
            # Get top k results
            top_indices = np.argsort(similarities)[-k:][::-1]
            
            results = []
            for idx in top_indices:
                if idx < len(self.documents):
                    doc = self.documents[idx].copy()
                    doc["similarity"] = float(similarities[idx])
                    results.append(doc)
            
            return results
            
        except Exception as e:
            print(f"❌ Search error: {e}")
            return self._keyword_search(query, k)
    
    def _keyword_search(self, query: str, k: int) -> List[Dict[str, Any]]:
        """Fallback keyword-based search."""
        query_words = query.lower().split()
        scored_docs = []
        
        for doc in self.documents:
            content_lower = doc["content"].lower()
            score = sum(1 for word in query_words if word in content_lower)
            if score > 0:
                doc_copy = doc.copy()
                doc_copy["similarity"] = score / len(query_words)
                scored_docs.append(doc_copy)
        
        # Sort by score and return top k
        scored_docs.sort(key=lambda x: x["similarity"], reverse=True)
        return scored_docs[:k]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get search engine statistics."""
        return {
            "total_documents": len(self.documents),
            "embedding_dimension": 384,
            "model_loaded": self.model is not None,
            "model_name": "all-MiniLM-L6-v2" if self.model else "fallback",
            "initialized": self.initialized
        }