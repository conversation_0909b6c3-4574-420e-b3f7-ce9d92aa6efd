"""Local Milvus client for CHaBot vector search."""

from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
import numpy as np
from typing import List, Dict, Any
from config import get_settings

settings = get_settings()

class MilvusLocalClient:
    def __init__(self):
        self.host = getattr(settings, 'milvus_host', 'localhost')
        self.port = getattr(settings, 'milvus_port', '19530')
        self.collection_name = getattr(settings, 'milvus_collection_name', 'chabot_embeddings')
        self.collection = None
        self.connected = False
    
    def connect(self) -> bool:
        """Connect to local Milvus instance."""
        try:
            connections.connect("default", host=self.host, port=self.port)
            self.connected = True
            print(f"✅ Connected to Milvus at {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to Milvus: {e}")
            return False
    
    def create_collection(self, dimension: int = 768) -> bool:
        """Create collection for embeddings."""
        if not self.connected:
            if not self.connect():
                return False
        
        try:
            # Drop existing collection if exists
            if utility.has_collection(self.collection_name):
                utility.drop_collection(self.collection_name)
                print(f"🗑️ Dropped existing collection: {self.collection_name}")
            
            # Define schema
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=dimension),
                FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=2000),
                FieldSchema(name="organization", dtype=DataType.VARCHAR, max_length=100),
                FieldSchema(name="document", dtype=DataType.VARCHAR, max_length=200),
                FieldSchema(name="chunk_id", dtype=DataType.INT64),
                FieldSchema(name="metadata", dtype=DataType.JSON)
            ]
            
            schema = CollectionSchema(fields, f"CHaBot embeddings collection (dim={dimension})")
            self.collection = Collection(self.collection_name, schema)
            
            # Create index for vector search
            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }
            self.collection.create_index("embedding", index_params)
            
            print(f"✅ Created collection: {self.collection_name} with dimension {dimension}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create collection: {e}")
            return False
    
    def insert_embeddings(self, embeddings: List[List[float]], texts: List[str], 
                         organizations: List[str], documents: List[str],
                         chunk_ids: List[int] = None, metadata: List[Dict] = None) -> bool:
        """Insert embeddings into collection."""
        if not self.collection:
            if not self.create_collection(len(embeddings[0]) if embeddings else 768):
                return False
        
        try:
            # Prepare data
            chunk_ids = chunk_ids or list(range(len(embeddings)))
            metadata = metadata or [{}] * len(embeddings)
            
            data = [
                embeddings,
                texts,
                organizations,
                documents,
                chunk_ids,
                metadata
            ]
            
            # Insert data
            self.collection.insert(data)
            self.collection.flush()
            
            print(f"✅ Inserted {len(embeddings)} embeddings")
            return True
            
        except Exception as e:
            print(f"❌ Failed to insert embeddings: {e}")
            return False
    
    def search_similar(self, query_embedding: List[float], top_k: int = 5,
                      organization: str = None, document: str = None) -> List[Dict[str, Any]]:
        """Search for similar embeddings."""
        if not self.collection:
            print("❌ Collection not initialized")
            return []
        
        try:
            # Load collection
            self.collection.load()
            
            # Build search expression
            expr_parts = []
            if organization:
                expr_parts.append(f'organization == "{organization}"')
            if document:
                expr_parts.append(f'document == "{document}"')
            
            expr = " and ".join(expr_parts) if expr_parts else None
            
            # Search parameters
            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }
            
            # Perform search
            results = self.collection.search(
                [query_embedding],
                "embedding",
                search_params,
                limit=top_k,
                expr=expr,
                output_fields=["text", "organization", "document", "chunk_id", "metadata"]
            )
            
            # Format results
            formatted_results = []
            for hit in results[0]:
                formatted_results.append({
                    "text": hit.entity.get("text"),
                    "organization": hit.entity.get("organization"),
                    "document": hit.entity.get("document"),
                    "chunk_id": hit.entity.get("chunk_id"),
                    "metadata": hit.entity.get("metadata", {}),
                    "score": hit.score,
                    "distance": hit.distance
                })
            
            return formatted_results
            
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        if not self.collection:
            return {"error": "Collection not initialized"}
        
        try:
            stats = self.collection.get_stats()
            return {
                "name": self.collection_name,
                "num_entities": self.collection.num_entities,
                "stats": stats
            }
        except Exception as e:
            return {"error": str(e)}

# Global client instance
milvus_local = MilvusLocalClient()