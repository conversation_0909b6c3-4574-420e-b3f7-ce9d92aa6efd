import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from .indexing.document_indexer import DocumentIndexer
from .retrieval.semantic_retriever import SemanticRetriever
from .ranking.relevance_ranker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List, Dict, Optional

class EnhancedVectorSearch:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.indexer = DocumentIndexer(model_name)
        self.retriever = SemanticRetriever(model_name)
        self.ranker = RelevanceRanker()
        self.is_initialized = False
    
    def initialize_from_documents(self, documents: List[Dict]) -> Dict:
        """Initialize the search system with documents"""
        # Index documents
        indexing_stats = self.indexer.index_document_collection(documents)
        
        # Load index into retriever
        self.retriever.load_index(self.indexer)
        
        self.is_initialized = True
        return indexing_stats
    
    def search(self, query: str, k: int = 10, user_context: Dict = None, 
               search_type: str = "hybrid", filters: Dict = None) -> List[Dict]:
        """Main search interface"""
        if not self.is_initialized:
            return []
        
        # Perform search based on type
        if search_type == "semantic":
            results = self.retriever.search(query, k * 2, filters)
        elif search_type == "hybrid":
            results = self.retriever.hybrid_search(query, k * 2, alpha=0.7, filters=filters)
        elif search_type == "contextual" and user_context:
            context = f"{user_context.get('organization', '')} {user_context.get('department', '')}"
            results = self.retriever.contextual_search(query, context, k * 2)
        else:
            results = self.retriever.search(query, k * 2, filters)
        
        # Re-rank results
        reranked_results = self.ranker.rerank_results(results, query, user_context)
        
        # Apply diversity if requested
        if len(reranked_results) > k:
            reranked_results = self.ranker.diversity_rerank(reranked_results[:k * 2])
        
        return reranked_results[:k]
    
    def multi_query_search(self, queries: List[str], k: int = 10, 
                          user_context: Dict = None) -> List[Dict]:
        """Search with multiple related queries"""
        if not self.is_initialized:
            return []
        
        # Perform multi-query search
        results = self.retriever.multi_query_search(queries, k * 2)
        
        # Re-rank with combined query
        combined_query = " ".join(queries)
        reranked_results = self.ranker.rerank_results(results, combined_query, user_context)
        
        return reranked_results[:k]
    
    def organization_specific_search(self, query: str, organization: str, 
                                   k: int = 10) -> List[Dict]:
        """Search within specific organization context"""
        filters = {"organization": organization}
        user_context = {"organization": organization}
        
        return self.search(query, k, user_context, "contextual", filters)
    
    def policy_search(self, query: str, policy_type: str = None, 
                     k: int = 10) -> List[Dict]:
        """Search for policy-related information"""
        # Enhance query with policy context
        if policy_type:
            enhanced_query = f"{policy_type} policy {query}"
        else:
            enhanced_query = f"policy {query}"
        
        results = self.search(enhanced_query, k, search_type="hybrid")
        
        # Filter for policy-relevant results
        policy_results = []
        for result in results:
            text_lower = result['text'].lower()
            if any(term in text_lower for term in ['policy', 'rule', 'guideline', 'procedure']):
                policy_results.append(result)
        
        return policy_results[:k]
    
    def get_similar_documents(self, document_text: str, k: int = 5) -> List[Dict]:
        """Find documents similar to a given document"""
        if not self.is_initialized:
            return []
        
        # Use the document text as query
        results = self.retriever.search(document_text, k + 1)  # +1 to exclude self
        
        # Remove the exact match if present
        filtered_results = []
        for result in results:
            if result['text'] != document_text:
                filtered_results.append(result)
        
        return filtered_results[:k]
    
    def explain_search_results(self, query: str, results: List[Dict]) -> List[Dict]:
        """Provide explanations for search results"""
        explained_results = []
        
        for result in results:
            explained_result = result.copy()
            explanation = self.ranker.explain_ranking(result)
            explained_result['explanation'] = explanation
            explained_results.append(explained_result)
        
        return explained_results
    
    def get_search_suggestions(self, partial_query: str) -> List[str]:
        """Generate search suggestions based on indexed content"""
        if not self.is_initialized:
            return []
        
        suggestions = []
        partial_lower = partial_query.lower()
        
        # Extract common terms from documents
        common_terms = set()
        for doc in self.indexer.documents[:100]:  # Sample first 100 docs
            words = doc.lower().split()
            for word in words:
                if len(word) > 3 and partial_lower in word:
                    common_terms.add(word)
        
        # Generate suggestions
        for term in sorted(common_terms)[:10]:
            if term.startswith(partial_lower):
                suggestions.append(term)
        
        # Add common organizational queries
        org_suggestions = [
            "privilege leave policy",
            "sexual harassment committee",
            "maternity leave benefits",
            "travel policy guidelines",
            "LTA reimbursement process"
        ]
        
        for suggestion in org_suggestions:
            if partial_lower in suggestion.lower():
                suggestions.append(suggestion)
        
        return suggestions[:10]
    
    def get_index_statistics(self) -> Dict:
        """Get statistics about the search index"""
        if not self.is_initialized:
            return {"status": "not_initialized"}
        
        stats = self.indexer.get_index_stats()
        
        # Add retrieval statistics
        stats.update({
            "search_ready": True,
            "model_name": self.retriever.model.get_sentence_embedding_dimension(),
            "supported_search_types": ["semantic", "hybrid", "contextual"],
            "supported_filters": ["organization", "filename", "min_score"]
        })
        
        return stats
    
    def save_index(self, path: str):
        """Save the search index to disk"""
        self.indexer.save_index(path)
    
    def load_index(self, path: str):
        """Load a search index from disk"""
        self.indexer.load_index(path)
        self.retriever.load_index(self.indexer)
        self.is_initialized = True