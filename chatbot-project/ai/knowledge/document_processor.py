import os
from typing import List, Dict
import re
from datetime import datetime

class DocumentProcessor:
    def __init__(self, docs_path: str = "docs/"):
        self.docs_path = docs_path
        
    def load_documents(self) -> List[Dict]:
        documents = []
        docs_path = os.path.join(os.path.dirname(__file__), '../../docs')
        
        # Walk through organization-wise directory structure
        for root, dirs, files in os.walk(docs_path):
            for filename in files:
                if filename.endswith('.txt'):
                    file_path = os.path.join(root, filename)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as file:
                            content = file.read()
                        
                        # Extract organization from directory structure
                        organization = self._extract_organization_from_path(root, filename, content)
                        
                        documents.append({
                            'filename': filename,
                            'content': content,
                            'organization': organization,
                            'file_path': file_path,
                            'processed_at': datetime.now().isoformat(),
                            'chunks': self._chunk_document(content)
                        })
                        
                    except Exception as e:
                        print(f"Error loading {filename}: {str(e)}")
        
        return documents
    
    def _extract_organization_from_path(self, root_path: str, filename: str, content: str) -> str:
        # Extract organization from directory structure
        path_parts = root_path.split(os.sep)
        
        if 'NUVO_AI' in path_parts:
            return 'NUVO AI Pvt Ltd'
        elif 'Meril_Life_Sciences' in path_parts:
            return 'Meril Life Sciences Pvt. Ltd.'
        elif 'Meril_Healthcare' in path_parts:
            return 'Meril Healthcare Pvt. Ltd.'
        elif 'Meril_Diagnostics' in path_parts:
            return 'Meril Diagnostics Pvt. Ltd.'
        elif 'Meril_Endo_Surgery' in path_parts:
            return 'Meril Endo-Surgery Pvt. Ltd.'
        elif 'MERIL_GROUP' in path_parts:
            return 'Meril Group of Companies'
        
        # Fallback to old method
        return self._extract_organization_legacy(filename, content)
    
    def _extract_organization_legacy(self, filename: str, content: str) -> str:
        filename_lower = filename.lower()
        content_lower = content.lower()
        
        if 'nuvo' in filename_lower or 'nuvo ai' in content_lower:
            return 'NUVO AI Pvt Ltd'
        elif 'meril' in filename_lower or 'meril' in content_lower:
            if 'healthcare' in filename_lower or 'healthcare' in content_lower:
                return 'Meril Healthcare Pvt. Ltd.'
            elif 'diagnostic' in filename_lower or 'diagnostic' in content_lower:
                return 'Meril Diagnostics Pvt. Ltd.'
            elif 'endo' in filename_lower or 'endo-surgery' in content_lower:
                return 'Meril Endo-Surgery Pvt. Ltd.'
            else:
                return 'Meril Life Sciences Pvt. Ltd.'
        
        return 'Unknown'
    
    def _chunk_document(self, content: str, chunk_size: int = 500) -> List[str]:
        sentences = re.split(r'[.!?]+', content)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk + sentence) < chunk_size:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
            
        return chunks