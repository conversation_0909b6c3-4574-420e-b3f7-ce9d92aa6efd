from typing import Dict, List, Optional
from ..neo4j_client import Neo4jClient

class GraphQueries:
    def __init__(self, neo4j_client: Neo4jClient):
        self.client = neo4j_client
    
    def find_organization_details(self, org_name: str) -> List[Dict]:
        """Find detailed information about an organization"""
        query = """
        MATCH (org:Organization)
        WHERE org.name CONTAINS $org_name OR org.legal_name CONTAINS $org_name
        OPTIONAL MATCH (org)<-[:WORKS_AT]-(person:Person)
        OPTIONAL MATCH (org)<-[:APPLIES_TO]-(policy:Policy)
        OPTIONAL MATCH (org)<-[:BELONGS_TO]-(committee:Committee)
        OPTIONAL MATCH (parent:Organization)-[:PARENT_OF]->(org)
        OPTIONAL MATCH (org)-[:PARENT_OF]->(child:Organization)
        RETURN org, 
               collect(DISTINCT person) as employees,
               collect(DISTINCT policy) as policies,
               collect(DISTINCT committee) as committees,
               parent,
               collect(DISTINCT child) as subsidiaries
        """
        
        with self.client.driver.session() as session:
            result = session.run(query, {'org_name': org_name})
            return [record.data() for record in result]
    
    def find_person_profile(self, person_name: str) -> List[Dict]:
        """Find complete profile of a person"""
        query = """
        MATCH (p:Person)
        WHERE p.name CONTAINS $person_name
        OPTIONAL MATCH (p)-[:WORKS_AT]->(org:Organization)
        OPTIONAL MATCH (p)-[:MEMBER_OF]->(committee:Committee)
        OPTIONAL MATCH (p)-[:PRESIDES_OVER]->(presided_committee:Committee)
        OPTIONAL MATCH (committee)-[:BELONGS_TO]->(committee_org:Organization)
        RETURN p, org, 
               collect(DISTINCT committee) as member_committees,
               collect(DISTINCT presided_committee) as presided_committees,
               collect(DISTINCT committee_org) as committee_organizations
        """
        
        with self.client.driver.session() as session:
            result = session.run(query, {'person_name': person_name})
            return [record.data() for record in result]
    
    def find_policy_implementation(self, policy_name: str) -> List[Dict]:
        """Find how a policy is implemented across organizations"""
        query = """
        MATCH (pol:Policy)
        WHERE pol.name CONTAINS $policy_name
        MATCH (pol)-[:APPLIES_TO]->(org:Organization)
        OPTIONAL MATCH (org)<-[:WORKS_AT]-(person:Person)
        OPTIONAL MATCH (org)<-[:BELONGS_TO]-(committee:Committee)
        OPTIONAL MATCH (committee)<-[:MEMBER_OF|PRESIDES_OVER]-(committee_member:Person)
        RETURN pol, org,
               collect(DISTINCT person) as affected_employees,
               collect(DISTINCT committee) as related_committees,
               collect(DISTINCT committee_member) as committee_members
        """
        
        with self.client.driver.session() as session:
            result = session.run(query, {'policy_name': policy_name})
            return [record.data() for record in result]
    
    def find_committee_structure(self, committee_name: str) -> List[Dict]:
        """Find committee structure and members"""
        query = """
        MATCH (c:Committee)
        WHERE c.name CONTAINS $committee_name
        OPTIONAL MATCH (c)<-[:MEMBER_OF]-(member:Person)
        OPTIONAL MATCH (c)<-[:PRESIDES_OVER]-(presiding_officer:Person)
        OPTIONAL MATCH (c)-[:BELONGS_TO]->(org:Organization)
        RETURN c, org,
               collect(DISTINCT member) as members,
               collect(DISTINCT presiding_officer) as presiding_officers
        """
        
        with self.client.driver.session() as session:
            result = session.run(query, {'committee_name': committee_name})
            return [record.data() for record in result]
    
    def find_organizational_hierarchy(self, root_org: str = None) -> List[Dict]:
        """Find complete organizational hierarchy"""
        if root_org:
            query = """
            MATCH (root:Organization)
            WHERE root.name CONTAINS $root_org
            OPTIONAL MATCH path = (root)-[:PARENT_OF*]->(child:Organization)
            RETURN root, collect(DISTINCT child) as all_children, 
                   [node in nodes(path) | node] as hierarchy_path
            """
            params = {'root_org': root_org}
        else:
            query = """
            MATCH (org:Organization)
            OPTIONAL MATCH (parent:Organization)-[:PARENT_OF]->(org)
            OPTIONAL MATCH (org)-[:PARENT_OF]->(child:Organization)
            RETURN org, parent, collect(DISTINCT child) as children
            ORDER BY org.name
            """
            params = {}
        
        with self.client.driver.session() as session:
            result = session.run(query, params)
            return [record.data() for record in result]
    
    def find_cross_organizational_connections(self, org1: str, org2: str) -> List[Dict]:
        """Find connections between two organizations"""
        query = """
        MATCH (org1:Organization), (org2:Organization)
        WHERE org1.name CONTAINS $org1 AND org2.name CONTAINS $org2
        OPTIONAL MATCH path = (org1)-[*1..3]-(org2)
        OPTIONAL MATCH (org1)<-[:WORKS_AT]-(person1:Person)
        OPTIONAL MATCH (org2)<-[:WORKS_AT]-(person2:Person)
        OPTIONAL MATCH (pol:Policy)-[:APPLIES_TO]->(org1)
        OPTIONAL MATCH (pol)-[:APPLIES_TO]->(org2)
        RETURN org1, org2, 
               collect(DISTINCT person1) as org1_people,
               collect(DISTINCT person2) as org2_people,
               collect(DISTINCT pol) as shared_policies,
               collect(DISTINCT path) as connection_paths
        """
        
        with self.client.driver.session() as session:
            result = session.run(query, {'org1': org1, 'org2': org2})
            return [record.data() for record in result]
    
    def find_policy_conflicts(self) -> List[Dict]:
        """Find potential policy conflicts across organizations"""
        query = """
        MATCH (pol1:Policy), (pol2:Policy)
        WHERE pol1.name = pol2.name AND pol1.id <> pol2.id
        MATCH (pol1)-[:APPLIES_TO]->(org1:Organization)
        MATCH (pol2)-[:APPLIES_TO]->(org2:Organization)
        RETURN pol1, pol2, org1, org2,
               pol1.name as policy_name,
               pol1.content as org1_content,
               pol2.content as org2_content
        """
        
        with self.client.driver.session() as session:
            result = session.run(query)
            return [record.data() for record in result]
    
    def find_key_personnel_by_role(self, role: str) -> List[Dict]:
        """Find key personnel by role across organizations"""
        query = """
        MATCH (p:Person)
        WHERE p.role CONTAINS $role
        MATCH (p)-[:WORKS_AT]->(org:Organization)
        OPTIONAL MATCH (p)-[:MEMBER_OF|PRESIDES_OVER]->(committee:Committee)
        RETURN p, org, collect(DISTINCT committee) as committees
        ORDER BY org.name, p.name
        """
        
        with self.client.driver.session() as session:
            result = session.run(query, {'role': role})
            return [record.data() for record in result]
    
    def get_organization_metrics(self, org_name: str) -> Dict:
        """Get comprehensive metrics for an organization"""
        queries = {
            'employee_count': """
                MATCH (org:Organization)-[:WORKS_AT]-(p:Person)
                WHERE org.name CONTAINS $org_name
                RETURN count(p) as count
            """,
            'policy_count': """
                MATCH (org:Organization)<-[:APPLIES_TO]-(pol:Policy)
                WHERE org.name CONTAINS $org_name
                RETURN count(pol) as count
            """,
            'committee_count': """
                MATCH (org:Organization)<-[:BELONGS_TO]-(c:Committee)
                WHERE org.name CONTAINS $org_name
                RETURN count(c) as count
            """,
            'subsidiary_count': """
                MATCH (org:Organization)-[:PARENT_OF]->(child:Organization)
                WHERE org.name CONTAINS $org_name
                RETURN count(child) as count
            """
        }
        
        metrics = {}
        with self.client.driver.session() as session:
            for metric_name, query in queries.items():
                result = session.run(query, {'org_name': org_name})
                record = result.single()
                metrics[metric_name] = record['count'] if record else 0
        
        return metrics