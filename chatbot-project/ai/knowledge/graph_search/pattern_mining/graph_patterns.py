from typing import Dict, List, Any, <PERSON><PERSON>
import networkx as nx
from collections import defaultdict, Counter

class GraphPatternMiner:
    def __init__(self, neo4j_client):
        self.client = neo4j_client
        self.patterns = {}
        
    def mine_frequent_patterns(self, min_support: int = 2) -> Dict[str, Any]:
        """Mine frequent patterns in the graph"""
        patterns = {
            "node_patterns": self.mine_node_patterns(min_support),
            "edge_patterns": self.mine_edge_patterns(min_support),
            "subgraph_patterns": self.mine_subgraph_patterns(min_support)
        }
        
        self.patterns = patterns
        return patterns
    
    def mine_node_patterns(self, min_support: int) -> List[Dict[str, Any]]:
        """Mine frequent node patterns"""
        try:
            query = """
            MATCH (n)
            RETURN labels(n) as node_labels, count(n) as frequency
            ORDER BY frequency DESC
            """
            
            with self.client.driver.session() as session:
                result = session.run(query)
                patterns = []
                
                for record in result:
                    frequency = record["frequency"]
                    if frequency >= min_support:
                        patterns.append({
                            "pattern": record["node_labels"],
                            "frequency": frequency,
                            "type": "node_pattern"
                        })
                
                return patterns
        except:
            return []
    
    def mine_edge_patterns(self, min_support: int) -> List[Dict[str, Any]]:
        """Mine frequent edge patterns"""
        try:
            query = """
            MATCH (a)-[r]->(b)
            RETURN type(r) as relationship_type, 
                   labels(a) as source_labels,
                   labels(b) as target_labels,
                   count(r) as frequency
            ORDER BY frequency DESC
            """
            
            with self.client.driver.session() as session:
                result = session.run(query)
                patterns = []
                
                for record in result:
                    frequency = record["frequency"]
                    if frequency >= min_support:
                        patterns.append({
                            "pattern": {
                                "relationship": record["relationship_type"],
                                "source": record["source_labels"],
                                "target": record["target_labels"]
                            },
                            "frequency": frequency,
                            "type": "edge_pattern"
                        })
                
                return patterns
        except:
            return []
    
    def mine_subgraph_patterns(self, min_support: int) -> List[Dict[str, Any]]:
        """Mine frequent subgraph patterns"""
        try:
            # Simple 2-hop patterns
            query = """
            MATCH (a)-[r1]->(b)-[r2]->(c)
            RETURN labels(a) as node1_labels,
                   type(r1) as rel1_type,
                   labels(b) as node2_labels,
                   type(r2) as rel2_type,
                   labels(c) as node3_labels,
                   count(*) as frequency
            ORDER BY frequency DESC
            """
            
            with self.client.driver.session() as session:
                result = session.run(query)
                patterns = []
                
                for record in result:
                    frequency = record["frequency"]
                    if frequency >= min_support:
                        patterns.append({
                            "pattern": {
                                "nodes": [
                                    record["node1_labels"],
                                    record["node2_labels"],
                                    record["node3_labels"]
                                ],
                                "relationships": [
                                    record["rel1_type"],
                                    record["rel2_type"]
                                ]
                            },
                            "frequency": frequency,
                            "type": "subgraph_pattern"
                        })
                
                return patterns
        except:
            return []
    
    def find_pattern_instances(self, pattern: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find instances of a specific pattern"""
        pattern_type = pattern.get("type", "")
        
        if pattern_type == "edge_pattern":
            return self.find_edge_pattern_instances(pattern)
        elif pattern_type == "subgraph_pattern":
            return self.find_subgraph_pattern_instances(pattern)
        
        return []
    
    def find_edge_pattern_instances(self, pattern: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find instances of edge pattern"""
        try:
            rel_type = pattern["pattern"]["relationship"]
            query = f"""
            MATCH (a)-[r:{rel_type}]->(b)
            RETURN a.name as source_name, b.name as target_name, r
            LIMIT 10
            """
            
            with self.client.driver.session() as session:
                result = session.run(query)
                instances = []
                
                for record in result:
                    instances.append({
                        "source": record["source_name"],
                        "target": record["target_name"],
                        "relationship": rel_type
                    })
                
                return instances
        except:
            return []
    
    def find_subgraph_pattern_instances(self, pattern: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find instances of subgraph pattern"""
        try:
            rels = pattern["pattern"]["relationships"]
            if len(rels) >= 2:
                query = f"""
                MATCH (a)-[r1:{rels[0]}]->(b)-[r2:{rels[1]}]->(c)
                RETURN a.name as node1, b.name as node2, c.name as node3
                LIMIT 10
                """
                
                with self.client.driver.session() as session:
                    result = session.run(query)
                    instances = []
                    
                    for record in result:
                        instances.append({
                            "nodes": [record["node1"], record["node2"], record["node3"]],
                            "pattern_type": "2-hop"
                        })
                    
                    return instances
        except:
            return []
    
    def get_pattern_statistics(self) -> Dict[str, Any]:
        """Get statistics about discovered patterns"""
        if not self.patterns:
            return {"status": "no_patterns_mined"}
        
        stats = {}
        for pattern_type, patterns in self.patterns.items():
            stats[pattern_type] = {
                "count": len(patterns),
                "total_frequency": sum(p.get("frequency", 0) for p in patterns),
                "avg_frequency": sum(p.get("frequency", 0) for p in patterns) / len(patterns) if patterns else 0
            }
        
        return stats
    
    def suggest_new_relationships(self) -> List[Dict[str, Any]]:
        """Suggest potential new relationships based on patterns"""
        suggestions = []
        
        # Simple heuristic: if A->B and B->C are common, suggest A->C
        try:
            query = """
            MATCH (a)-[r1]->(b)-[r2]->(c)
            WHERE NOT (a)-[]->(c)
            RETURN a.name as source, c.name as target, 
                   type(r1) as rel1, type(r2) as rel2,
                   count(*) as frequency
            ORDER BY frequency DESC
            LIMIT 5
            """
            
            with self.client.driver.session() as session:
                result = session.run(query)
                
                for record in result:
                    suggestions.append({
                        "source": record["source"],
                        "target": record["target"],
                        "suggested_relationship": "RELATED_TO",
                        "reasoning": f"Connected via {record['rel1']} and {record['rel2']}",
                        "confidence": min(record["frequency"] / 10.0, 1.0)
                    })
        except:
            pass
        
        return suggestions