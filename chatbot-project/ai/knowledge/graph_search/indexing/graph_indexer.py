import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from ..neo4j_client import Neo4jClient
from ..models.graph_models import Organization, Person, Policy, Committee
from knowledge.document_processor import DocumentProcessor
from typing import List, Dict
import hashlib

class GraphIndexer:
    def __init__(self, neo4j_client: Neo4jClient):
        self.client = neo4j_client
        self.doc_processor = DocumentProcessor()
    
    def index_organizations(self) -> Dict:
        """Index all organizations from documents"""
        organizations = [
            Organization(
                id="nuvo_ai",
                name="NUVO AI",
                legal_name="NUVO AI Pvt Ltd",
                address="India"
            ),
            Organization(
                id="meril_group",
                name="Meril Group",
                legal_name="Meril Group of Companies"
            ),
            Organization(
                id="meril_life_sciences",
                name="Meril Life Sciences",
                legal_name="Meril Life Sciences Pvt. Ltd.",
                parent_id="meril_group",
                address="Bilakhia House, Survey No.135/139, <PERSON><PERSON><PERSON>nd <PERSON>, Chala, Vapi-396191, Gujarat, India",
                phone="+91 ***********",
                email="<EMAIL>",
                website="www.merillife.com",
                cin="U24239GJ2007PTC051137"
            ),
            Organization(
                id="meril_healthcare",
                name="Meril Healthcare",
                legal_name="Meril Healthcare Pvt. Ltd.",
                parent_id="meril_life_sciences"
            ),
            Organization(
                id="meril_diagnostics", 
                name="Meril Diagnostics",
                legal_name="Meril Diagnostics Pvt. Ltd.",
                parent_id="meril_life_sciences"
            ),
            Organization(
                id="meril_endo_surgery",
                name="Meril Endo-Surgery",
                legal_name="Meril Endo-Surgery Pvt. Ltd.",
                parent_id="meril_life_sciences"
            )
        ]
        
        created_count = 0
        for org in organizations:
            if self.client.create_organization(org):
                created_count += 1
        
        # Create parent-child relationships
        relationships_created = 0
        for org in organizations:
            if org.parent_id:
                if self.client.create_relationship(org.parent_id, org.id, "PARENT_OF"):
                    relationships_created += 1
                if self.client.create_relationship(org.id, org.parent_id, "SUBSIDIARY_OF"):
                    relationships_created += 1
        
        return {
            "organizations_created": created_count,
            "relationships_created": relationships_created
        }
    
    def index_personnel(self) -> Dict:
        """Index personnel from documents"""
        personnel = [
            # NUVO AI Personnel
            Person(
                id="ankita_desai",
                name="Ankita Desai",
                role="Policy Drafter",
                organization_id="nuvo_ai"
            ),
            Person(
                id="dhaval_pancholi",
                name="Dhaval Pancholi", 
                role="Policy Reviewer",
                organization_id="nuvo_ai"
            ),
            Person(
                id="dr_harshadkumar_panjkar",
                name="Dr. Harshadkumar Panjkar",
                role="Policy Approver",
                organization_id="nuvo_ai"
            ),
            # Meril Life Sciences Personnel
            Person(
                id="anita_nagar",
                name="Anita Nagar",
                role="Presiding Officer",
                organization_id="meril_life_sciences",
                email="<EMAIL>",
                phone="**********"
            ),
            Person(
                id="gayathri_nair",
                name="Gayathri Nair",
                role="DGM",
                organization_id="meril_life_sciences",
                email="<EMAIL>",
                phone="**********"
            ),
            # Meril Healthcare Personnel
            Person(
                id="pallabi_sarkar",
                name="Pallabi Sarkar",
                role="DGM",
                organization_id="meril_healthcare",
                email="<EMAIL>",
                phone="**********"
            ),
            # Meril Diagnostics Personnel
            Person(
                id="twisha_hathi",
                name="Twisha Hathi",
                role="DGM", 
                organization_id="meril_diagnostics",
                email="<EMAIL>",
                phone="**********"
            ),
            # Meril Endo-Surgery Personnel
            Person(
                id="ami_rughani",
                name="Ami Rughani",
                role="DGM",
                organization_id="meril_endo_surgery",
                email="<EMAIL>",
                phone="**********"
            )
        ]
        
        created_count = 0
        for person in personnel:
            if self.client.create_person(person):
                created_count += 1
        
        # Create WORKS_AT relationships
        relationships_created = 0
        for person in personnel:
            if self.client.create_relationship(person.id, person.organization_id, "WORKS_AT"):
                relationships_created += 1
        
        return {
            "personnel_created": created_count,
            "relationships_created": relationships_created
        }
    
    def index_policies(self) -> Dict:
        """Index policies from documents"""
        policies = [
            Policy(
                id="nuvo_privilege_leave",
                name="Privilege Leave Policy",
                type="HR Policy",
                organization_id="nuvo_ai",
                content="30 days per year, credited January 7th, minimum 3 days per application",
                version="NAIPL001"
            ),
            Policy(
                id="nuvo_sexual_harassment",
                name="Sexual Harassment Prevention",
                type="Compliance Policy",
                organization_id="nuvo_ai",
                content="Guidelines to ensure prevention of sexual harassment at workplace"
            ),
            Policy(
                id="meril_privilege_leave",
                name="Privilege Leave Policy", 
                type="HR Policy",
                organization_id="meril_life_sciences",
                content="30 days per year, 240 working days eligibility, encashment up to 60 days",
                effective_date="2022-01-01"
            ),
            Policy(
                id="meril_travel_policy",
                name="Travel Policy",
                type="Administrative Policy",
                organization_id="meril_life_sciences",
                content="Domestic and international travel guidelines, booking through Meril Travel Portal"
            )
        ]
        
        created_count = 0
        for policy in policies:
            if self.client.create_policy(policy):
                created_count += 1
        
        # Create APPLIES_TO relationships
        relationships_created = 0
        for policy in policies:
            if self.client.create_relationship(policy.id, policy.organization_id, "APPLIES_TO"):
                relationships_created += 1
        
        return {
            "policies_created": created_count,
            "relationships_created": relationships_created
        }
    
    def index_committees(self) -> Dict:
        """Index committees from documents"""
        committees = [
            Committee(
                id="meril_harassment_committee",
                name="Sexual Harassment Prevention Committee",
                type="Compliance Committee",
                organization_id="meril_life_sciences",
                established_date="2022-01-17",
                valid_until="2025-01-16"
            ),
            Committee(
                id="meril_healthcare_harassment_committee",
                name="Sexual Harassment Prevention Committee",
                type="Compliance Committee", 
                organization_id="meril_healthcare",
                established_date="2022-01-17",
                valid_until="2025-01-16"
            )
        ]
        
        # Create committee nodes
        created_count = 0
        for committee in committees:
            query = """
            MERGE (c:Committee {id: $id})
            SET c.name = $name,
                c.type = $type,
                c.established_date = $established_date,
                c.valid_until = $valid_until
            RETURN c
            """
            
            with self.client.driver.session() as session:
                result = session.run(query, {
                    'id': committee.id,
                    'name': committee.name,
                    'type': committee.type,
                    'established_date': committee.established_date,
                    'valid_until': committee.valid_until
                })
                if result.single():
                    created_count += 1
        
        # Create committee relationships
        relationships_created = 0
        committee_memberships = [
            ("anita_nagar", "meril_harassment_committee", "PRESIDES_OVER"),
            ("gayathri_nair", "meril_harassment_committee", "MEMBER_OF"),
            ("pallabi_sarkar", "meril_healthcare_harassment_committee", "PRESIDES_OVER")
        ]
        
        for person_id, committee_id, rel_type in committee_memberships:
            if self.client.create_relationship(person_id, committee_id, rel_type):
                relationships_created += 1
        
        return {
            "committees_created": created_count,
            "relationships_created": relationships_created
        }
    
    def index_all(self) -> Dict:
        """Index all entities and relationships"""
        print("🔄 Initializing graph schema...")
        self.client.initialize_schema()
        
        print("🏢 Indexing organizations...")
        org_stats = self.index_organizations()
        
        print("👥 Indexing personnel...")
        person_stats = self.index_personnel()
        
        print("📋 Indexing policies...")
        policy_stats = self.index_policies()
        
        print("🏛️ Indexing committees...")
        committee_stats = self.index_committees()
        
        return {
            "organizations": org_stats,
            "personnel": person_stats,
            "policies": policy_stats,
            "committees": committee_stats,
            "total_stats": self.client.get_graph_statistics()
        }