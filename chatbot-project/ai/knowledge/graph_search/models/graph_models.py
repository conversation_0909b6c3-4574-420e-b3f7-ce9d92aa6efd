from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class Organization:
    id: str
    name: str
    legal_name: str
    parent_id: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    cin: Optional[str] = None

@dataclass
class Person:
    id: str
    name: str
    role: str
    organization_id: str
    email: Optional[str] = None
    phone: Optional[str] = None
    department: Optional[str] = None

@dataclass
class Policy:
    id: str
    name: str
    type: str
    organization_id: str
    content: str
    effective_date: Optional[str] = None
    version: Optional[str] = None

@dataclass
class Committee:
    id: str
    name: str
    type: str
    organization_id: str
    established_date: Optional[str] = None
    valid_until: Optional[str] = None

@dataclass
class Document:
    id: str
    filename: str
    organization_id: str
    content: str
    file_path: str
    processed_at: str

class GraphSchema:
    @staticmethod
    def get_node_constraints():
        return [
            "CREATE CONSTRAINT org_id IF NOT EXISTS FOR (o:Organization) REQUIRE o.id IS UNIQUE",
            "CREATE CONSTRAINT person_id IF NOT EXISTS FOR (p:Person) REQUIRE p.id IS UNIQUE", 
            "CREATE CONSTRAINT policy_id IF NOT EXISTS FOR (pol:Policy) REQUIRE pol.id IS UNIQUE",
            "CREATE CONSTRAINT committee_id IF NOT EXISTS FOR (c:Committee) REQUIRE c.id IS UNIQUE",
            "CREATE CONSTRAINT doc_id IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE"
        ]
    
    @staticmethod
    def get_relationship_types():
        return [
            "WORKS_AT",
            "PARENT_OF", 
            "SUBSIDIARY_OF",
            "MEMBER_OF",
            "PRESIDES_OVER",
            "APPLIES_TO",
            "BELONGS_TO",
            "REPORTS_TO"
        ]