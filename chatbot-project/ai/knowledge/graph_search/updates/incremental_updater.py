from typing import Dict, List, Any
from datetime import datetime
import hashlib

class IncrementalGraphUpdater:
    def __init__(self, neo4j_client):
        self.client = neo4j_client
        self.update_log = []
        self.entity_hashes = {}
        
    def update_graph_from_documents(self, new_documents: List[Dict]) -> Dict[str, Any]:
        """Incrementally update graph from new documents"""
        updates = {"nodes_added": 0, "nodes_updated": 0, "relationships_added": 0}
        
        for doc in new_documents:
            doc_updates = self.process_document_updates(doc)
            for key in updates:
                updates[key] += doc_updates.get(key, 0)
        
        return updates
    
    def process_document_updates(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Process updates from a single document"""
        updates = {"nodes_added": 0, "nodes_updated": 0, "relationships_added": 0}
        
        entities = self.extract_entities_from_document(document)
        
        for entity in entities:
            if self.is_entity_new_or_changed(entity):
                if self.entity_exists(entity["id"]):
                    self.update_entity(entity)
                    updates["nodes_updated"] += 1
                else:
                    self.create_entity(entity)
                    updates["nodes_added"] += 1
        
        return updates
    
    def extract_entities_from_document(self, document: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract entities from document content"""
        content = document.get("content", "")
        org = document.get("organization", "")
        
        entities = []
        
        if org:
            entities.append({
                "id": f"org_{org.lower().replace(' ', '_')}",
                "type": "Organization",
                "name": org
            })
        
        people = ["Anita Nagar", "Twisha Hathi", "Ami Rughani"]
        for person in people:
            if person.lower() in content.lower():
                entities.append({
                    "id": f"person_{person.lower().replace(' ', '_')}",
                    "type": "Person",
                    "name": person
                })
        
        return entities
    
    def is_entity_new_or_changed(self, entity: Dict[str, Any]) -> bool:
        """Check if entity is new or has changed"""
        entity_id = entity["id"]
        current_hash = hashlib.md5(str(entity).encode()).hexdigest()
        
        if entity_id not in self.entity_hashes:
            self.entity_hashes[entity_id] = current_hash
            return True
        
        if self.entity_hashes[entity_id] != current_hash:
            self.entity_hashes[entity_id] = current_hash
            return True
        
        return False
    
    def entity_exists(self, entity_id: str) -> bool:
        """Check if entity exists in graph"""
        try:
            query = "MATCH (n) WHERE n.id = $entity_id RETURN count(n) as count"
            with self.client.driver.session() as session:
                result = session.run(query, {"entity_id": entity_id})
                record = result.single()
                return record["count"] > 0 if record else False
        except:
            return False
    
    def create_entity(self, entity: Dict[str, Any]):
        """Create new entity in graph"""
        try:
            if entity["type"] == "Organization":
                query = "CREATE (o:Organization {id: $id, name: $name})"
            elif entity["type"] == "Person":
                query = "CREATE (p:Person {id: $id, name: $name})"
            else:
                return
            
            with self.client.driver.session() as session:
                session.run(query, {"id": entity["id"], "name": entity["name"]})
        except:
            pass
    
    def update_entity(self, entity: Dict[str, Any]):
        """Update existing entity in graph"""
        try:
            query = "MATCH (n {id: $id}) SET n.name = $name"
            with self.client.driver.session() as session:
                session.run(query, {"id": entity["id"], "name": entity["name"]})
        except:
            pass