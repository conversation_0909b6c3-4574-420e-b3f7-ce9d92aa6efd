from neo4j import GraphDatabase
from typing import Dict, List, Optional, Any
import os
from .models.graph_models import Organization, Person, Policy, Committee, Document, GraphSchema

class Neo4jClient:
    def __init__(self, uri: str = "bolt://localhost:7687", user: str = "neo4j", password: str = "password"):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        self.schema = GraphSchema()
    
    def close(self):
        self.driver.close()
    
    def initialize_schema(self):
        """Initialize graph schema with constraints"""
        with self.driver.session() as session:
            for constraint in self.schema.get_node_constraints():
                try:
                    session.run(constraint)
                except Exception as e:
                    print(f"Constraint already exists or error: {e}")
    
    def clear_database(self):
        """Clear all nodes and relationships"""
        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
    
    def create_organization(self, org: Organization) -> bool:
        """Create organization node"""
        query = """
        MERGE (o:Organization {id: $id})
        SET o.name = $name,
            o.legal_name = $legal_name,
            o.address = $address,
            o.phone = $phone,
            o.email = $email,
            o.website = $website,
            o.cin = $cin
        RETURN o
        """
        
        with self.driver.session() as session:
            result = session.run(query, {
                'id': org.id,
                'name': org.name,
                'legal_name': org.legal_name,
                'address': org.address,
                'phone': org.phone,
                'email': org.email,
                'website': org.website,
                'cin': org.cin
            })
            return result.single() is not None
    
    def create_person(self, person: Person) -> bool:
        """Create person node"""
        query = """
        MERGE (p:Person {id: $id})
        SET p.name = $name,
            p.role = $role,
            p.email = $email,
            p.phone = $phone,
            p.department = $department
        RETURN p
        """
        
        with self.driver.session() as session:
            result = session.run(query, {
                'id': person.id,
                'name': person.name,
                'role': person.role,
                'email': person.email,
                'phone': person.phone,
                'department': person.department
            })
            return result.single() is not None
    
    def create_policy(self, policy: Policy) -> bool:
        """Create policy node"""
        query = """
        MERGE (pol:Policy {id: $id})
        SET pol.name = $name,
            pol.type = $type,
            pol.content = $content,
            pol.effective_date = $effective_date,
            pol.version = $version
        RETURN pol
        """
        
        with self.driver.session() as session:
            result = session.run(query, {
                'id': policy.id,
                'name': policy.name,
                'type': policy.type,
                'content': policy.content,
                'effective_date': policy.effective_date,
                'version': policy.version
            })
            return result.single() is not None
    
    def create_relationship(self, from_id: str, to_id: str, relationship_type: str, properties: Dict = None) -> bool:
        """Create relationship between nodes"""
        query = f"""
        MATCH (a), (b)
        WHERE a.id = $from_id AND b.id = $to_id
        MERGE (a)-[r:{relationship_type}]->(b)
        """
        
        if properties:
            set_clause = ", ".join([f"r.{key} = ${key}" for key in properties.keys()])
            query += f" SET {set_clause}"
        
        query += " RETURN r"
        
        params = {'from_id': from_id, 'to_id': to_id}
        if properties:
            params.update(properties)
        
        with self.driver.session() as session:
            result = session.run(query, params)
            return result.single() is not None
    
    def find_organization_hierarchy(self, org_id: str) -> List[Dict]:
        """Find organization hierarchy"""
        query = """
        MATCH (org:Organization {id: $org_id})
        OPTIONAL MATCH (org)-[:PARENT_OF*]->(child:Organization)
        OPTIONAL MATCH (parent:Organization)-[:PARENT_OF*]->(org)
        RETURN org, collect(DISTINCT child) as children, collect(DISTINCT parent) as parents
        """
        
        with self.driver.session() as session:
            result = session.run(query, {'org_id': org_id})
            return [record.data() for record in result]
    
    def find_person_connections(self, person_id: str) -> List[Dict]:
        """Find all connections for a person"""
        query = """
        MATCH (p:Person {id: $person_id})
        OPTIONAL MATCH (p)-[r1:WORKS_AT]->(org:Organization)
        OPTIONAL MATCH (p)-[r2:MEMBER_OF]->(committee:Committee)
        OPTIONAL MATCH (p)-[r3:PRESIDES_OVER]->(committee2:Committee)
        RETURN p, org, committee, committee2, r1, r2, r3
        """
        
        with self.driver.session() as session:
            result = session.run(query, {'person_id': person_id})
            return [record.data() for record in result]
    
    def search_by_organization(self, org_name: str) -> List[Dict]:
        """Search for all entities related to an organization"""
        query = """
        MATCH (org:Organization)
        WHERE org.name CONTAINS $org_name OR org.legal_name CONTAINS $org_name
        OPTIONAL MATCH (org)<-[:WORKS_AT]-(person:Person)
        OPTIONAL MATCH (org)<-[:APPLIES_TO]-(policy:Policy)
        OPTIONAL MATCH (org)<-[:BELONGS_TO]-(committee:Committee)
        RETURN org, collect(DISTINCT person) as people, 
               collect(DISTINCT policy) as policies,
               collect(DISTINCT committee) as committees
        """
        
        with self.driver.session() as session:
            result = session.run(query, {'org_name': org_name})
            return [record.data() for record in result]
    
    def find_policy_stakeholders(self, policy_name: str) -> List[Dict]:
        """Find all stakeholders related to a policy"""
        query = """
        MATCH (pol:Policy)
        WHERE pol.name CONTAINS $policy_name
        MATCH (pol)-[:APPLIES_TO]->(org:Organization)
        OPTIONAL MATCH (org)<-[:WORKS_AT]-(person:Person)
        OPTIONAL MATCH (org)<-[:BELONGS_TO]-(committee:Committee)
        OPTIONAL MATCH (committee)<-[:MEMBER_OF]-(member:Person)
        RETURN pol, org, collect(DISTINCT person) as employees,
               collect(DISTINCT committee) as committees,
               collect(DISTINCT member) as committee_members
        """
        
        with self.driver.session() as session:
            result = session.run(query, {'policy_name': policy_name})
            return [record.data() for record in result]
    
    def get_graph_statistics(self) -> Dict:
        """Get graph database statistics"""
        queries = {
            'organizations': "MATCH (o:Organization) RETURN count(o) as count",
            'people': "MATCH (p:Person) RETURN count(p) as count",
            'policies': "MATCH (pol:Policy) RETURN count(pol) as count",
            'committees': "MATCH (c:Committee) RETURN count(c) as count",
            'relationships': "MATCH ()-[r]->() RETURN count(r) as count"
        }
        
        stats = {}
        with self.driver.session() as session:
            for key, query in queries.items():
                result = session.run(query)
                stats[key] = result.single()['count']
        
        return stats