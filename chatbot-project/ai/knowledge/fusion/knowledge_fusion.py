import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from ..vector_search.enhanced_vector_search import EnhancedVectorSearch
from ..graph_search.neo4j_client import Neo4jClient
from ..graph_search.queries.graph_queries import GraphQueries
from typing import Dict, List, Optional, Tuple

class KnowledgeFusion:
    def __init__(self):
        self.vector_search = EnhancedVectorSearch()
        self.neo4j_client = None
        self.graph_queries = None
        self.fusion_weights = {
            'vector_score': 0.4,
            'graph_relevance': 0.3,
            'entity_match': 0.2,
            'cross_validation': 0.1
        }
    
    def initialize(self, documents: List[Dict], neo4j_uri: str = None):
        """Initialize both vector and graph knowledge systems"""
        # Initialize vector search
        self.vector_search.initialize_from_documents(documents)
        
        # Initialize graph search if Neo4j available
        if neo4j_uri:
            try:
                self.neo4j_client = Neo4jClient(neo4j_uri, "neo4j", "password")
                self.graph_queries = GraphQueries(self.neo4j_client)
            except:
                print("Neo4j not available, using vector search only")
    
    def fused_search(self, query: str, k: int = 10, user_context: Dict = None) -> List[Dict]:
        """Perform fused search combining vector and graph results"""
        # Get vector search results
        vector_results = self.vector_search.search(query, k * 2, user_context, "hybrid")
        
        # Get graph-enhanced results if available
        if self.graph_queries:
            graph_enhanced_results = self._enhance_with_graph(query, vector_results, user_context)
        else:
            graph_enhanced_results = vector_results
        
        # Apply fusion scoring
        fused_results = self._apply_fusion_scoring(query, graph_enhanced_results, user_context)
        
        # Cross-validate results
        validated_results = self._cross_validate_results(fused_results)
        
        return validated_results[:k]
    
    def _enhance_with_graph(self, query: str, vector_results: List[Dict], user_context: Dict) -> List[Dict]:
        """Enhance vector results with graph knowledge"""
        enhanced_results = []
        
        for result in vector_results:
            enhanced_result = result.copy()
            
            # Extract entities from result text
            entities = self._extract_entities_from_text(result['text'])
            
            # Get graph context for entities
            graph_context = self._get_graph_context(entities, user_context)
            
            # Add graph information
            enhanced_result['graph_context'] = graph_context
            enhanced_result['entity_connections'] = self._find_entity_connections(entities)
            enhanced_result['organizational_relevance'] = self._calculate_org_relevance(entities, user_context)
            
            enhanced_results.append(enhanced_result)
        
        return enhanced_results
    
    def _extract_entities_from_text(self, text: str) -> List[str]:
        """Extract key entities from text"""
        # Simple entity extraction - in production, use NER
        entities = []
        
        # Organization entities
        orgs = ['NUVO AI', 'Meril Life Sciences', 'Meril Healthcare', 'Meril Diagnostics', 'Meril Endo-Surgery']
        for org in orgs:
            if org.lower() in text.lower():
                entities.append(org)
        
        # Personnel entities  
        people = ['Anita Nagar', 'Twisha Hathi', 'Ami Rughani', 'Pallabi Sarkar', 'Ankita Desai']
        for person in people:
            if person.lower() in text.lower():
                entities.append(person)
        
        # Policy entities
        policies = ['privilege leave', 'sexual harassment', 'maternity leave', 'travel policy']
        for policy in policies:
            if policy.lower() in text.lower():
                entities.append(policy)
        
        return entities
    
    def _get_graph_context(self, entities: List[str], user_context: Dict) -> Dict:
        """Get graph context for entities"""
        if not self.graph_queries:
            return {}
        
        context = {}
        
        for entity in entities:
            try:
                # Try different entity types
                org_info = self.graph_queries.find_organization_details(entity)
                if org_info:
                    context[entity] = {'type': 'organization', 'info': org_info[0]}
                    continue
                
                person_info = self.graph_queries.find_person_profile(entity)
                if person_info:
                    context[entity] = {'type': 'person', 'info': person_info[0]}
                    continue
                
                policy_info = self.graph_queries.find_policy_implementation(entity)
                if policy_info:
                    context[entity] = {'type': 'policy', 'info': policy_info[0]}
            except:
                continue
        
        return context
    
    def _find_entity_connections(self, entities: List[str]) -> List[Dict]:
        """Find connections between entities"""
        connections = []
        
        if not self.graph_queries or len(entities) < 2:
            return connections
        
        for i, entity1 in enumerate(entities):
            for entity2 in entities[i+1:]:
                try:
                    connection = self.graph_queries.find_cross_organizational_connections(entity1, entity2)
                    if connection:
                        connections.append({
                            'entity1': entity1,
                            'entity2': entity2,
                            'connection': connection[0]
                        })
                except:
                    continue
        
        return connections
    
    def _calculate_org_relevance(self, entities: List[str], user_context: Dict) -> float:
        """Calculate organizational relevance score"""
        if not user_context or 'organization' not in user_context:
            return 0.5
        
        user_org = user_context['organization'].lower()
        relevance_score = 0.0
        
        for entity in entities:
            if user_org in entity.lower():
                relevance_score += 0.3
            elif any(org.lower() in entity.lower() for org in ['meril', 'nuvo']):
                relevance_score += 0.1
        
        return min(relevance_score, 1.0)
    
    def _apply_fusion_scoring(self, query: str, results: List[Dict], user_context: Dict) -> List[Dict]:
        """Apply fusion scoring to combine different signals"""
        scored_results = []
        
        for result in results:
            # Base vector score
            vector_score = result.get('final_score', result.get('score', 0))
            
            # Graph relevance score
            graph_relevance = len(result.get('graph_context', {})) * 0.2
            
            # Entity match score
            entity_match = len(result.get('entity_connections', [])) * 0.1
            
            # Cross-validation score (placeholder)
            cross_validation = 0.5
            
            # Calculate fusion score
            fusion_score = (
                self.fusion_weights['vector_score'] * vector_score +
                self.fusion_weights['graph_relevance'] * graph_relevance +
                self.fusion_weights['entity_match'] * entity_match +
                self.fusion_weights['cross_validation'] * cross_validation
            )
            
            result['fusion_score'] = fusion_score
            result['score_breakdown'] = {
                'vector_score': vector_score,
                'graph_relevance': graph_relevance,
                'entity_match': entity_match,
                'cross_validation': cross_validation
            }
            
            scored_results.append(result)
        
        # Sort by fusion score
        scored_results.sort(key=lambda x: x['fusion_score'], reverse=True)
        return scored_results
    
    def _cross_validate_results(self, results: List[Dict]) -> List[Dict]:
        """Cross-validate results for consistency"""
        validated_results = []
        
        for result in results:
            # Check for contradictions
            has_contradiction = self._check_contradictions(result, results)
            
            # Adjust confidence based on validation
            if has_contradiction:
                result['fusion_score'] *= 0.8
                result['validation_warning'] = "Potential contradiction detected"
            else:
                result['validation_status'] = "Validated"
            
            validated_results.append(result)
        
        return validated_results
    
    def _check_contradictions(self, result: Dict, all_results: List[Dict]) -> bool:
        """Check for contradictions between results"""
        # Simple contradiction detection
        result_text = result['text'].lower()
        
        for other_result in all_results:
            if other_result == result:
                continue
            
            other_text = other_result['text'].lower()
            
            # Check for opposing statements (simplified)
            if ('not' in result_text and 'not' not in other_text) or \
               ('not' not in result_text and 'not' in other_text):
                # Check if they're talking about the same topic
                common_entities = set(result.get('graph_context', {}).keys()) & \
                                set(other_result.get('graph_context', {}).keys())
                if common_entities:
                    return True
        
        return False
    
    def get_knowledge_summary(self, query: str, user_context: Dict = None) -> Dict:
        """Get comprehensive knowledge summary for a query"""
        # Get fused search results
        results = self.fused_search(query, k=5, user_context=user_context)
        
        # Extract key information
        entities = set()
        organizations = set()
        policies = set()
        
        for result in results:
            entities.update(result.get('graph_context', {}).keys())
            
            for entity, info in result.get('graph_context', {}).items():
                if info['type'] == 'organization':
                    organizations.add(entity)
                elif info['type'] == 'policy':
                    policies.add(entity)
        
        return {
            'query': query,
            'total_results': len(results),
            'key_entities': list(entities),
            'organizations_involved': list(organizations),
            'policies_referenced': list(policies),
            'top_result_score': results[0]['fusion_score'] if results else 0,
            'knowledge_sources': ['vector_search', 'graph_database'] if self.graph_queries else ['vector_search']
        }