"""Advanced Knowledge Fusion Engine for Multi-Agent RAG System."""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np
from datetime import datetime
import json

class FusionStrategy(Enum):
    WEIGHTED_AVERAGE = "weighted_average"
    CONSENSUS_BASED = "consensus_based"
    HIERARCHICAL = "hierarchical"
    EVIDENCE_BASED = "evidence_based"

@dataclass
class KnowledgeSource:
    id: str
    content: str
    confidence: float
    source_type: str
    agent_id: str
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class FusedKnowledge:
    content: str
    confidence: float
    sources: List[str]
    fusion_strategy: str
    reasoning_trace: List[str]
    conflicts_resolved: List[Dict[str, Any]]

class KnowledgeFusionEngine:
    def __init__(self):
        self.fusion_strategies = {
            FusionStrategy.WEIGHTED_AVERAGE: self.weighted_average_fusion,
            FusionStrategy.CONSENSUS_BASED: self.consensus_based_fusion,
            FusionStrategy.HIERARCHICAL: self.hierarchical_fusion,
            FusionStrategy.EVIDENCE_BASED: self.evidence_based_fusion
        }
        self.source_weights = {
            "vector_search": 0.8,
            "graph_search": 0.9,
            "agent_reasoning": 0.7,
            "tool_execution": 0.85,
            "memory_retrieval": 0.6
        }
    
    async def fuse_multi_agent_results(self, agent_results: Dict[str, Any]) -> FusedKnowledge:
        """Fuse results from multiple agents"""
        knowledge_sources = self.extract_knowledge_sources(agent_results)
        
        if not knowledge_sources:
            return FusedKnowledge(
                content="No knowledge sources available for fusion",
                confidence=0.0,
                sources=[],
                fusion_strategy="none",
                reasoning_trace=["No sources to fuse"],
                conflicts_resolved=[]
            )
        
        # Detect and resolve conflicts
        conflicts = self.detect_conflicts(knowledge_sources)
        resolved_sources = await self.resolve_conflicts(knowledge_sources, conflicts)
        
        # Apply fusion strategy
        strategy = self.select_fusion_strategy(resolved_sources)
        fused_result = await self.fusion_strategies[strategy](resolved_sources)
        
        return fused_result
    
    def extract_knowledge_sources(self, agent_results: Dict[str, Any]) -> List[KnowledgeSource]:
        """Extract knowledge sources from agent results"""
        sources = []
        
        for agent_id, result in agent_results.items():
            if isinstance(result, dict) and "results" in result:
                for item in result["results"]:
                    if isinstance(item, dict) and "text" in item:
                        source = KnowledgeSource(
                            id=f"{agent_id}_{len(sources)}",
                            content=item["text"],
                            confidence=item.get("score", result.get("confidence", 0.5)),
                            source_type=result.get("reasoning_type", "unknown"),
                            agent_id=agent_id,
                            timestamp=datetime.now(),
                            metadata=item.get("metadata", {})
                        )
                        sources.append(source)
        
        return sources
    
    def detect_conflicts(self, sources: List[KnowledgeSource]) -> List[Dict[str, Any]]:
        """Detect conflicts between knowledge sources"""
        conflicts = []
        
        for i, source1 in enumerate(sources):
            for j, source2 in enumerate(sources[i+1:], i+1):
                conflict_score = self.calculate_conflict_score(source1, source2)
                
                if conflict_score > 0.7:  # High conflict threshold
                    conflicts.append({
                        "source1_id": source1.id,
                        "source2_id": source2.id,
                        "conflict_score": conflict_score,
                        "conflict_type": self.classify_conflict(source1, source2)
                    })
        
        return conflicts
    
    def calculate_conflict_score(self, source1: KnowledgeSource, source2: KnowledgeSource) -> float:
        """Calculate conflict score between two sources"""
        # Simple conflict detection based on content similarity and confidence difference
        content_similarity = self.calculate_content_similarity(source1.content, source2.content)
        confidence_diff = abs(source1.confidence - source2.confidence)
        
        # High similarity with high confidence difference indicates conflict
        if content_similarity > 0.8 and confidence_diff > 0.3:
            return 0.8
        
        # Different agents saying different things about similar topics
        if content_similarity > 0.5 and source1.agent_id != source2.agent_id:
            return 0.6
        
        return 0.0
    
    def calculate_content_similarity(self, content1: str, content2: str) -> float:
        """Calculate content similarity (simplified)"""
        words1 = set(content1.lower().split())
        words2 = set(content2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def classify_conflict(self, source1: KnowledgeSource, source2: KnowledgeSource) -> str:
        """Classify the type of conflict"""
        if source1.source_type != source2.source_type:
            return "source_type_conflict"
        elif source1.agent_id != source2.agent_id:
            return "agent_disagreement"
        else:
            return "content_conflict"
    
    async def resolve_conflicts(self, sources: List[KnowledgeSource], conflicts: List[Dict[str, Any]]) -> List[KnowledgeSource]:
        """Resolve conflicts between sources"""
        resolved_sources = sources.copy()
        
        for conflict in conflicts:
            source1_id = conflict["source1_id"]
            source2_id = conflict["source2_id"]
            
            source1 = next((s for s in resolved_sources if s.id == source1_id), None)
            source2 = next((s for s in resolved_sources if s.id == source2_id), None)
            
            if source1 and source2:
                resolution = await self.resolve_single_conflict(source1, source2, conflict)
                
                # Update sources based on resolution
                if resolution["action"] == "merge":
                    merged_source = self.merge_sources(source1, source2, resolution)
                    resolved_sources = [s for s in resolved_sources if s.id not in [source1_id, source2_id]]
                    resolved_sources.append(merged_source)
                elif resolution["action"] == "prefer":
                    preferred_id = resolution["preferred_source"]
                    resolved_sources = [s for s in resolved_sources if s.id != (source2_id if preferred_id == source1_id else source1_id)]
        
        return resolved_sources
    
    async def resolve_single_conflict(self, source1: KnowledgeSource, source2: KnowledgeSource, conflict: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve a single conflict between two sources"""
        conflict_type = conflict["conflict_type"]
        
        if conflict_type == "source_type_conflict":
            # Prefer higher-weighted source types
            weight1 = self.source_weights.get(source1.source_type, 0.5)
            weight2 = self.source_weights.get(source2.source_type, 0.5)
            
            if weight1 > weight2:
                return {"action": "prefer", "preferred_source": source1.id, "reason": "higher_source_weight"}
            elif weight2 > weight1:
                return {"action": "prefer", "preferred_source": source2.id, "reason": "higher_source_weight"}
            else:
                return {"action": "merge", "reason": "equal_source_weights"}
        
        elif conflict_type == "agent_disagreement":
            # Prefer higher confidence
            if source1.confidence > source2.confidence:
                return {"action": "prefer", "preferred_source": source1.id, "reason": "higher_confidence"}
            elif source2.confidence > source1.confidence:
                return {"action": "prefer", "preferred_source": source2.id, "reason": "higher_confidence"}
            else:
                return {"action": "merge", "reason": "equal_confidence"}
        
        else:  # content_conflict
            return {"action": "merge", "reason": "content_synthesis"}
    
    def merge_sources(self, source1: KnowledgeSource, source2: KnowledgeSource, resolution: Dict[str, Any]) -> KnowledgeSource:
        """Merge two conflicting sources"""
        merged_content = f"{source1.content} [MERGED WITH] {source2.content}"
        merged_confidence = (source1.confidence + source2.confidence) / 2
        
        return KnowledgeSource(
            id=f"merged_{source1.id}_{source2.id}",
            content=merged_content,
            confidence=merged_confidence,
            source_type="merged",
            agent_id=f"{source1.agent_id}+{source2.agent_id}",
            timestamp=datetime.now(),
            metadata={
                "merged_from": [source1.id, source2.id],
                "resolution_reason": resolution["reason"]
            }
        )
    
    def select_fusion_strategy(self, sources: List[KnowledgeSource]) -> FusionStrategy:
        """Select appropriate fusion strategy based on sources"""
        if len(sources) <= 1:
            return FusionStrategy.WEIGHTED_AVERAGE
        
        # Check source diversity
        source_types = set(s.source_type for s in sources)
        agent_ids = set(s.agent_id for s in sources)
        
        if len(source_types) > 2 and len(agent_ids) > 2:
            return FusionStrategy.CONSENSUS_BASED
        elif len(agent_ids) > 2:
            return FusionStrategy.HIERARCHICAL
        else:
            return FusionStrategy.EVIDENCE_BASED
    
    async def weighted_average_fusion(self, sources: List[KnowledgeSource]) -> FusedKnowledge:
        """Weighted average fusion strategy"""
        if not sources:
            return self.empty_fusion_result("weighted_average")
        
        total_weight = sum(s.confidence for s in sources)
        if total_weight == 0:
            return self.empty_fusion_result("weighted_average")
        
        # Weight sources by confidence
        weighted_content = []
        for source in sources:
            weight = source.confidence / total_weight
            weighted_content.append(f"[{weight:.2f}] {source.content}")
        
        fused_content = " | ".join(weighted_content)
        avg_confidence = total_weight / len(sources)
        
        return FusedKnowledge(
            content=fused_content,
            confidence=avg_confidence,
            sources=[s.id for s in sources],
            fusion_strategy="weighted_average",
            reasoning_trace=[f"Applied weighted average to {len(sources)} sources"],
            conflicts_resolved=[]
        )
    
    async def consensus_based_fusion(self, sources: List[KnowledgeSource]) -> FusedKnowledge:
        """Consensus-based fusion strategy"""
        if not sources:
            return self.empty_fusion_result("consensus_based")
        
        # Group sources by similarity
        consensus_groups = self.group_by_consensus(sources)
        
        # Select largest consensus group
        largest_group = max(consensus_groups, key=len)
        
        # Fuse within consensus group
        consensus_content = self.synthesize_consensus_content(largest_group)
        consensus_confidence = sum(s.confidence for s in largest_group) / len(largest_group)
        
        return FusedKnowledge(
            content=consensus_content,
            confidence=consensus_confidence,
            sources=[s.id for s in largest_group],
            fusion_strategy="consensus_based",
            reasoning_trace=[f"Selected consensus from {len(largest_group)} of {len(sources)} sources"],
            conflicts_resolved=[]
        )
    
    def empty_fusion_result(self, strategy: str) -> FusedKnowledge:
        """Return empty fusion result"""
        return FusedKnowledge(
            content="No content available for fusion",
            confidence=0.0,
            sources=[],
            fusion_strategy=strategy,
            reasoning_trace=["No sources available"],
            conflicts_resolved=[]
        )
