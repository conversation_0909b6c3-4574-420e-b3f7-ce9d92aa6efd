import React, { useState, useEffect, useContext } from 'react';
import './UserDashboard.css';
import { assets } from '../../assets/assets';
import { Context } from '../../context/Context';
import ChatInterface from '../Chat/ChatInterface';
import ReasoningVisualization from '../Reasoning/ReasoningVisualization';
import AgentActivity from '../Agents/AgentActivity';
import SourcesPanel from '../Sources/SourcesPanel';
import ConfidenceIndicator from '../Confidence/ConfidenceIndicator';

const UserDashboard = () => {
    const { 
        onSent, 
        recentPrompt, 
        showResult, 
        loading, 
        resultData, 
        setInput, 
        input,
        prevPrompts 
    } = useContext(Context);

    const [activeView, setActiveView] = useState('chat');
    const [showReasoningTrace, setShowReasoningTrace] = useState(false);
    const [showAgentActivity, setShowAgentActivity] = useState(false);
    const [showSources, setShowSources] = useState(false);
    const [userStats, setUserStats] = useState({
        totalQueries: 0,
        successRate: 0,
        avgResponseTime: 0,
        favoriteTopics: []
    });

    useEffect(() => {
        // Load user statistics
        loadUserStats();
    }, []);

    const loadUserStats = async () => {
        try {
            // Mock data - replace with actual API call
            setUserStats({
                totalQueries: 156,
                successRate: 94.2,
                avgResponseTime: 1.8,
                favoriteTopics: ['HR Policies', 'Technical Support', 'Leave Management']
            });
        } catch (error) {
            console.error('Failed to load user stats:', error);
        }
    };

    const handleQuerySubmit = async (query) => {
        setShowReasoningTrace(true);
        setShowAgentActivity(true);
        await onSent(query);
    };

    const viewOptions = [
        { id: 'chat', label: 'Chat', icon: assets.message_icon },
        { id: 'reasoning', label: 'Reasoning', icon: assets.bulb_icon },
        { id: 'agents', label: 'Agents', icon: assets.setting_icon },
        { id: 'sources', label: 'Sources', icon: assets.compass_icon }
    ];

    return (
        <div className="user-dashboard">
            {/* Header */}
            <header className="dashboard-header">
                <div className="header-left">
                    <h1>CHaBot Assistant</h1>
                    <span className="user-role">User Dashboard</span>
                </div>
                <div className="header-right">
                    <div className="user-stats">
                        <div className="stat-item">
                            <span className="stat-value">{userStats.totalQueries}</span>
                            <span className="stat-label">Queries</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{userStats.successRate}%</span>
                            <span className="stat-label">Success</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{userStats.avgResponseTime}s</span>
                            <span className="stat-label">Avg Time</span>
                        </div>
                    </div>
                    <div className="system-status">
                        <span className="status-indicator online">🟢 Online</span>
                    </div>
                </div>
            </header>

            {/* Navigation Tabs */}
            <nav className="dashboard-nav">
                {viewOptions.map(option => (
                    <button
                        key={option.id}
                        className={`nav-tab ${activeView === option.id ? 'active' : ''}`}
                        onClick={() => setActiveView(option.id)}
                    >
                        <img src={option.icon} alt={option.label} />
                        <span>{option.label}</span>
                    </button>
                ))}
            </nav>

            {/* Main Content Area */}
            <main className="dashboard-content">
                {activeView === 'chat' && (
                    <div className="chat-view">
                        <div className="chat-container">
                            <ChatInterface 
                                onQuerySubmit={handleQuerySubmit}
                                showEnhancedFeatures={true}
                            />
                        </div>
                        
                        {/* Side Panels */}
                        <div className="side-panels">
                            {showResult && (
                                <>
                                    <ConfidenceIndicator 
                                        confidence={resultData?.confidence || 0.8}
                                        reasoning={resultData?.reasoning_trace || []}
                                    />
                                    
                                    {showReasoningTrace && (
                                        <div className="panel reasoning-panel">
                                            <h3>Reasoning Process</h3>
                                            <ReasoningVisualization 
                                                trace={resultData?.reasoning_trace || []}
                                                isCompact={true}
                                            />
                                        </div>
                                    )}
                                    
                                    {showAgentActivity && (
                                        <div className="panel agents-panel">
                                            <h3>Agent Activity</h3>
                                            <AgentActivity 
                                                agents={resultData?.agents_involved || []}
                                                isCompact={true}
                                            />
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    </div>
                )}

                {activeView === 'reasoning' && (
                    <div className="reasoning-view">
                        <div className="view-header">
                            <h2>Reasoning Visualization</h2>
                            <p>Explore how CHaBot processes your queries</p>
                        </div>
                        <ReasoningVisualization 
                            trace={resultData?.reasoning_trace || []}
                            isFullView={true}
                        />
                    </div>
                )}

                {activeView === 'agents' && (
                    <div className="agents-view">
                        <div className="view-header">
                            <h2>Agent Activity</h2>
                            <p>See which agents are working on your queries</p>
                        </div>
                        <AgentActivity 
                            agents={resultData?.agents_involved || []}
                            isFullView={true}
                        />
                    </div>
                )}

                {activeView === 'sources' && (
                    <div className="sources-view">
                        <div className="view-header">
                            <h2>Knowledge Sources</h2>
                            <p>View the sources used to answer your questions</p>
                        </div>
                        <SourcesPanel 
                            sources={resultData?.sources || []}
                            isFullView={true}
                        />
                    </div>
                )}
            </main>

            {/* Quick Actions */}
            <div className="quick-actions">
                <button 
                    className="quick-action"
                    onClick={() => setShowReasoningTrace(!showReasoningTrace)}
                    title="Toggle Reasoning Trace"
                >
                    <img src={assets.bulb_icon} alt="Reasoning" />
                </button>
                <button 
                    className="quick-action"
                    onClick={() => setShowAgentActivity(!showAgentActivity)}
                    title="Toggle Agent Activity"
                >
                    <img src={assets.setting_icon} alt="Agents" />
                </button>
                <button 
                    className="quick-action"
                    onClick={() => setShowSources(!showSources)}
                    title="Toggle Sources"
                >
                    <img src={assets.compass_icon} alt="Sources" />
                </button>
            </div>

            {/* Recent Queries Sidebar */}
            <aside className="recent-queries">
                <h3>Recent Queries</h3>
                <div className="queries-list">
                    {prevPrompts.slice(0, 5).map((prompt, index) => (
                        <div key={index} className="query-item" onClick={() => onSent(prompt)}>
                            <img src={assets.message_icon} alt="Query" />
                            <span className="query-text">{prompt.slice(0, 40)}...</span>
                            <span className="query-time">2m ago</span>
                        </div>
                    ))}
                </div>
                
                <div className="favorite-topics">
                    <h4>Favorite Topics</h4>
                    {userStats.favoriteTopics.map((topic, index) => (
                        <span key={index} className="topic-tag">{topic}</span>
                    ))}
                </div>
            </aside>

            {/* Floating Help Button */}
            <button className="help-button" title="Help & Tips">
                <img src={assets.question_icon} alt="Help" />
            </button>
        </div>
    );
};

export default UserDashboard;
