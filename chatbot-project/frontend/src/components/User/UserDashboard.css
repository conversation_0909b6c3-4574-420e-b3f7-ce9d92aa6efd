.user-dashboard {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(135deg, #f0f4f9 0%, #e8f2ff 100%);
    font-family: 'Outfit', sans-serif;
    position: relative;
}

/* Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.header-left h1 {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.user-role {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 400;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 16px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 12px;
    min-width: 60px;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #3498db;
}

.stat-label {
    font-size: 12px;
    color: #7f8c8d;
    margin-top: 2px;
}

.system-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    font-size: 14px;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
}

.status-indicator.online {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
}

/* Navigation */
.dashboard-nav {
    display: flex;
    padding: 0 30px;
    background: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #7f8c8d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    color: #3498db;
    background: rgba(52, 152, 219, 0.05);
}

.nav-tab.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.nav-tab img {
    width: 16px;
    height: 16px;
    opacity: 0.7;
}

.nav-tab.active img {
    opacity: 1;
}

/* Main Content */
.dashboard-content {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
}

.chat-view {
    display: flex;
    gap: 20px;
    height: 100%;
}

.chat-container {
    flex: 1;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.side-panels {
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.panel {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.panel h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 15px 0;
}

.reasoning-panel,
.agents-panel {
    max-height: 250px;
    overflow-y: auto;
}

/* View Headers */
.view-header {
    margin-bottom: 30px;
    text-align: center;
}

.view-header h2 {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
}

.view-header p {
    font-size: 16px;
    color: #7f8c8d;
    margin: 0;
}

/* Quick Actions */
.quick-actions {
    position: fixed;
    right: 30px;
    bottom: 30px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.quick-action {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #3498db;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
}

.quick-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.quick-action img {
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
}

/* Recent Queries Sidebar */
.recent-queries {
    position: fixed;
    left: 30px;
    top: 50%;
    transform: translateY(-50%);
    width: 280px;
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
    z-index: 999;
}

.recent-queries h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 15px 0;
}

.queries-list {
    margin-bottom: 20px;
}

.query-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.2s ease;
    margin-bottom: 5px;
}

.query-item:hover {
    background: rgba(52, 152, 219, 0.05);
}

.query-item img {
    width: 16px;
    height: 16px;
    opacity: 0.6;
}

.query-text {
    flex: 1;
    font-size: 14px;
    color: #2c3e50;
}

.query-time {
    font-size: 12px;
    color: #95a5a6;
}

.favorite-topics h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 10px 0;
}

.topic-tag {
    display: inline-block;
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin: 2px;
}

/* Help Button */
.help-button {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e74c3c;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
}

.help-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.help-button img {
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .recent-queries {
        display: none;
    }
    
    .side-panels {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .user-stats {
        gap: 10px;
    }
    
    .dashboard-nav {
        padding: 0 20px;
        overflow-x: auto;
    }
    
    .nav-tab {
        padding: 12px 16px;
        white-space: nowrap;
    }
    
    .dashboard-content {
        padding: 15px 20px;
    }
    
    .chat-view {
        flex-direction: column;
    }
    
    .side-panels {
        width: 100%;
        flex-direction: row;
        overflow-x: auto;
    }
    
    .panel {
        min-width: 250px;
    }
    
    .quick-actions {
        right: 20px;
        bottom: 20px;
    }
    
    .help-button {
        bottom: 20px;
        left: 20px;
    }
}
