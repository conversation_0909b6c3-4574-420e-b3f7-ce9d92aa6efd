import React, { useState, useEffect } from 'react';
import { assets } from '../../assets/assets';

const AgentManagement = () => {
    const [agents, setAgents] = useState([]);
    const [selectedAgent, setSelectedAgent] = useState(null);
    const [agentLogs, setAgentLogs] = useState([]);
    const [filterStatus, setFilterStatus] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');

    useEffect(() => {
        loadAgents();
    }, []);

    const loadAgents = async () => {
        // Mock data - replace with actual API call
        const mockAgents = [
            {
                id: 'orchestrator-001',
                name: 'OrchestratorAgent',
                type: 'Orchestrator',
                status: 'active',
                health: 98,
                uptime: '15d 4h 23m',
                queriesProcessed: 5432,
                avgResponseTime: 0.8,
                lastActivity: new Date(Date.now() - 2 * 60 * 1000),
                capabilities: ['Task Planning', 'Agent Coordination', 'Resource Management'],
                currentLoad: 75,
                memoryUsage: 245,
                cpuUsage: 12
            },
            {
                id: 'reasoning-001',
                name: 'ReasoningAgent',
                type: 'Specialized',
                status: 'active',
                health: 95,
                uptime: '15d 4h 23m',
                queriesProcessed: 3210,
                avgResponseTime: 1.2,
                lastActivity: new Date(Date.now() - 5 * 60 * 1000),
                capabilities: ['Tree of Thoughts', 'Logic Processing', 'Problem Solving'],
                currentLoad: 68,
                memoryUsage: 189,
                cpuUsage: 18
            },
            {
                id: 'critic-001',
                name: 'CriticAgent',
                type: 'Specialized',
                status: 'active',
                health: 92,
                uptime: '15d 4h 23m',
                queriesProcessed: 2876,
                avgResponseTime: 0.6,
                lastActivity: new Date(Date.now() - 1 * 60 * 1000),
                capabilities: ['Response Verification', 'Quality Assessment', 'Error Detection'],
                currentLoad: 45,
                memoryUsage: 156,
                cpuUsage: 8
            },
            {
                id: 'nuvoai-001',
                name: 'NuvoAiAgent',
                type: 'Organization',
                status: 'active',
                health: 97,
                uptime: '15d 4h 23m',
                queriesProcessed: 1987,
                avgResponseTime: 1.1,
                lastActivity: new Date(Date.now() - 3 * 60 * 1000),
                capabilities: ['NuvoAi Knowledge', 'Policy Retrieval', 'Organization Context'],
                currentLoad: 52,
                memoryUsage: 178,
                cpuUsage: 15
            },
            {
                id: 'meril-001',
                name: 'MerilAgent',
                type: 'Organization',
                status: 'active',
                health: 94,
                uptime: '15d 4h 23m',
                queriesProcessed: 1654,
                avgResponseTime: 1.0,
                lastActivity: new Date(Date.now() - 7 * 60 * 1000),
                capabilities: ['Meril Knowledge', 'Healthcare Context', 'Compliance Checking'],
                currentLoad: 38,
                memoryUsage: 134,
                cpuUsage: 11
            },
            {
                id: 'hr-001',
                name: 'HRAgent',
                type: 'Department',
                status: 'active',
                health: 96,
                uptime: '15d 4h 23m',
                queriesProcessed: 2341,
                avgResponseTime: 0.9,
                lastActivity: new Date(Date.now() - 4 * 60 * 1000),
                capabilities: ['HR Policies', 'Leave Management', 'Employee Relations'],
                currentLoad: 61,
                memoryUsage: 167,
                cpuUsage: 13
            },
            {
                id: 'technical-001',
                name: 'TechnicalAgent',
                type: 'Department',
                status: 'idle',
                health: 89,
                uptime: '15d 4h 23m',
                queriesProcessed: 1432,
                avgResponseTime: 1.4,
                lastActivity: new Date(Date.now() - 15 * 60 * 1000),
                capabilities: ['Technical Support', 'System Troubleshooting', 'Documentation'],
                currentLoad: 23,
                memoryUsage: 98,
                cpuUsage: 5
            },
            {
                id: 'finance-001',
                name: 'FinanceAgent',
                type: 'Department',
                status: 'maintenance',
                health: 0,
                uptime: '0d 0h 0m',
                queriesProcessed: 876,
                avgResponseTime: 0,
                lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
                capabilities: ['Financial Queries', 'Budget Information', 'Expense Tracking'],
                currentLoad: 0,
                memoryUsage: 0,
                cpuUsage: 0
            }
        ];
        
        setAgents(mockAgents);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return '#2ecc71';
            case 'idle': return '#f39c12';
            case 'maintenance': return '#e74c3c';
            case 'error': return '#c0392b';
            default: return '#95a5a6';
        }
    };

    const getHealthColor = (health) => {
        if (health >= 95) return '#2ecc71';
        if (health >= 85) return '#f39c12';
        if (health >= 70) return '#e67e22';
        return '#e74c3c';
    };

    const handleAgentAction = async (agentId, action) => {
        console.log(`Performing ${action} on agent ${agentId}`);
        // Implement actual agent control logic here
        
        if (action === 'restart') {
            // Simulate restart
            setAgents(prev => prev.map(agent => 
                agent.id === agentId 
                    ? { ...agent, status: 'active', health: 100, lastActivity: new Date() }
                    : agent
            ));
        }
    };

    const filteredAgents = agents.filter(agent => {
        const matchesStatus = filterStatus === 'all' || agent.status === filterStatus;
        const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            agent.type.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesStatus && matchesSearch;
    });

    const formatUptime = (uptime) => {
        return uptime;
    };

    const formatLastActivity = (date) => {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        
        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes}m ago`;
        
        const hours = Math.floor(minutes / 60);
        if (hours < 24) return `${hours}h ago`;
        
        const days = Math.floor(hours / 24);
        return `${days}d ago`;
    };

    return (
        <div className="agent-management">
            {/* Controls */}
            <div className="management-controls">
                <div className="search-filter">
                    <input
                        type="text"
                        placeholder="Search agents..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="search-input"
                    />
                    <select
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                        className="status-filter"
                    >
                        <option value="all">All Status</option>
                        <option value="active">Active</option>
                        <option value="idle">Idle</option>
                        <option value="maintenance">Maintenance</option>
                        <option value="error">Error</option>
                    </select>
                </div>
                
                <div className="bulk-actions">
                    <button className="bulk-action-btn">Start All</button>
                    <button className="bulk-action-btn">Stop All</button>
                    <button className="bulk-action-btn">Restart All</button>
                </div>
            </div>

            {/* Agents Grid */}
            <div className="agents-grid">
                {filteredAgents.map(agent => (
                    <div 
                        key={agent.id} 
                        className={`agent-card ${selectedAgent?.id === agent.id ? 'selected' : ''}`}
                        onClick={() => setSelectedAgent(agent)}
                    >
                        <div className="agent-header">
                            <div className="agent-info">
                                <h3>{agent.name}</h3>
                                <span className="agent-type">{agent.type}</span>
                            </div>
                            <div className="agent-status">
                                <span 
                                    className="status-indicator"
                                    style={{ backgroundColor: getStatusColor(agent.status) }}
                                ></span>
                                <span className="status-text">{agent.status}</span>
                            </div>
                        </div>

                        <div className="agent-metrics">
                            <div className="metric">
                                <span className="metric-label">Health</span>
                                <div className="health-bar">
                                    <div 
                                        className="health-fill"
                                        style={{ 
                                            width: `${agent.health}%`,
                                            backgroundColor: getHealthColor(agent.health)
                                        }}
                                    ></div>
                                </div>
                                <span className="metric-value">{agent.health}%</span>
                            </div>

                            <div className="metric">
                                <span className="metric-label">Load</span>
                                <div className="load-bar">
                                    <div 
                                        className="load-fill"
                                        style={{ width: `${agent.currentLoad}%` }}
                                    ></div>
                                </div>
                                <span className="metric-value">{agent.currentLoad}%</span>
                            </div>
                        </div>

                        <div className="agent-stats">
                            <div className="stat">
                                <span className="stat-value">{agent.queriesProcessed.toLocaleString()}</span>
                                <span className="stat-label">Queries</span>
                            </div>
                            <div className="stat">
                                <span className="stat-value">{agent.avgResponseTime}s</span>
                                <span className="stat-label">Avg Time</span>
                            </div>
                            <div className="stat">
                                <span className="stat-value">{formatLastActivity(agent.lastActivity)}</span>
                                <span className="stat-label">Last Active</span>
                            </div>
                        </div>

                        <div className="agent-actions">
                            <button 
                                className="action-btn start"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleAgentAction(agent.id, 'start');
                                }}
                                disabled={agent.status === 'active'}
                            >
                                Start
                            </button>
                            <button 
                                className="action-btn stop"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleAgentAction(agent.id, 'stop');
                                }}
                                disabled={agent.status === 'maintenance'}
                            >
                                Stop
                            </button>
                            <button 
                                className="action-btn restart"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleAgentAction(agent.id, 'restart');
                                }}
                            >
                                Restart
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {/* Agent Details Panel */}
            {selectedAgent && (
                <div className="agent-details-panel">
                    <div className="panel-header">
                        <h3>{selectedAgent.name} Details</h3>
                        <button 
                            className="close-panel"
                            onClick={() => setSelectedAgent(null)}
                        >
                            ×
                        </button>
                    </div>

                    <div className="panel-content">
                        <div className="detail-section">
                            <h4>System Information</h4>
                            <div className="detail-grid">
                                <div className="detail-item">
                                    <span className="detail-label">Agent ID</span>
                                    <span className="detail-value">{selectedAgent.id}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Type</span>
                                    <span className="detail-value">{selectedAgent.type}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Uptime</span>
                                    <span className="detail-value">{selectedAgent.uptime}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Memory Usage</span>
                                    <span className="detail-value">{selectedAgent.memoryUsage} MB</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">CPU Usage</span>
                                    <span className="detail-value">{selectedAgent.cpuUsage}%</span>
                                </div>
                            </div>
                        </div>

                        <div className="detail-section">
                            <h4>Capabilities</h4>
                            <div className="capabilities-list">
                                {selectedAgent.capabilities.map((capability, index) => (
                                    <span key={index} className="capability-tag">
                                        {capability}
                                    </span>
                                ))}
                            </div>
                        </div>

                        <div className="detail-section">
                            <h4>Performance Metrics</h4>
                            <div className="performance-metrics">
                                <div className="performance-item">
                                    <span className="performance-label">Total Queries Processed</span>
                                    <span className="performance-value">{selectedAgent.queriesProcessed.toLocaleString()}</span>
                                </div>
                                <div className="performance-item">
                                    <span className="performance-label">Average Response Time</span>
                                    <span className="performance-value">{selectedAgent.avgResponseTime}s</span>
                                </div>
                                <div className="performance-item">
                                    <span className="performance-label">Current Load</span>
                                    <span className="performance-value">{selectedAgent.currentLoad}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AgentManagement;
