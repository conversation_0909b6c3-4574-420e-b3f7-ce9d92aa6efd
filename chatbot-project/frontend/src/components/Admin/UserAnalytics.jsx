import React, { useState, useEffect } from 'react';
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';

const UserAnalytics = () => {
    const [userGrowthData, setUserGrowthData] = useState([]);
    const [queryVolumeData, setQueryVolumeData] = useState([]);
    const [topicsData, setTopicsData] = useState([]);
    const [organizationData, setOrganizationData] = useState([]);
    const [userEngagementData, setUserEngagementData] = useState([]);
    const [timeRange, setTimeRange] = useState('7d');

    useEffect(() => {
        generateAnalyticsData();
    }, [timeRange]);

    const generateAnalyticsData = () => {
        // User growth data
        const growthData = [];
        const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            
            growthData.push({
                date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
                newUsers: Math.floor(Math.random() * 20) + 5,
                activeUsers: Math.floor(Math.random() * 100) + 200,
                totalUsers: 1200 + (days - i) * 15
            });
        }
        setUserGrowthData(growthData);

        // Query volume by hour
        const queryData = [];
        for (let hour = 0; hour < 24; hour++) {
            queryData.push({
                hour: `${hour.toString().padStart(2, '0')}:00`,
                queries: Math.floor(Math.random() * 50) + 10 + (hour >= 9 && hour <= 17 ? 30 : 0),
                successRate: Math.random() * 10 + 90
            });
        }
        setQueryVolumeData(queryData);

        // Popular topics
        setTopicsData([
            { topic: 'HR Policies', queries: 1234, percentage: 28 },
            { topic: 'Leave Management', queries: 987, percentage: 22 },
            { topic: 'Technical Support', queries: 756, percentage: 17 },
            { topic: 'Finance Queries', queries: 543, percentage: 12 },
            { topic: 'IT Support', queries: 432, percentage: 10 },
            { topic: 'General Info', queries: 321, percentage: 7 },
            { topic: 'Others', queries: 189, percentage: 4 }
        ]);

        // Organization distribution
        setOrganizationData([
            { name: 'NuvoAi', users: 687, queries: 3456, color: '#3498db' },
            { name: 'Meril Life Sciences', users: 423, queries: 2134, color: '#e74c3c' },
            { name: 'Meril Healthcare', users: 137, queries: 876, color: '#2ecc71' }
        ]);

        // User engagement metrics
        setUserEngagementData([
            { metric: 'Daily Active Users', value: 234, change: '+12%', trend: 'up' },
            { metric: 'Average Session Duration', value: '8.5 min', change: '+5%', trend: 'up' },
            { metric: 'Queries per User', value: 4.2, change: '-2%', trend: 'down' },
            { metric: 'User Satisfaction', value: '94%', change: '+3%', trend: 'up' },
            { metric: 'Return Rate', value: '78%', change: '+8%', trend: 'up' },
            { metric: 'Response Accuracy', value: '96%', change: '+1%', trend: 'up' }
        ]);
    };

    const COLORS = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e'];

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            return (
                <div className="analytics-tooltip">
                    <p className="tooltip-label">{label}</p>
                    {payload.map((entry, index) => (
                        <p key={index} className="tooltip-value" style={{ color: entry.color }}>
                            {`${entry.dataKey}: ${entry.value}${entry.dataKey === 'successRate' ? '%' : ''}`}
                        </p>
                    ))}
                </div>
            );
        }
        return null;
    };

    return (
        <div className="user-analytics">
            {/* Time Range Selector */}
            <div className="analytics-controls">
                <div className="time-range-selector">
                    <button 
                        className={`time-btn ${timeRange === '7d' ? 'active' : ''}`}
                        onClick={() => setTimeRange('7d')}
                    >
                        7 Days
                    </button>
                    <button 
                        className={`time-btn ${timeRange === '30d' ? 'active' : ''}`}
                        onClick={() => setTimeRange('30d')}
                    >
                        30 Days
                    </button>
                    <button 
                        className={`time-btn ${timeRange === '90d' ? 'active' : ''}`}
                        onClick={() => setTimeRange('90d')}
                    >
                        90 Days
                    </button>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="engagement-metrics">
                <h3>User Engagement Metrics</h3>
                <div className="metrics-grid">
                    {userEngagementData.map((metric, index) => (
                        <div key={index} className="engagement-card">
                            <div className="metric-header">
                                <span className="metric-name">{metric.metric}</span>
                                <span className={`metric-trend ${metric.trend}`}>
                                    {metric.trend === 'up' ? '↗' : '↘'} {metric.change}
                                </span>
                            </div>
                            <div className="metric-value">{metric.value}</div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Charts Grid */}
            <div className="analytics-charts">
                {/* User Growth */}
                <div className="chart-container">
                    <div className="chart-header">
                        <h4>User Growth Trend</h4>
                        <span className="chart-subtitle">New vs Active Users</span>
                    </div>
                    <ResponsiveContainer width="100%" height={250}>
                        <AreaChart data={userGrowthData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                            <XAxis dataKey="date" stroke="#a0a0a0" fontSize={12} />
                            <YAxis stroke="#a0a0a0" fontSize={12} />
                            <Tooltip content={<CustomTooltip />} />
                            <Area 
                                type="monotone" 
                                dataKey="activeUsers" 
                                stackId="1"
                                stroke="#3498db" 
                                fill="rgba(52, 152, 219, 0.3)"
                            />
                            <Area 
                                type="monotone" 
                                dataKey="newUsers" 
                                stackId="1"
                                stroke="#2ecc71" 
                                fill="rgba(46, 204, 113, 0.3)"
                            />
                        </AreaChart>
                    </ResponsiveContainer>
                </div>

                {/* Query Volume by Hour */}
                <div className="chart-container">
                    <div className="chart-header">
                        <h4>Query Volume by Hour</h4>
                        <span className="chart-subtitle">24-hour activity pattern</span>
                    </div>
                    <ResponsiveContainer width="100%" height={250}>
                        <LineChart data={queryVolumeData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                            <XAxis dataKey="hour" stroke="#a0a0a0" fontSize={12} />
                            <YAxis stroke="#a0a0a0" fontSize={12} />
                            <Tooltip content={<CustomTooltip />} />
                            <Line 
                                type="monotone" 
                                dataKey="queries" 
                                stroke="#e74c3c" 
                                strokeWidth={2}
                                dot={{ fill: '#e74c3c', strokeWidth: 2, r: 4 }}
                            />
                        </LineChart>
                    </ResponsiveContainer>
                </div>

                {/* Popular Topics */}
                <div className="chart-container">
                    <div className="chart-header">
                        <h4>Popular Query Topics</h4>
                        <span className="chart-subtitle">Most requested information</span>
                    </div>
                    <ResponsiveContainer width="100%" height={250}>
                        <BarChart data={topicsData} layout="horizontal">
                            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                            <XAxis type="number" stroke="#a0a0a0" fontSize={12} />
                            <YAxis 
                                type="category" 
                                dataKey="topic" 
                                stroke="#a0a0a0" 
                                fontSize={11}
                                width={100}
                            />
                            <Tooltip 
                                formatter={(value) => [value, 'Queries']}
                                labelStyle={{ color: '#2c3e50' }}
                                contentStyle={{ 
                                    backgroundColor: 'rgba(255,255,255,0.95)', 
                                    border: 'none',
                                    borderRadius: '8px'
                                }}
                            />
                            <Bar 
                                dataKey="queries" 
                                fill="#3498db"
                                radius={[0, 4, 4, 0]}
                            />
                        </BarChart>
                    </ResponsiveContainer>
                </div>

                {/* Organization Distribution */}
                <div className="chart-container">
                    <div className="chart-header">
                        <h4>Organization Distribution</h4>
                        <span className="chart-subtitle">Users by organization</span>
                    </div>
                    <ResponsiveContainer width="100%" height={250}>
                        <PieChart>
                            <Pie
                                data={organizationData}
                                cx="50%"
                                cy="50%"
                                innerRadius={50}
                                outerRadius={100}
                                paddingAngle={2}
                                dataKey="users"
                            >
                                {organizationData.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={entry.color} />
                                ))}
                            </Pie>
                            <Tooltip 
                                formatter={(value) => [value, 'Users']}
                                labelStyle={{ color: '#2c3e50' }}
                                contentStyle={{ 
                                    backgroundColor: 'rgba(255,255,255,0.95)', 
                                    border: 'none',
                                    borderRadius: '8px'
                                }}
                            />
                        </PieChart>
                    </ResponsiveContainer>
                    <div className="pie-legend">
                        {organizationData.map((entry, index) => (
                            <div key={entry.name} className="legend-item">
                                <span 
                                    className="legend-color" 
                                    style={{ backgroundColor: entry.color }}
                                ></span>
                                <span className="legend-text">{entry.name}</span>
                                <span className="legend-value">({entry.users} users)</span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Detailed Statistics */}
            <div className="detailed-stats">
                <h3>Detailed User Statistics</h3>
                <div className="stats-table">
                    <div className="table-header">
                        <div className="header-cell">Organization</div>
                        <div className="header-cell">Total Users</div>
                        <div className="header-cell">Active Users</div>
                        <div className="header-cell">Total Queries</div>
                        <div className="header-cell">Avg Queries/User</div>
                        <div className="header-cell">Satisfaction</div>
                    </div>
                    {organizationData.map((org, index) => (
                        <div key={index} className="table-row">
                            <div className="table-cell">
                                <span 
                                    className="org-indicator" 
                                    style={{ backgroundColor: org.color }}
                                ></span>
                                {org.name}
                            </div>
                            <div className="table-cell">{org.users}</div>
                            <div className="table-cell">{Math.floor(org.users * 0.7)}</div>
                            <div className="table-cell">{org.queries.toLocaleString()}</div>
                            <div className="table-cell">{(org.queries / org.users).toFixed(1)}</div>
                            <div className="table-cell">
                                <span className="satisfaction-score">
                                    {(Math.random() * 10 + 90).toFixed(1)}%
                                </span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default UserAnalytics;
