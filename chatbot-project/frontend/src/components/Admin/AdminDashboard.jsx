import React, { useState, useEffect } from 'react';
import './AdminDashboard.css';
import { assets } from '../../assets/assets';
import SystemMetrics from './SystemMetrics';
import AgentManagement from './AgentManagement';
import UserAnalytics from './UserAnalytics';
import KnowledgeManagement from './KnowledgeManagement';
import SystemLogs from './SystemLogs';
import ConfigurationPanel from './ConfigurationPanel';

const AdminDashboard = () => {
    const [activeView, setActiveView] = useState('overview');
    const [systemStats, setSystemStats] = useState({
        totalUsers: 0,
        activeAgents: 0,
        totalQueries: 0,
        systemHealth: 0,
        uptime: 0,
        responseTime: 0
    });
    const [alerts, setAlerts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        loadSystemData();
        const interval = setInterval(loadSystemData, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
    }, []);

    const loadSystemData = async () => {
        try {
            setIsLoading(true);
            // Mock data - replace with actual API calls
            setSystemStats({
                totalUsers: 1247,
                activeAgents: 10,
                totalQueries: 15678,
                systemHealth: 98.5,
                uptime: 99.9,
                responseTime: 1.2
            });
            
            setAlerts([
                { id: 1, type: 'warning', message: 'High memory usage on Agent Server 2', time: '5 min ago' },
                { id: 2, type: 'info', message: 'Knowledge base updated successfully', time: '1 hour ago' },
                { id: 3, type: 'success', message: 'All agents are operational', time: '2 hours ago' }
            ]);
        } catch (error) {
            console.error('Failed to load system data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const adminViews = [
        { id: 'overview', label: 'Overview', icon: assets.home_icon },
        { id: 'agents', label: 'Agents', icon: assets.setting_icon },
        { id: 'users', label: 'Users', icon: assets.user_icon },
        { id: 'knowledge', label: 'Knowledge', icon: assets.bulb_icon },
        { id: 'logs', label: 'Logs', icon: assets.history_icon },
        { id: 'config', label: 'Config', icon: assets.setting_icon }
    ];

    const getHealthColor = (health) => {
        if (health >= 95) return '#27ae60';
        if (health >= 85) return '#f39c12';
        return '#e74c3c';
    };

    const getAlertIcon = (type) => {
        switch (type) {
            case 'error': return '🚨';
            case 'warning': return '⚠️';
            case 'info': return 'ℹ️';
            case 'success': return '✅';
            default: return 'ℹ️';
        }
    };

    return (
        <div className="admin-dashboard">
            {/* Header */}
            <header className="admin-header">
                <div className="header-left">
                    <h1>CHaBot Admin</h1>
                    <span className="admin-role">System Administration</span>
                </div>
                <div className="header-center">
                    <div className="system-health">
                        <div className="health-indicator">
                            <span 
                                className="health-dot" 
                                style={{ backgroundColor: getHealthColor(systemStats.systemHealth) }}
                            ></span>
                            <span className="health-text">
                                System Health: {systemStats.systemHealth}%
                            </span>
                        </div>
                        <div className="uptime-indicator">
                            <span>Uptime: {systemStats.uptime}%</span>
                        </div>
                    </div>
                </div>
                <div className="header-right">
                    <div className="alerts-summary">
                        <button className="alerts-button">
                            <img src={assets.notification_icon} alt="Alerts" />
                            <span className="alert-count">{alerts.length}</span>
                        </button>
                    </div>
                    <div className="admin-profile">
                        <img src={assets.user_icon} alt="Admin" />
                        <span>Admin</span>
                    </div>
                </div>
            </header>

            {/* Navigation */}
            <nav className="admin-nav">
                {adminViews.map(view => (
                    <button
                        key={view.id}
                        className={`admin-nav-tab ${activeView === view.id ? 'active' : ''}`}
                        onClick={() => setActiveView(view.id)}
                    >
                        <img src={view.icon} alt={view.label} />
                        <span>{view.label}</span>
                    </button>
                ))}
            </nav>

            {/* Main Content */}
            <main className="admin-content">
                {activeView === 'overview' && (
                    <div className="overview-view">
                        <div className="overview-header">
                            <h2>System Overview</h2>
                            <div className="refresh-controls">
                                <button onClick={loadSystemData} disabled={isLoading}>
                                    <img src={assets.refresh_icon} alt="Refresh" />
                                    {isLoading ? 'Refreshing...' : 'Refresh'}
                                </button>
                            </div>
                        </div>

                        {/* Key Metrics Cards */}
                        <div className="metrics-grid">
                            <div className="metric-card">
                                <div className="metric-icon">
                                    <img src={assets.user_icon} alt="Users" />
                                </div>
                                <div className="metric-content">
                                    <h3>{systemStats.totalUsers.toLocaleString()}</h3>
                                    <p>Total Users</p>
                                    <span className="metric-change positive">+12% this month</span>
                                </div>
                            </div>

                            <div className="metric-card">
                                <div className="metric-icon">
                                    <img src={assets.setting_icon} alt="Agents" />
                                </div>
                                <div className="metric-content">
                                    <h3>{systemStats.activeAgents}</h3>
                                    <p>Active Agents</p>
                                    <span className="metric-change positive">All operational</span>
                                </div>
                            </div>

                            <div className="metric-card">
                                <div className="metric-icon">
                                    <img src={assets.message_icon} alt="Queries" />
                                </div>
                                <div className="metric-content">
                                    <h3>{systemStats.totalQueries.toLocaleString()}</h3>
                                    <p>Total Queries</p>
                                    <span className="metric-change positive">+8% today</span>
                                </div>
                            </div>

                            <div className="metric-card">
                                <div className="metric-icon">
                                    <img src={assets.speed_icon} alt="Response Time" />
                                </div>
                                <div className="metric-content">
                                    <h3>{systemStats.responseTime}s</h3>
                                    <p>Avg Response Time</p>
                                    <span className="metric-change positive">-5% improvement</span>
                                </div>
                            </div>
                        </div>

                        {/* System Metrics Component */}
                        <SystemMetrics />

                        {/* Recent Alerts */}
                        <div className="alerts-section">
                            <h3>Recent Alerts</h3>
                            <div className="alerts-list">
                                {alerts.map(alert => (
                                    <div key={alert.id} className={`alert-item ${alert.type}`}>
                                        <span className="alert-icon">{getAlertIcon(alert.type)}</span>
                                        <div className="alert-content">
                                            <p>{alert.message}</p>
                                            <span className="alert-time">{alert.time}</span>
                                        </div>
                                        <button className="alert-dismiss">×</button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                {activeView === 'agents' && (
                    <div className="agents-view">
                        <div className="view-header">
                            <h2>Agent Management</h2>
                            <p>Monitor and manage all system agents</p>
                        </div>
                        <AgentManagement />
                    </div>
                )}

                {activeView === 'users' && (
                    <div className="users-view">
                        <div className="view-header">
                            <h2>User Analytics</h2>
                            <p>User activity and engagement metrics</p>
                        </div>
                        <UserAnalytics />
                    </div>
                )}

                {activeView === 'knowledge' && (
                    <div className="knowledge-view">
                        <div className="view-header">
                            <h2>Knowledge Management</h2>
                            <p>Manage knowledge base and data sources</p>
                        </div>
                        <KnowledgeManagement />
                    </div>
                )}

                {activeView === 'logs' && (
                    <div className="logs-view">
                        <div className="view-header">
                            <h2>System Logs</h2>
                            <p>Monitor system activity and troubleshoot issues</p>
                        </div>
                        <SystemLogs />
                    </div>
                )}

                {activeView === 'config' && (
                    <div className="config-view">
                        <div className="view-header">
                            <h2>System Configuration</h2>
                            <p>Configure system settings and parameters</p>
                        </div>
                        <ConfigurationPanel />
                    </div>
                )}
            </main>

            {/* Quick Actions */}
            <div className="admin-quick-actions">
                <button className="quick-action emergency" title="Emergency Stop">
                    <img src={assets.stop_icon} alt="Stop" />
                </button>
                <button className="quick-action restart" title="Restart System">
                    <img src={assets.refresh_icon} alt="Restart" />
                </button>
                <button className="quick-action backup" title="Backup System">
                    <img src={assets.save_icon} alt="Backup" />
                </button>
            </div>

            {/* Status Bar */}
            <footer className="admin-status-bar">
                <div className="status-left">
                    <span>Last updated: {new Date().toLocaleTimeString()}</span>
                </div>
                <div className="status-center">
                    <span>CHaBot Admin v2.0.0</span>
                </div>
                <div className="status-right">
                    <span>Server: Online</span>
                    <span>DB: Connected</span>
                    <span>Agents: {systemStats.activeAgents}/10</span>
                </div>
            </footer>
        </div>
    );
};

export default AdminDashboard;
