.admin-dashboard {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    font-family: 'Outfit', sans-serif;
    color: #ffffff;
}

/* Header */
.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.header-left h1 {
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.admin-role {
    font-size: 14px;
    color: #a0a0a0;
    font-weight: 400;
}

.header-center {
    display: flex;
    align-items: center;
    gap: 20px;
}

.system-health {
    display: flex;
    align-items: center;
    gap: 20px;
}

.health-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.health-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.health-text {
    font-size: 14px;
    font-weight: 500;
}

.uptime-indicator {
    font-size: 14px;
    color: #a0a0a0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.alerts-button {
    position: relative;
    background: rgba(231, 76, 60, 0.2);
    border: 1px solid rgba(231, 76, 60, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e74c3c;
    transition: all 0.3s ease;
}

.alerts-button:hover {
    background: rgba(231, 76, 60, 0.3);
}

.alert-count {
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
}

.admin-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
}

.admin-profile img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

/* Navigation */
.admin-nav {
    display: flex;
    padding: 0 30px;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #a0a0a0;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.admin-nav-tab:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
}

.admin-nav-tab.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.admin-nav-tab img {
    width: 16px;
    height: 16px;
    opacity: 0.7;
    filter: brightness(0) invert(1);
}

.admin-nav-tab.active img {
    opacity: 1;
    filter: brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(180deg);
}

/* Main Content */
.admin-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.overview-header h2 {
    font-size: 28px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.refresh-controls button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(52, 152, 219, 0.2);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 8px;
    padding: 10px 16px;
    color: #3498db;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-controls button:hover:not(:disabled) {
    background: rgba(52, 152, 219, 0.3);
}

.refresh-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
}

.metric-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.metric-icon {
    width: 48px;
    height: 48px;
    background: rgba(52, 152, 219, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.metric-icon img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(180deg);
}

.metric-content h3 {
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 4px 0;
}

.metric-content p {
    font-size: 14px;
    color: #a0a0a0;
    margin: 0 0 8px 0;
}

.metric-change {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
}

.metric-change.positive {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

.metric-change.negative {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

/* Alerts Section */
.alerts-section {
    margin-top: 30px;
}

.alerts-section h3 {
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 20px 0;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.alert-item.error {
    border-left: 4px solid #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.alert-item.warning {
    border-left: 4px solid #f39c12;
    background: rgba(243, 156, 18, 0.1);
}

.alert-item.info {
    border-left: 4px solid #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.alert-item.success {
    border-left: 4px solid #2ecc71;
    background: rgba(46, 204, 113, 0.1);
}

.alert-icon {
    font-size: 20px;
}

.alert-content {
    flex: 1;
}

.alert-content p {
    font-size: 14px;
    color: #ffffff;
    margin: 0 0 4px 0;
}

.alert-time {
    font-size: 12px;
    color: #a0a0a0;
}

.alert-dismiss {
    background: none;
    border: none;
    color: #a0a0a0;
    cursor: pointer;
    font-size: 18px;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.alert-dismiss:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* View Headers */
.view-header {
    margin-bottom: 30px;
    text-align: center;
}

.view-header h2 {
    font-size: 28px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 8px 0;
}

.view-header p {
    font-size: 16px;
    color: #a0a0a0;
    margin: 0;
}

/* Quick Actions */
.admin-quick-actions {
    position: fixed;
    right: 30px;
    bottom: 80px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 1000;
}

.quick-action {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.quick-action.emergency {
    background: #e74c3c;
}

.quick-action.restart {
    background: #f39c12;
}

.quick-action.backup {
    background: #2ecc71;
}

.quick-action:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

.quick-action img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
}

/* Status Bar */
.admin-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 30px;
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 12px;
    color: #a0a0a0;
}

.status-right {
    display: flex;
    gap: 20px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .admin-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .header-center {
        order: -1;
    }
    
    .admin-nav {
        padding: 0 20px;
        overflow-x: auto;
    }
    
    .admin-nav-tab {
        padding: 12px 16px;
        white-space: nowrap;
    }
    
    .admin-content {
        padding: 20px;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .admin-quick-actions {
        right: 20px;
        bottom: 70px;
    }
    
    .admin-status-bar {
        padding: 10px 20px;
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
    
    .status-right {
        gap: 15px;
    }
}

/* System Metrics Specific Styles */
.system-metrics {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 30px;
}

.system-metrics h3 {
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 20px 0;
}

.metrics-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.chart-container {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: 16px;
}

.chart-title {
    font-size: 14px;
    font-weight: 500;
    color: #a0a0a0;
    margin-bottom: 12px;
}

@media (max-width: 768px) {
    .metrics-charts {
        grid-template-columns: 1fr;
    }
}

/* Custom Tooltip Styles */
.custom-tooltip {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.tooltip-label {
    color: #2c3e50;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.tooltip-value {
    margin: 4px 0;
    font-size: 14px;
}

/* Pie Chart Legend */
.pie-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 12px;
    justify-content: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.legend-text {
    font-size: 12px;
    color: #a0a0a0;
}

/* Resource Meters */
.resource-meters {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px 0;
}

.resource-meter {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.resource-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.resource-name {
    font-size: 14px;
    color: #ffffff;
    font-weight: 500;
}

.resource-value {
    font-size: 14px;
    color: #a0a0a0;
    font-weight: 600;
}

.resource-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.resource-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Real-time Stats */
.realtime-stats {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-group h4 {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 16px 0;
}

.stat-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
}

.stat-items .stat-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.stat-items .stat-label {
    font-size: 12px;
    color: #a0a0a0;
}

.stat-items .stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #3498db;
}
