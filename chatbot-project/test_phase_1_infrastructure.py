"""Phase 1: Core Infrastructure Testing - According to Implementation Roadmap."""

import asyncio
import sys
import os
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(__file__))

class Phase1InfrastructureTester:
    """Test Phase 1: Core Infrastructure components"""
    
    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.phase_score = 0.0
    
    async def run_phase_1_tests(self):
        """Run all Phase 1 infrastructure tests"""
        print("🏗️  PHASE 1: CORE INFRASTRUCTURE TESTING")
        print("="*70)
        print("Testing according to Implementation Roadmap Phase 1")
        print("="*70)
        
        # Phase 1.1: Environment Setup
        await self.test_environment_setup()
        
        # Phase 1.2: Kubernetes Cluster (Mock/Check)
        await self.test_kubernetes_readiness()
        
        # Phase 1.3: Database Deployment
        await self.test_database_deployment()
        
        # Phase 1.4: Vector Database & Monitoring
        await self.test_vector_database_monitoring()
        
        # Phase 1.5: Agent Communication Infrastructure
        await self.test_agent_communication_infrastructure()
        
        # Generate Phase 1 report
        await self.generate_phase_1_report()
    
    async def test_environment_setup(self):
        """Test Phase 1.1: Environment Setup"""
        print("\n🔧 Phase 1.1: Environment Setup")
        print("-" * 40)
        
        # Test 1: Development environment structure
        await self.test_dev_environment_structure()
        
        # Test 2: Docker configuration
        await self.test_docker_configuration()
        
        # Test 3: Project structure and standards
        await self.test_project_structure()
        
        # Test 4: Agent development environment
        await self.test_agent_development_environment()
    
    async def test_dev_environment_structure(self):
        """Test development environment structure"""
        try:
            required_files = [
                "requirements.txt",
                "docker-compose.yml", 
                "Dockerfile",
                "pyproject.toml",
                "setup.py"
            ]
            
            missing_files = []
            for file in required_files:
                if not os.path.exists(file):
                    missing_files.append(file)
            
            if not missing_files:
                self.record_test("Environment Structure", True, "All required files present")
                print("✅ Development environment structure: PASSED")
            else:
                self.record_test("Environment Structure", False, f"Missing files: {missing_files}")
                print(f"❌ Development environment structure: FAILED - Missing: {missing_files}")
                
        except Exception as e:
            self.record_test("Environment Structure", False, str(e))
            print(f"❌ Development environment structure: ERROR - {str(e)}")
    
    async def test_docker_configuration(self):
        """Test Docker configuration"""
        try:
            # Check if Docker files exist
            docker_files = ["Dockerfile", "docker-compose.yml"]
            docker_present = all(os.path.exists(f) for f in docker_files)
            
            if docker_present:
                # Try to validate docker-compose syntax
                try:
                    result = subprocess.run(
                        ["docker-compose", "config"], 
                        capture_output=True, 
                        text=True, 
                        timeout=10
                    )
                    if result.returncode == 0:
                        self.record_test("Docker Configuration", True, "Docker compose configuration valid")
                        print("✅ Docker configuration: PASSED")
                    else:
                        self.record_test("Docker Configuration", False, f"Docker compose error: {result.stderr}")
                        print(f"❌ Docker configuration: FAILED - {result.stderr}")
                except subprocess.TimeoutExpired:
                    self.record_test("Docker Configuration", True, "Docker files present (timeout on validation)")
                    print("✅ Docker configuration: PASSED (files present)")
                except FileNotFoundError:
                    self.record_test("Docker Configuration", True, "Docker files present (docker-compose not installed)")
                    print("✅ Docker configuration: PASSED (files present)")
            else:
                self.record_test("Docker Configuration", False, "Docker files missing")
                print("❌ Docker configuration: FAILED - Files missing")
                
        except Exception as e:
            self.record_test("Docker Configuration", False, str(e))
            print(f"❌ Docker configuration: ERROR - {str(e)}")
    
    async def test_project_structure(self):
        """Test project structure and coding standards"""
        try:
            required_dirs = [
                "agents",
                "ai", 
                "backend",
                "frontend",
                "infrastructure",
                "deployment",
                "monitoring",
                "testing"
            ]
            
            present_dirs = []
            missing_dirs = []
            
            for dir_name in required_dirs:
                if os.path.exists(dir_name):
                    present_dirs.append(dir_name)
                else:
                    missing_dirs.append(dir_name)
            
            completion_rate = len(present_dirs) / len(required_dirs)
            
            if completion_rate >= 0.8:  # 80% of directories present
                self.record_test("Project Structure", True, f"Structure {completion_rate:.1%} complete")
                print(f"✅ Project structure: PASSED ({completion_rate:.1%} complete)")
            else:
                self.record_test("Project Structure", False, f"Only {completion_rate:.1%} complete, missing: {missing_dirs}")
                print(f"❌ Project structure: FAILED - Only {completion_rate:.1%} complete")
                
        except Exception as e:
            self.record_test("Project Structure", False, str(e))
            print(f"❌ Project structure: ERROR - {str(e)}")
    
    async def test_agent_development_environment(self):
        """Test agent development environment"""
        try:
            agent_components = [
                "agents/core",
                "agents/orchestrator", 
                "agents/specialized",
                "agents/communication"
            ]
            
            present_components = []
            for component in agent_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            completion_rate = len(present_components) / len(agent_components)
            
            if completion_rate >= 0.75:
                self.record_test("Agent Development Environment", True, f"Agent structure {completion_rate:.1%} complete")
                print(f"✅ Agent development environment: PASSED ({completion_rate:.1%} complete)")
            else:
                self.record_test("Agent Development Environment", False, f"Agent structure only {completion_rate:.1%} complete")
                print(f"❌ Agent development environment: FAILED - Only {completion_rate:.1%} complete")
                
        except Exception as e:
            self.record_test("Agent Development Environment", False, str(e))
            print(f"❌ Agent development environment: ERROR - {str(e)}")
    
    async def test_kubernetes_readiness(self):
        """Test Phase 1.2: Kubernetes Cluster readiness"""
        print("\n☸️  Phase 1.2: Kubernetes Cluster Readiness")
        print("-" * 40)
        
        try:
            # Check for Kubernetes configuration files
            k8s_files = [
                "deployment/kubernetes",
                "infrastructure/k8s",
                "deployment"
            ]
            
            k8s_present = any(os.path.exists(path) for path in k8s_files)
            
            if k8s_present:
                # Check for specific k8s manifests
                manifest_patterns = ["deployment", "service", "configmap", "namespace"]
                found_manifests = []
                
                for root, dirs, files in os.walk("deployment"):
                    for file in files:
                        if file.endswith(('.yaml', '.yml')):
                            for pattern in manifest_patterns:
                                if pattern in file.lower():
                                    found_manifests.append(pattern)
                
                if found_manifests:
                    self.record_test("Kubernetes Configuration", True, f"K8s manifests found: {set(found_manifests)}")
                    print(f"✅ Kubernetes configuration: PASSED - Manifests: {set(found_manifests)}")
                else:
                    self.record_test("Kubernetes Configuration", True, "K8s directory structure present")
                    print("✅ Kubernetes configuration: PASSED - Structure present")
            else:
                self.record_test("Kubernetes Configuration", False, "No Kubernetes configuration found")
                print("❌ Kubernetes configuration: FAILED - No K8s files found")
                
        except Exception as e:
            self.record_test("Kubernetes Configuration", False, str(e))
            print(f"❌ Kubernetes configuration: ERROR - {str(e)}")
    
    async def test_database_deployment(self):
        """Test Phase 1.3: Database Deployment"""
        print("\n🗄️  Phase 1.3: Database Deployment")
        print("-" * 40)
        
        # Test database configuration files
        await self.test_database_configs()
        
        # Test database connection setup
        await self.test_database_connections()
    
    async def test_database_configs(self):
        """Test database configuration files"""
        try:
            database_configs = {
                "PostgreSQL": ["database/docker-compose.yml", "config.py"],
                "Neo4j": ["database/docker-compose.yml", "ai/knowledge/graph_search"],
                "Milvus": ["milvus-standalone/docker-compose.yml", "ai/knowledge/vector_search"],
                "Memgraph": ["database", "ai/reasoning"]
            }
            
            configured_dbs = []
            missing_dbs = []
            
            for db_name, config_paths in database_configs.items():
                db_configured = any(os.path.exists(path) for path in config_paths)
                if db_configured:
                    configured_dbs.append(db_name)
                else:
                    missing_dbs.append(db_name)
            
            completion_rate = len(configured_dbs) / len(database_configs)
            
            if completion_rate >= 0.5:  # At least 50% of databases configured
                self.record_test("Database Configuration", True, f"Databases configured: {configured_dbs}")
                print(f"✅ Database configuration: PASSED - {configured_dbs}")
            else:
                self.record_test("Database Configuration", False, f"Missing databases: {missing_dbs}")
                print(f"❌ Database configuration: FAILED - Missing: {missing_dbs}")
                
        except Exception as e:
            self.record_test("Database Configuration", False, str(e))
            print(f"❌ Database configuration: ERROR - {str(e)}")
    
    async def test_database_connections(self):
        """Test database connection setup"""
        try:
            # Check for database connection modules
            connection_modules = [
                "backend/database",
                "ai/knowledge/vector_search",
                "ai/knowledge/graph_search",
                "config.py"
            ]
            
            present_modules = []
            for module in connection_modules:
                if os.path.exists(module):
                    present_modules.append(module)
            
            if present_modules:
                self.record_test("Database Connections", True, f"Connection modules: {present_modules}")
                print(f"✅ Database connections: PASSED - Modules: {len(present_modules)}")
            else:
                self.record_test("Database Connections", False, "No database connection modules found")
                print("❌ Database connections: FAILED - No connection modules")
                
        except Exception as e:
            self.record_test("Database Connections", False, str(e))
            print(f"❌ Database connections: ERROR - {str(e)}")
    
    async def test_vector_database_monitoring(self):
        """Test Phase 1.4: Vector Database & Monitoring"""
        print("\n📊 Phase 1.4: Vector Database & Monitoring")
        print("-" * 40)
        
        # Test vector database setup
        await self.test_vector_database_setup()
        
        # Test monitoring infrastructure
        await self.test_monitoring_setup()
    
    async def test_vector_database_setup(self):
        """Test vector database setup"""
        try:
            vector_components = [
                "ai/knowledge/vector_search",
                "milvus-standalone",
                "ai/knowledge/fusion"
            ]
            
            present_components = []
            for component in vector_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            completion_rate = len(present_components) / len(vector_components)
            
            if completion_rate >= 0.67:  # At least 2/3 components present
                self.record_test("Vector Database Setup", True, f"Vector components: {present_components}")
                print(f"✅ Vector database setup: PASSED - {len(present_components)}/{len(vector_components)} components")
            else:
                self.record_test("Vector Database Setup", False, f"Only {len(present_components)}/{len(vector_components)} components present")
                print(f"❌ Vector database setup: FAILED - Incomplete setup")
                
        except Exception as e:
            self.record_test("Vector Database Setup", False, str(e))
            print(f"❌ Vector database setup: ERROR - {str(e)}")
    
    async def test_monitoring_setup(self):
        """Test monitoring infrastructure setup"""
        try:
            monitoring_components = [
                "monitoring",
                "infrastructure/monitoring",
                "deployment/monitoring"
            ]
            
            monitoring_present = any(os.path.exists(path) for path in monitoring_components)
            
            if monitoring_present:
                self.record_test("Monitoring Setup", True, "Monitoring infrastructure present")
                print("✅ Monitoring setup: PASSED")
            else:
                self.record_test("Monitoring Setup", False, "No monitoring infrastructure found")
                print("❌ Monitoring setup: FAILED - No monitoring components")
                
        except Exception as e:
            self.record_test("Monitoring Setup", False, str(e))
            print(f"❌ Monitoring setup: ERROR - {str(e)}")
    
    async def test_agent_communication_infrastructure(self):
        """Test Phase 1.5: Agent Communication Infrastructure"""
        print("\n📡 Phase 1.5: Agent Communication Infrastructure")
        print("-" * 40)
        
        # Test communication framework
        await self.test_communication_framework()
        
        # Test message protocols
        await self.test_message_protocols()
    
    async def test_communication_framework(self):
        """Test agent communication framework"""
        try:
            comm_components = [
                "agents/communication",
                "agents/core/communication.py",
                "backend/communication"
            ]
            
            present_components = []
            for component in comm_components:
                if os.path.exists(component):
                    present_components.append(component)
            
            if present_components:
                self.record_test("Communication Framework", True, f"Communication components: {present_components}")
                print(f"✅ Communication framework: PASSED - {len(present_components)} components")
            else:
                self.record_test("Communication Framework", False, "No communication components found")
                print("❌ Communication framework: FAILED - No components")
                
        except Exception as e:
            self.record_test("Communication Framework", False, str(e))
            print(f"❌ Communication framework: ERROR - {str(e)}")
    
    async def test_message_protocols(self):
        """Test message protocols"""
        try:
            protocol_files = [
                "agents/communication/message_protocols.py",
                "agents/communication/agent_coordinator.py",
                "backend/protocols"
            ]
            
            present_protocols = []
            for protocol in protocol_files:
                if os.path.exists(protocol):
                    present_protocols.append(protocol)
            
            if present_protocols:
                self.record_test("Message Protocols", True, f"Protocol files: {present_protocols}")
                print(f"✅ Message protocols: PASSED - {len(present_protocols)} protocol files")
            else:
                self.record_test("Message Protocols", False, "No message protocol files found")
                print("❌ Message protocols: FAILED - No protocol files")
                
        except Exception as e:
            self.record_test("Message Protocols", False, str(e))
            print(f"❌ Message protocols: ERROR - {str(e)}")
    
    def record_test(self, test_name: str, passed: bool, details: str):
        """Record test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
        
        self.test_results.append({
            "test_name": test_name,
            "passed": passed,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })
    
    async def generate_phase_1_report(self):
        """Generate Phase 1 test report"""
        self.phase_score = (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
        
        print("\n" + "="*70)
        print("📊 PHASE 1: CORE INFRASTRUCTURE - TEST REPORT")
        print("="*70)
        
        print(f"📈 Phase 1 Success Rate: {self.phase_score:.1f}% ({self.passed_tests}/{self.total_tests})")
        print(f"✅ Passed Tests: {self.passed_tests}")
        print(f"❌ Failed Tests: {self.total_tests - self.passed_tests}")
        
        print("\n📋 Detailed Results:")
        print("-" * 70)
        
        for result in self.test_results:
            status = "✅ PASS" if result["passed"] else "❌ FAIL"
            print(f"{status} | {result['test_name']:<30} | {result['details']}")
        
        # Phase 1 Assessment
        print(f"\n🎯 Phase 1 Assessment:")
        if self.phase_score >= 80:
            print("🎉 PHASE 1: EXCELLENT - Infrastructure foundation is solid")
        elif self.phase_score >= 60:
            print("👍 PHASE 1: GOOD - Infrastructure mostly ready, minor improvements needed")
        elif self.phase_score >= 40:
            print("⚠️  PHASE 1: NEEDS IMPROVEMENT - Several infrastructure components missing")
        else:
            print("🚨 PHASE 1: CRITICAL - Major infrastructure components missing")
        
        # Save detailed report
        report_data = {
            "phase": "Phase 1: Core Infrastructure",
            "success_rate": self.phase_score,
            "total_tests": self.total_tests,
            "passed_tests": self.passed_tests,
            "detailed_results": self.test_results,
            "timestamp": datetime.now().isoformat()
        }
        
        with open("phase_1_infrastructure_report.json", "w") as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n💾 Detailed Phase 1 report saved to: phase_1_infrastructure_report.json")
        print("="*70)

async def main():
    """Run Phase 1 infrastructure tests"""
    tester = Phase1InfrastructureTester()
    await tester.run_phase_1_tests()

if __name__ == "__main__":
    asyncio.run(main())
